import 'package:flutter_test/flutter_test.dart';
import 'package:chat_app/utils/helper.dart';

void main() {
  group('YouTube Link Detection Tests', () {
    test('should detect various YouTube URL formats', () {
      // Test different YouTube URL formats
      expect(Helper.isYouTubeVideoLink('https://www.youtube.com/watch?v=dQw4w9WgXcQ'), true);
      expect(Helper.isYouTubeVideoLink('https://youtu.be/dQw4w9WgXcQ'), true);
      expect(Helper.isYouTubeVideoLink('https://m.youtube.com/watch?v=dQw4w9WgXcQ'), true);
      expect(Helper.isYouTubeVideoLink('https://youtube.com/watch?v=dQw4w9WgXcQ'), true);
      
      // Test invalid URLs
      expect(Helper.isYouTubeVideoLink('https://google.com'), false);
      expect(Helper.isYouTubeVideoLink('not a url'), false);
      expect(Helper.isYouTubeVideoLink(''), false);
    });

    test('should extract YouTube video IDs correctly', () {
      expect(Helper.getYouTubeVideoId('https://www.youtube.com/watch?v=dQw4w9WgXcQ'), 'dQw4w9WgXcQ');
      expect(Helper.getYouTubeVideoId('https://youtu.be/dQw4w9WgXcQ'), 'dQw4w9WgXcQ');
      expect(Helper.getYouTubeVideoId('https://m.youtube.com/watch?v=dQw4w9WgXcQ'), 'dQw4w9WgXcQ');
      
      // Test invalid URLs
      expect(Helper.getYouTubeVideoId('https://google.com'), null);
      expect(Helper.getYouTubeVideoId('not a url'), null);
    });

    test('should generate YouTube thumbnail URLs', () {
      const videoId = 'dQw4w9WgXcQ';
      const url = 'https://www.youtube.com/watch?v=$videoId';
      
      final thumbnail = Helper.getYouTubeThumbnail(url);
      expect(thumbnail, 'https://img.youtube.com/vi/$videoId/maxresdefault.jpg');
      
      final mediumThumbnail = Helper.getYouTubeThumbnailMedium(url);
      expect(mediumThumbnail, 'https://img.youtube.com/vi/$videoId/mqdefault.jpg');
    });
  });

  group('Google Drive Link Detection Tests', () {
    test('should detect Google Drive document links', () {
      expect(Helper.isGoogleDriveDocumentLink('https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/view'), true);
      expect(Helper.isGoogleDriveDocumentLink('https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit'), true);
      
      // Test invalid URLs
      expect(Helper.isGoogleDriveDocumentLink('https://google.com'), false);
      expect(Helper.isGoogleDriveDocumentLink('not a url'), false);
    });

    test('should generate Google Drive document thumbnail URLs', () {
      const fileId = '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms';
      const url = 'https://drive.google.com/file/d/$fileId/view';
      
      final thumbnail = Helper.getGoogleDriveDocumentThumbnail(url);
      expect(thumbnail, 'https://drive.google.com/thumbnail?id=$fileId&sz=w400-h300');
    });
  });

  group('URL Extraction Tests', () {
    test('should extract URLs from text using regex', () {
      const text = 'Check out this video: https://www.youtube.com/watch?v=dQw4w9WgXcQ and this document: https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/view';
      
      final urlRegex = RegExp(r'https?://[^\s]+');
      final matches = urlRegex.allMatches(text);
      
      expect(matches.length, 2);
      
      final urls = matches.map((match) => match.group(0)).toList();
      expect(urls[0], 'https://www.youtube.com/watch?v=dQw4w9WgXcQ');
      expect(urls[1], 'https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/view');
      
      // Test that we can detect YouTube and Google Drive links
      expect(Helper.isYouTubeVideoLink(urls[0]!), true);
      expect(Helper.isGoogleDriveDocumentLink(urls[1]!), true);
    });
  });
}
