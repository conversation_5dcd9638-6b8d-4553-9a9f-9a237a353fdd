import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/foundation.dart';
import 'package:chat_app/utils/platform_helper.dart';
import 'package:chat_app/utils/image_helper.dart';

void main() {
  group('Platform Compatibility Tests', () {
    test('PlatformHelper should work on mobile platforms', () async {
      // Mock mobile platform (kIsWeb = false)
      // Note: In real tests, you'd need to mock kIsWeb
      
      // Test that methods don't throw errors
      expect(() => PlatformHelper.isFileOperationSupported, returnsNormally);
      expect(() => PlatformHelper.isUrl('https://example.com'), returnsNormally);
    });

    test('ImageHelper should return original URL on mobile', () {
      const testUrl = 'https://firebasestorage.googleapis.com/test.jpg';
      
      // On mobile (when kIsWeb is false), should return original URL
      // Note: This test assumes kIsWeb is false in test environment
      final result = ImageHelper.getWebCompatibleImageUrl(testUrl);
      
      // Should return original URL or modified URL (both are acceptable)
      expect(result, isNotEmpty);
      expect(result, contains('firebasestorage.googleapis.com'));
    });

    test('ImageHelper utility methods work correctly', () {
      const firebaseUrl = 'https://firebasestorage.googleapis.com/test.jpg';
      const regularUrl = 'https://example.com/image.jpg';
      
      expect(ImageHelper.isFirebaseStorageUrl(firebaseUrl), isTrue);
      expect(ImageHelper.isFirebaseStorageUrl(regularUrl), isFalse);
      
      final urlWithToken = ImageHelper.getFirebaseStorageUrlWithToken(firebaseUrl);
      expect(urlWithToken, contains('alt=media'));
    });

    test('PlatformHelper URL validation works', () {
      expect(PlatformHelper.isUrl('https://example.com'), isTrue);
      expect(PlatformHelper.isUrl('http://example.com'), isTrue);
      expect(PlatformHelper.isUrl('ftp://example.com'), isTrue);
      expect(PlatformHelper.isUrl('not-a-url'), isFalse);
      expect(PlatformHelper.isUrl(''), isFalse);
    });
  });
}
