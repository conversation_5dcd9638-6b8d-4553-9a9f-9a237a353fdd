# App Update Notification System

This guide explains how to use the app update notification system for Android/iOS platforms.

## 🚀 Features

- **Automatic Update Checking**: Checks for updates when users log in
- **Optional Updates**: Users can choose to update later
- **Force Updates**: Mandatory updates that users must install
- **Platform Specific**: Different versions for Android, iOS, and Web
- **Custom Messages**: Personalized update messages
- **Store Integration**: Direct links to Play Store/App Store

## 📱 How It Works

### 1. Version Storage
App version information is stored in Firestore under the `app_versions` collection:
```
app_versions/
├── android/
│   ├── version: "1.0.21"
│   ├── build_number: "171"
│   ├── message: "New features available!"
│   ├── force_update: false
│   ├── download_url: "https://play.google.com/store/..."
│   └── updated_at: timestamp
├── ios/
│   └── (same structure)
└── web/
    └── (same structure)
```

### 2. Update Check Process
1. App starts and user logs in successfully
2. System gets current app version from device
3. Fetches latest version info from Firestore
4. Compares versions and shows update dialog if newer version available
5. User can update now or later (unless force update)

### 3. Update Dialog Types

#### Optional Update
- Shows current vs latest version
- "Later" and "Update" buttons
- User can dismiss and continue using app

#### Force Update
- Shows warning message
- Only "Update Now" button
- User cannot dismiss dialog
- App unusable until updated

## 🛠️ Implementation

### Files Created
1. `lib/services/app_version_service.dart` - Core update checking logic
2. `lib/utils/app_version_admin.dart` - Admin tools for setting versions
3. `lib/screens/admin/app_version_test_screen.dart` - Testing interface
4. Updated `lib/utils/firestore_service.dart` - Added update methods
5. Updated `lib/screens/auth/splash_screen.dart` - Added update check

### Integration Points
- **Splash Screen**: Checks for updates after successful login
- **Firestore Service**: Manages version data in database
- **Navigation**: Uses global navigator for showing dialogs

## 📋 Usage Instructions

### For Developers/Admins

#### 1. Initial Setup
```dart
import 'package:chat_app/utils/app_version_admin.dart';

// Run once to setup initial versions
await AppVersionAdmin.setupCurrentVersions();
```

#### 2. Release New Version
```dart
// For optional update
await AppVersionAdmin.setAndroidVersion(
  version: '1.0.21',
  buildNumber: '171',
  message: 'New chat features and bug fixes!',
  forceUpdate: false,
  playStoreUrl: 'https://play.google.com/store/apps/details?id=your.app.id',
);

// For force update
await AppVersionAdmin.setAndroidVersion(
  version: '1.0.22',
  buildNumber: '172',
  message: 'Critical security update required!',
  forceUpdate: true,
);
```

#### 3. Testing
Use the test screen to verify functionality:
```dart
// Navigate to test screen
Navigator.push(context, MaterialPageRoute(
  builder: (context) => AppVersionTestScreen(),
));
```

### For Users
1. **Optional Update**: Users see update notification and can choose to update or continue
2. **Force Update**: Users must update to continue using the app
3. **No Update**: Users continue normally if app is up to date

## 🔧 Configuration

### Store URLs
The correct store URLs are configured in `app_version_admin.dart`:
```dart
// Android Play Store
playStoreUrl: 'https://play.google.com/store/apps/details?id=com.surena.schoolsmessanger.teacher'

// iOS App Store
appStoreUrl: 'https://apps.apple.com/us/app/schoolmessenger-teacher/id6744751476'
```

### Update Messages
Customize messages for different update types:
```dart
// Feature update
message: 'New messaging features and performance improvements!'

// Bug fix update
message: 'Important bug fixes and stability improvements.'

// Security update
message: 'Critical security update. Please update immediately.'
```

### Version Comparison
The system compares versions using semantic versioning:
- `1.0.21` > `1.0.20` (patch update)
- `1.1.0` > `1.0.21` (minor update)  
- `2.0.0` > `1.1.0` (major update)

Build numbers are also compared: `172` > `171`

## 🧪 Testing

### Test Scenarios
1. **No Update Available**: Current version matches latest
2. **Optional Update**: Newer version available, force_update = false
3. **Force Update**: Newer version available, force_update = true
4. **Network Error**: Handle Firestore connection issues gracefully

### Test Steps
1. Open `AppVersionTestScreen`
2. Run "Setup Initial Versions"
3. Test "Check for Updates" (should show no updates)
4. Simulate updates and test dialog behavior
5. Verify store links work correctly

## 📱 Platform Considerations

### Android
- Uses `package_info_plus` to get current version
- Opens Play Store for updates
- Supports APK direct download if URL provided

### iOS
- Uses `package_info_plus` to get current version  
- Opens App Store for updates
- Follows Apple's update guidelines

### Web
- Returns hardcoded version (web apps auto-update)
- Can show refresh notification
- No store integration needed

## 🔒 Security

### Best Practices
1. **Validate URLs**: Ensure download URLs are legitimate
2. **Secure Firestore**: Use proper security rules
3. **Version Verification**: Verify version strings are valid
4. **Error Handling**: Handle network/parsing errors gracefully

### Firestore Security Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /app_versions/{platform} {
      allow read: if true; // Anyone can read version info
      allow write: if request.auth != null && 
                      request.auth.token.admin == true; // Only admins can write
    }
  }
}
```

## 🚀 Deployment Workflow

### Release Process
1. **Build New Version**: Update version in `pubspec.yaml`
2. **Test Locally**: Use test screen to verify functionality
3. **Update Firestore**: Set new version info using admin tools
4. **Deploy App**: Release to stores
5. **Monitor**: Check update adoption rates

### Rollback Process
If issues are found:
1. **Reset Version**: Use admin tools to reset to previous version
2. **Fix Issues**: Address problems in code
3. **Re-release**: Deploy fixed version

## 📊 Monitoring

### Analytics
Track update adoption:
- Update dialog shown count
- Update accepted/declined rates
- Force update compliance
- Platform-specific adoption

### Logs
Monitor for:
- Version check failures
- Dialog display issues
- Store link problems
- Network connectivity issues

## 🆘 Troubleshooting

### Common Issues
1. **Dialog Not Showing**: Check network connection and Firestore rules
2. **Wrong Version**: Verify version format and comparison logic
3. **Store Link Fails**: Check URL format and app store IDs
4. **Force Update Loop**: Ensure version numbers are correct

### Debug Steps
1. Check device logs for error messages
2. Verify Firestore data structure
3. Test with different version combinations
4. Use test screen for isolated testing

## 📝 Notes

- Update checks only run on mobile platforms (not web)
- Dialogs appear 2 seconds after successful login
- Force updates prevent app usage until updated
- Version comparison supports semantic versioning
- Store URLs can be customized per release
