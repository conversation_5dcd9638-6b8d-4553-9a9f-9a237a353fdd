import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/repositories/education_repositories.dart';
import 'package:chat_app/screens/attendance/attendance.dart';
import 'package:chat_app/screens/chat/search/search_parent.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/widgets/custom_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ManageAttendance extends StatefulWidget {
  const ManageAttendance({super.key});

  @override
  State<ManageAttendance> createState() => _ManageAttendanceState();
}

class _ManageAttendanceState extends State<ManageAttendance> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(builder: (context, state) {
      var users = state.children ?? [];
      return Scaffold(
        backgroundColor: AppColors.backgroundScaffold,
        body: Builder(builder: (context) {
          if (users.isEmpty) {
            return Center(
              child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 10, vertical: 16),
                  child: Text(
                    "no_results_found".tr(),
                    style: AppStyles.textSize14(),
                  )),
            );
          }
          return ListView.separated(
              padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
              itemBuilder: (_, index) {
                var user = users[index];
                return UserItem(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 14, vertical: 10),
                    decoration: BoxDecoration(
                      color: AppColors.white
                          .withOpacity(AppColors.isDark ? 0.3 : 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    onTap: () {
                      push(context, Attendance(user: user));
                    },
                    user: user,
                    showPhoneCall: false,
                    isSelectMulti: false,
                    isSelected: false);
              },
              separatorBuilder: (_, index) {
                return const Divider();
              },
              itemCount: users.length);
        }),
      );
    });
  }
}
