import 'dart:collection';

import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/models/education/attendance_model.dart';
import 'package:chat_app/repositories/education_repositories.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:table_calendar/table_calendar.dart';

class Attendance extends StatefulWidget {
  final UserModel user;
  const Attendance({super.key, required this.user});

  @override
  State<Attendance> createState() => _AttendanceState();
}

class _AttendanceState extends State<Attendance> {
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  bool _isLoading = false;
  AttendanceModel? _attendanceModel;
  final EducationRepositories _educationRepositories = EducationRepositories();
  @override
  void initState() {
    _fetchData();
    super.initState();
  }

  _fetchData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      _attendanceModel = await _educationRepositories.getAttendanceDetails(
        schoolCode: BlocProvider.of<AuthBloc>(context).state.schoolName!,
        body: {
          "sid": widget.user.sid,
          "month": DateFormat("yyyy-MM").format(_focusedDay)
        },
      );
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  List<Event> _getEventsForDay(DateTime day) {
    // Implementation example
    return kEvents[day] ?? [];
  }

  @override
  Widget build(BuildContext context) {
    return OverlayLoading(
      isLoading: _isLoading,
      child: Scaffold(
        backgroundColor: AppColors.backgroundScaffold,
        appBar: CustomAppbar(
          title: "attendance".tr(),
          isShowLeadingIcon: false,
        ),
        // floatingActionButton: FloatingActionButton(
        //   onPressed: () {},
        //   child: const Icon(Icons.add),
        // ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              TableCalendar(
                firstDay: DateTime(2000),
                lastDay: DateTime(3000),
                focusedDay: _focusedDay,
                daysOfWeekStyle: DaysOfWeekStyle(
                  weekdayStyle: AppStyles.textSize14(),
                ),
                headerStyle: HeaderStyle(
                  titleTextStyle: AppStyles.textSize14(),
                  formatButtonTextStyle: AppStyles.textSize14(),
                ),
                calendarBuilders: CalendarBuilders(
                  prioritizedBuilder: (context, day, focusedDay) {
                    var dateFormat = DateFormat("yyyy-MM-dd").format(day);
                    Color? color;
                    if (_attendanceModel != null) {
                      var index = _attendanceModel?.record?.indexWhere(
                          (element) => element.attendanceDate == dateFormat);
                      if (index != null && index != -1) {
                        var type =
                            _attendanceModel!.record![index].overAllStatus;
                        if (type == "H") {
                          color = AppColors.holidayColor;
                        } else if (type == "Ab") {
                          color = AppColors.absentColor;
                        } else if (type == "PL") {
                          color = AppColors.halfDayColor;
                        } else if (type == "PR") {
                          color = AppColors.presentColor;
                        }
                      }
                    }
                    return Container(
                        width: 40,
                        height: 40,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: color,
                        ),
                        child: Text(
                          "${day.day}",
                          style: AppStyles.textSize16(
                            fontWeight: FontWeight.w500,
                            color: color != null ? Colors.white : null,
                          ),
                        ));
                  },
                ),
                calendarStyle: CalendarStyle(
                    isTodayHighlighted: false,
                    weekendTextStyle: AppStyles.textSize14(),
                    todayTextStyle: AppStyles.textSize14(),
                    defaultTextStyle: AppStyles.textSize14()),
                calendarFormat: _calendarFormat,
                selectedDayPredicate: (day) {
                  // Use `selectedDayPredicate` to determine which day is currently selected.
                  // If this returns true, then `day` will be marked as selected.

                  // Using `isSameDay` is recommended to disregard
                  // the time-part of compared DateTime objects.
                  return isSameDay(_selectedDay, day);
                },
                availableCalendarFormats: const {CalendarFormat.month: 'Month'},
                onDaySelected: (selectedDay, focusedDay) {
                  if (!isSameDay(_selectedDay, selectedDay)) {
                    // Call `setState()` when updating the selected day
                    setState(() {
                      _selectedDay = selectedDay;
                      _focusedDay = focusedDay;
                    });
                  }
                },
                onPageChanged: (focusedDay) {
                  // No need to call `setState()` here
                  setState(() {
                    _focusedDay = focusedDay;
                  });
                  _fetchData();
                },
              ),
              SizedBox(
                height: 20,
              ),
              Row(
                children: [
                  Expanded(
                      child: Container(
                    height: 46,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: AppColors.presentColor,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "present".tr(),
                          style: AppStyles.textSize14(
                            fontWeight: FontWeight.w700,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          "${_attendanceModel?.presentCount ?? 0}",
                          style: AppStyles.textSize14(
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  )),
                  Expanded(
                      child: Container(
                    height: 46,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: AppColors.partialLeave,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "half_day".tr(),
                          style: AppStyles.textSize14(
                            fontWeight: FontWeight.w700,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          "${_attendanceModel?.partialCount ?? 0}",
                          style: AppStyles.textSize14(
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  )),
                  Expanded(
                      child: Container(
                    height: 46,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: AppColors.absentColor,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "absent".tr(),
                          style: AppStyles.textSize14(
                            fontWeight: FontWeight.w700,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          "${_attendanceModel?.absentCount ?? 0}",
                          style: AppStyles.textSize14(
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  )),
                  Expanded(
                      child: Container(
                    height: 46,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: AppColors.holidayColor,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "holiday".tr(),
                          style: AppStyles.textSize14(
                            fontWeight: FontWeight.w700,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          "${_attendanceModel?.holidayCount ?? 0}",
                          style: AppStyles.textSize14(
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  )),
                ],
              ),
              Builder(builder: (context) {
                var lastDate =
                    DateTime(_focusedDay.year, _focusedDay.month + 1, -1);
                var totalDays = lastDate.day;
                var w = getWidth(context) / 7;
                if (_attendanceModel?.record?.every((element) {
                      return element.sessionDetails?.every((element) {
                            return element.status == "" ||
                                element.status == null;
                          }) ??
                          false;
                    }) ??
                    false) {
                  return Container();
                }
                return Padding(
                  padding: const EdgeInsets.only(top: 0),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(vertical: 10),
                            width: w,
                            alignment: Alignment.center,
                            child: Text(
                              'Day',
                              style: AppStyles.textSize14(),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(vertical: 10),
                            width: w,
                            alignment: Alignment.center,
                            child: Text(
                              'S3',
                              style: AppStyles.textSize14(),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(vertical: 10),
                            width: w,
                            alignment: Alignment.center,
                            child: Text(
                              'S4',
                              style: AppStyles.textSize14(),
                            ),
                          ),
                          Container(
                            width: w,
                            padding: EdgeInsets.symmetric(vertical: 10),
                            alignment: Alignment.center,
                            child: Text(
                              'S5',
                              style: AppStyles.textSize14(),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(vertical: 10),
                            width: w,
                            alignment: Alignment.center,
                            child: Text(
                              'S6',
                              style: AppStyles.textSize14(),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(vertical: 10),
                            width: w,
                            alignment: Alignment.center,
                            child: Text(
                              'S7',
                              style: AppStyles.textSize14(),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(vertical: 10),
                            width: w,
                            alignment: Alignment.center,
                            child: Text(
                              'All',
                              style: AppStyles.textSize14(),
                            ),
                          ),
                        ],
                      ),
                      Column(
                        children: List.generate(totalDays + 1, (index) {
                          var day = index + 1;
                          String s3Data = '';
                          String s4Data = '';
                          String s5Data = '';
                          String s6Data = '';
                          String s7Data = '';
                          if (_attendanceModel?.record != null) {
                            try {
                              var s3Index = _attendanceModel
                                  ?.record![index].sessionDetails
                                  ?.indexWhere((element) =>
                                      element.attShortName == "S3");

                              if (s3Index != -1) {
                                s3Data = _attendanceModel!.record![index]
                                        .sessionDetails![s3Index!].status ??
                                    "";
                              }
                            } catch (e) {
                              print(e);
                            }

                            try {
                              var s4Index = _attendanceModel
                                  ?.record![index].sessionDetails
                                  ?.indexWhere((element) =>
                                      element.attShortName == "S4");

                              if (s4Index != -1) {
                                s4Data = _attendanceModel!.record![index]
                                        .sessionDetails![s4Index!].status ??
                                    "";
                              }
                            } catch (e) {
                              print(e);
                            }

                            try {
                              var s5Index = _attendanceModel
                                  ?.record![index].sessionDetails
                                  ?.indexWhere((element) =>
                                      element.attShortName == "S5");

                              if (s5Index != -1) {
                                s3Data = _attendanceModel!.record![index]
                                        .sessionDetails![s5Index!].status ??
                                    "";
                              }
                            } catch (e) {
                              print(e);
                            }

                            try {
                              var s6Index = _attendanceModel
                                  ?.record![index].sessionDetails
                                  ?.indexWhere((element) =>
                                      element.attShortName == "S6");

                              if (s6Index != -1) {
                                s3Data = _attendanceModel!.record![index]
                                        .sessionDetails![s6Index!].status ??
                                    "";
                              }
                            } catch (e) {
                              print(e);
                            }

                            try {
                              var s7Index = _attendanceModel
                                  ?.record![index].sessionDetails
                                  ?.indexWhere((element) =>
                                      element.attShortName == "S7");

                              if (s7Index != -1) {
                                s3Data = _attendanceModel!.record![index]
                                        .sessionDetails![s7Index!].status ??
                                    "";
                              }
                            } catch (e) {
                              print(e);
                            }
                          }
                          return Row(
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(vertical: 10),
                                width: w,
                                alignment: Alignment.center,
                                child: Text(
                                  '${index + 1}',
                                  style: AppStyles.textSize14(),
                                ),
                              ),
                              Container(
                                width: w,
                                padding: EdgeInsets.symmetric(vertical: 10),
                                alignment: Alignment.center,
                                child: Text(
                                  s3Data,
                                  style: AppStyles.textSize14(),
                                ),
                              ),
                              Container(
                                width: w,
                                padding: EdgeInsets.symmetric(vertical: 10),
                                alignment: Alignment.center,
                                child: Text(
                                  s4Data,
                                  style: AppStyles.textSize14(),
                                ),
                              ),
                              Container(
                                width: w,
                                padding: EdgeInsets.symmetric(vertical: 10),
                                alignment: Alignment.center,
                                child: Text(
                                  s5Data,
                                  style: AppStyles.textSize14(),
                                ),
                              ),
                              Container(
                                width: w,
                                padding: EdgeInsets.symmetric(vertical: 10),
                                alignment: Alignment.center,
                                child: Text(
                                  s6Data,
                                  style: AppStyles.textSize14(),
                                ),
                              ),
                              Container(
                                width: w,
                                padding: EdgeInsets.symmetric(vertical: 10),
                                alignment: Alignment.center,
                                child: Text(
                                  s7Data,
                                  style: AppStyles.textSize14(),
                                ),
                              ),
                              Container(
                                width: w,
                                padding: EdgeInsets.symmetric(vertical: 10),
                                alignment: Alignment.center,
                                child: Text(
                                  _attendanceModel
                                          ?.record?[index].overAllStatus ??
                                      "-",
                                  style: AppStyles.textSize14(),
                                ),
                              ),
                            ],
                          );
                        }),
                      )
                    ],
                  ),
                );
                return SizedBox(
                  // width: getWidth(context),
                  child: DataTable(
                      columns: <DataColumn>[
                        DataColumn(
                          label: Text(
                            'Day',
                            style: AppStyles.textSize14(),
                          ),
                        ),
                        DataColumn(
                          label: Text(
                            'S3',
                            style: AppStyles.textSize14(),
                          ),
                        ),
                        DataColumn(
                          label: Text(
                            'S4',
                            style: AppStyles.textSize14(),
                          ),
                        ),
                        DataColumn(
                          label: Text(
                            'S5',
                            style: AppStyles.textSize14(),
                          ),
                        ),
                        DataColumn(
                          label: Text(
                            'S6',
                            style: AppStyles.textSize14(),
                          ),
                        ),
                        DataColumn(
                          label: Text(
                            'S7',
                            style: AppStyles.textSize14(),
                          ),
                        ),
                        DataColumn(
                          label: Text(
                            'All',
                            textAlign: TextAlign.end,
                            style: AppStyles.textSize14(),
                          ),
                        ),
                      ],
                      rows: List.generate(totalDays + 1, (index) {
                        String? data;
                        if (_attendanceModel?.record != null) {
                          try {
                            var e = _attendanceModel?.record?.elementAt(index);
                          } catch (e) {
                            print(e);
                          }
                        }
                        return DataRow(
                          cells: <DataCell>[
                            DataCell(Text(
                              '${index + 1}',
                              style: AppStyles.textSize14(),
                            )),
                            DataCell(Text(
                              '${index + 1}',
                              style: AppStyles.textSize14(),
                            )),
                            DataCell(Text(
                              '${index + 1}',
                              style: AppStyles.textSize14(),
                            )),
                            DataCell(Text(
                              '${index + 1}',
                              style: AppStyles.textSize14(),
                            )),
                            DataCell(Text(
                              '${index + 1}',
                              style: AppStyles.textSize14(),
                            )),
                            DataCell(Text(
                              '${index + 1}',
                              style: AppStyles.textSize14(),
                            )),
                            DataCell(Container(
                              alignment: Alignment.centerRight,
                              child: Text(
                                '-',
                                textAlign: TextAlign.end,
                                style: AppStyles.textSize14(),
                              ),
                            )),
                          ],
                        );
                      })),
                );
              })
            ],
          ),
        ),
      ),
    );
  }
}

/// Example event class.
class Event {
  final String title;

  const Event(this.title);

  @override
  String toString() => title;
}

/// Example events.
///
/// Using a [LinkedHashMap] is highly recommended if you decide to use a map.
final kEvents = LinkedHashMap<DateTime, List<Event>>(
  equals: isSameDay,
  hashCode: getHashCode,
)..addAll(_kEventSource);

final _kEventSource = Map.fromIterable(List.generate(50, (index) => index),
    key: (item) => DateTime.utc(kFirstDay.year, kFirstDay.month, item * 5),
    value: (item) => List.generate(
        item % 4 + 1, (index) => Event('Event $item | ${index + 1}')))
  ..addAll({
    kToday: [
      Event('Today\'s Event 1'),
      Event('Today\'s Event 2'),
    ],
  });

int getHashCode(DateTime key) {
  return key.day * 1000000 + key.month * 10000 + key.year;
}

/// Returns a list of [DateTime] objects from [first] to [last], inclusive.
List<DateTime> daysInRange(DateTime first, DateTime last) {
  final dayCount = last.difference(first).inDays + 1;
  return List.generate(
    dayCount,
    (index) => DateTime.utc(first.year, first.month, first.day + index),
  );
}

final kToday = DateTime.now();
final kFirstDay = DateTime(kToday.year, kToday.month - 3, kToday.day);
final kLastDay = DateTime(kToday.year, kToday.month + 3, kToday.day);
