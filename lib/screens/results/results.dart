import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/screens/chat/search/search_parent.dart';
import 'package:chat_app/screens/results/exam_results/exam_results.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/navigator_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class Results extends StatefulWidget {
  const Results({super.key});

  @override
  State<Results> createState() => _ResultsState();
}

class _ResultsState extends State<Results> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(builder: (context, state) {
      var users = state.children ?? [];
      return Scaffold(
        backgroundColor: AppColors.backgroundScaffold,
        body: Builder(builder: (context) {
          if (users.isEmpty) {
            return Center(
              child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 10, vertical: 16),
                  child: Text(
                    "no_results_found".tr(),
                    style: AppStyles.textSize14(),
                  )),
            );
          }
          return ListView.separated(
              padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
              itemBuilder: (_, index) {
                var user = users[index];
                return UserItem(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 14, vertical: 10),
                    decoration: BoxDecoration(
                      color: AppColors.white
                          .withOpacity(AppColors.isDark ? 0.3 : 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    onTap: () {
                      push(
                          context,
                          ExamResults(
                            user: user,
                          ));
                    },
                    user: user,
                    showPhoneCall: false,
                    isSelectMulti: false,
                    isSelected: false);
              },
              separatorBuilder: (_, index) {
                return const Divider();
              },
              itemCount: users.length);
        }),
      );
    });
  }
}
