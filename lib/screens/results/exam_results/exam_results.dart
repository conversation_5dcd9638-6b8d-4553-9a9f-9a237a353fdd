import 'package:chat_app/export.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/screens/results/exam_results/widgets/academics.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:flutter/material.dart';

class ExamResults extends StatefulWidget {
  final UserModel user;
  const ExamResults({super.key, required this.user});

  @override
  State<ExamResults> createState() => _ExamResultsState();
}

class _ExamResultsState extends State<ExamResults>
    with TickerProviderStateMixin {
  late final TabController _tabController =
      TabController(length: 3, vsync: this);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundScaffold,
      appBar: CustomAppbar(
        height: kTextTabBarHeight * 2,
        title: "exam_results".tr(),
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.white,
          labelStyle: AppStyles.textSize14(),
          tabs: [
            Tab(
              text: "academics".tr(),
            ),
            const Tab(
              text: "",
            ),
            const Tab(
              text: "",
            )
          ],
        ),
      ),
      body: TabBarView(controller: _tabController, children: [
        Academics(
          sid: "${widget.user.sid}",
        ),
      ]),
    );
  }
}
