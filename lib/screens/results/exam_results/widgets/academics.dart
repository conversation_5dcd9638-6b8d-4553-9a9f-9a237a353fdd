import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/models/education/exam_model.dart';
import 'package:chat_app/repositories/education_repositories.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/app_styles.dart';
import 'package:chat_app/utils/assets.dart';
import 'package:chat_app/utils/extensions.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:chat_app/widgets/pdf_viewer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher_string.dart';

class Academics extends StatefulWidget {
  final String sid;
  const Academics({super.key, required this.sid});

  @override
  State<Academics> createState() => _AcademicsState();
}

class _AcademicsState extends State<Academics> {
  final EducationRepositories _educationRepositories = EducationRepositories();
  List<ExamModel> _exams = [];
  bool _isLoading = false;
  @override
  void initState() {
    _fetchData();
    super.initState();
  }

  _fetchData() async {
    try {
      setState(() {
        _isLoading = true;
      });
      _exams = await _educationRepositories.getListExamResults(
        body: {"sid": widget.sid},
        schoolCode: BlocProvider.of<AuthBloc>(context).state.schoolName!,
      );
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return OverlayLoading(
      isLoading: _isLoading,
      child: ListView.separated(
          padding: const EdgeInsets.all(20),
          itemBuilder: (_, index) {
            var exam = _exams[index];
            return GestureDetector(
              onTap: () {
                push(
                    context,
                    PDFViewer(
                        url:
                            "https://payitseasy.com/${BlocProvider.of<AuthBloc>(context).state.schoolName!.toLowerCase()}/${exam.pdfLink}",
                        title: "${exam.mainActivity}"));
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: AppColors.white.withOpacity(0.2),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "${exam.mainActivity}",
                            style: AppStyles.textSize17(
                                fontWeight: FontWeight.w500),
                          ),
                          Text(
                            exam.examDate ?? "",
                            style: AppStyles.textSize12(),
                          )
                        ],
                      ),
                    ),
                    SvgPicture.asset(
                      Assets.assetsImagesIconsPdfIcon,
                      width: 40,
                      height: 40,
                    ),
                  ],
                ),
              ),
            );
          },
          separatorBuilder: (_, index) {
            return 10.h;
          },
          itemCount: _exams.length),
    );
  }
}
