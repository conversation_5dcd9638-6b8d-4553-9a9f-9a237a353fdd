import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/screens/chat/search/search_parent.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher_string.dart';

class FeePay extends StatefulWidget {
  const FeePay({super.key});

  @override
  State<FeePay> createState() => _FeePayState();
}

class _FeePayState extends State<FeePay> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(builder: (context, state) {
      return Scaffold(
        appBar: CustomAppbar(
          title: "fee_pay".tr(),
          isShowLeadingIcon: false,
        ),
        body: Builder(builder: (_) {
          if (state.userModel?.type == "teacher") {
            return Center(
              child: Text("please_login_as_parent".tr()),
            );
          }
          var students = state.students ?? [];
          return ListView.builder(
              itemCount: students.length,
              itemBuilder: (_, index) {
                var user = students[index];
                return UserItem(
                    onTap: () {
                      var paymentLink =
                          'https://${FirestoreService.projectName.toLowerCase()}.feepayindia.in/opay/index.php?mobileNo=${user.phoneNumber}';
                      launchUrlString(paymentLink,
                          mode: LaunchMode.inAppBrowserView);
                    },
                    user: user,
                    showPhoneCall: false,
                    isSelectMulti: false,
                    isSelected: false);
              });
        }),
      );
    });
  }
}
