import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/models/payments/transaction_model.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:open_filex/open_filex.dart';

class TransactionDetails extends StatefulWidget {
  final TransactionModel transactionModel;
  final String downloadPath;
  const TransactionDetails(
      {super.key, required this.transactionModel, required this.downloadPath});

  @override
  State<TransactionDetails> createState() => _TransactionDetailsState();
}

class _TransactionDetailsState extends State<TransactionDetails> {
  TransactionModel get model => widget.transactionModel;
  bool _isLoading = false;
  _downloadReceipt() async {
    try {
      setState(() {
        _isLoading = true;
      });
      await Helper.downloadMedia(
          link: widget.downloadPath,
          onCompleted: (path) {
            setState(() {
              _isLoading = false;
            });
            OpenFilex.open(path);
          },
          onError: (error) {
            setState(() {
              _isLoading = false;
            });
          });

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      showToast(e.toString());
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(builder: (context, state) {
      return OverlayLoading(
        isLoading: _isLoading,
        child: Scaffold(
          appBar: CustomAppbar(
            title: "key_receipt".tr(),
          ),
          body: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Column(
                children: [
                  Container(
                    width: getWidth(context) * 0.9,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 12),
                    margin:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.primary),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Center(
                          child: Text(
                            "${state.schoolName}",
                            style: AppStyles.textSize20(
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ),
                        16.h,
                        Item(
                            label: "key_transaction_status".tr(),
                            value: "${model.status}"),
                        Item(
                            label: "key_fee_amount".tr(),
                            value: "${model.feeAmount}"),
                        Item(
                            label: "key_convinience_fee".tr(),
                            value: "${model.conveyanceFee}"),
                        Item(
                            label: "key_total_paid".tr(),
                            value: "${model.paidAmount}"),
                        Item(
                            label: "key_transaction_date".tr(),
                            value: "${model.transactionAt}"),
                        Item(
                            label: "key_order_id".tr(),
                            value: "${model.orderId}"),
                        Item(
                            label: "key_transaction_id".tr(),
                            value: "${model.transactionId}"),
                        Item(
                            label: "key_student_id".tr(),
                            value: "${model.studentId}"),
                        Builder(builder: (context) {
                          var index = state.students?.indexWhere(
                              (element) => element.sid == model.studentId);
                          if (index != null && index != -1) {
                            return Item(
                                label: "key_student_name".tr(),
                                value: "${state.students![index].name}");
                          }
                          return Container();
                        }),
                      ],
                    ),
                  ),
                  ButtonCustom(
                    onPressed: _downloadReceipt,
                    title: "key_download_receipt".tr(),
                    textStyle: AppStyles.textSize14(),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }
}

class Item extends StatelessWidget {
  final String label;
  final String value;
  const Item({super.key, required this.label, required this.value});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: AppStyles.textSize14(
            fontWeight: FontWeight.w500,
            color: AppColors.grey,
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 4, bottom: 10),
          child: Text(
            value,
            style: AppStyles.textSize14(
                // fontWeight: FontWeight.w500,
                // color: AppColors.grey,
                ),
          ),
        ),
      ],
    );
  }
}
