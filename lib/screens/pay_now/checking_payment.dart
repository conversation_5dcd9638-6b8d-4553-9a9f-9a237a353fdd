import 'dart:async';

import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/models/payments/transaction_model.dart';
import 'package:chat_app/repositories/home_repositories.dart';
import 'package:chat_app/screens/pay_now/transaction_details.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../export.dart';

class CheckingPayment extends StatefulWidget {
  final String orderId;
  final UserModel student;
  const CheckingPayment(
      {super.key, required this.orderId, required this.student});

  @override
  State<CheckingPayment> createState() => _CheckingPaymentState();
}

class _CheckingPaymentState extends State<CheckingPayment> {
  bool _isLoading = false;
  @override
  void initState() {
    _init();
    super.initState();
  }

  _init() async {
    _checkingStatus();
  }

  _checkingStatus() async {
    try {
      setState(() {
        _isLoading = true;
      });
      var response = await HomeRepositories().checkDeepLinkPaymentStatus(body: {
        "school_code": FirestoreService.projectName,
        "order_id": widget.orderId,
        // "order_id": "2121412"
        // "order_id": "AISKGL_6170_2713888000"
      });
      var student = widget.student;
      var body = response['data'][0];
      TransactionModel model = TransactionModel.fromJson(body);
      var receiptFile = await Helper.generatePdfFromOrder(
        schoolName: BlocProvider.of<AuthBloc>(context).state.schoolName!,
        openFile: false,
        orderId: widget.orderId,
        schoolCode: FirestoreService.projectName,
      );
      var task = await FirestoreService.uploadFileToFireStorage(receiptFile);
      await task.whenComplete(() => null);
      var receiptDownloadPath = await task.storage
          .ref(task.snapshot.metadata!.fullPath)
          .getDownloadURL();
      if (body['status'] == 'SUCCESS') {
        var fcm = BlocProvider.of<AuthBloc>(context).state.userModel?.fcmToken;
        if (fcm != null) {
          var message =
              "Amount: ${body['paidamount']}\nStudent ID: ${body['student_id']}\nAt: ${body['transaction_at']}\nTransaction ID: ${widget.orderId}";
          await FirestoreService.sendActivity(
              metadata: {
                "receipt_path": receiptDownloadPath,
                "order_id": widget.orderId,
                "school_code": FirestoreService.projectName,
                "student": widget.student.toMap(),
                "school_name":
                    BlocProvider.of<AuthBloc>(context).state.schoolName,
              },
              type: AppConstants.transactionActivityType,
              titleNotification: "Transaction success🎉",
              sid: student.sid!,
              message: message,
              senderName: "Transaction success🎉",
              receiverName: student.name ?? "",
              senderId: "",
              receiverId: student.docId!);
        }
      }
      setState(() {
        _isLoading = false;
      });

      replace(
          context,
          TransactionDetails(
            transactionModel: model,
            downloadPath: receiptDownloadPath,
          ));
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      // showToast("key_unhandle_error".tr());
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: getWidth(context) * 0.4,
              padding: EdgeInsets.symmetric(vertical: 16),
              child: LinearProgressIndicator(
                // borderRadius: BorderRadius.circular(1000),

                minHeight: 30,
              ),
            ),
            Text(
              "key_connecting_securely".tr(),
              style: AppStyles.textSize16(),
            )
          ],
        ),
      ),
    );
  }
}
