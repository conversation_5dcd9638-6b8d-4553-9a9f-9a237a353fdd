import 'dart:async';

import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/models/payments/transaction_model.dart';
import 'package:chat_app/repositories/home_repositories.dart';
import 'package:chat_app/screens/chat/search/search_parent.dart';
import 'package:chat_app/screens/pay_now/checking_payment.dart';
import 'package:chat_app/screens/pay_now/transaction_details.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher_string.dart';

class PayNow extends StatefulWidget {
  const PayNow({super.key});

  @override
  State<PayNow> createState() => _PayNowState();
}

class _PayNowState extends State<PayNow> with WidgetsBindingObserver {
  List<UserModel> _userSelected = [];
  TextEditingController _textEditingController = TextEditingController();
  bool _isLoading = false;
  String? _orderId;
  Timer? _timer;
  int _count = 0;
  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();
  }

  _submit() async {
    hideKeyboard(context);

    try {
      double amount = double.tryParse(_textEditingController.text) ?? 0;
      var randomNumber = Helper.getRandomNumber();

      var studentId = _userSelected.first.sid;
      var schoolCode = FirestoreService.projectName;
      _orderId = "${schoolCode}_${studentId}_$randomNumber";
      // var paymentLink =
      //     'upi://pay?pa=EASYBILLPAYSURENA@ICICI&pn=${BlocProvider.of<AuthBloc>(context).state.schoolName}&tr=${schoolCode}_${studentId}_$randomNumber&am=$amount&cu=INR&mc=6012&tn=${schoolCode}_${studentId}_$randomNumber';

      var paymentLink =
          'https://${BlocProvider.of<AuthBloc>(context).state.schoolName}.feepayindia.in/opay/index.php?mobileNo=${_userSelected.first.phoneNumber}';
      await Helper.processUpiPayment(
          onSuccess: (res) {
            replace(
                context,
                CheckingPayment(
                    orderId: _orderId!, student: _userSelected.first));
          },
          onError: (error) {
            showToast(error);
          },
          deeplink: paymentLink);
    } catch (e) {
      showToast("Can't launch");
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    print("state: $state");
    if (state == AppLifecycleState.resumed) {
      print("resume!!!!");
    }

    super.didChangeAppLifecycleState(state);
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard(context);
      },
      child: OverlayLoading(
        // customLoading: ,
        customLoading: Container(
          width: getWidth(context),
          height: getHeight(context),
          color: Colors.black.withOpacity(0.2),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: CircularProgressIndicator(),
              ),
              SizedBox(
                width: 100,
                child: ButtonCustom(
                  onPressed: () {
                    _orderId = null;
                    _isLoading = false;
                    _timer?.cancel();
                    setState(() {});
                  },
                  textStyle: AppStyles.textSize12(),
                  title: "key_cancel".tr(),
                ),
              ),
            ],
          ),
        ),
        isLoading: _isLoading,
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          appBar: CustomAppbar(
            title: "key_pay_now".tr(),
          ),
          bottomNavigationBar: Container(
            margin: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            padding: EdgeInsets.symmetric(
              vertical: 10,
              horizontal: 10,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: _textEditingController,
                  onChanged: (value) {
                    setState(() {});
                  },
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                  // inputFormatters: [
                  //   FilteringTextInputFormatter.digitsOnly,
                  // ],
                  style: AppStyles.textSize14(),
                  decoration: InputDecoration(
                      prefixIcon: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 8)
                                .copyWith(right: 0),
                            child: SvgPicture.asset(
                              AppAssets.ruppeIcon,
                              width: 14,
                              height: 14,
                              color: AppColors.primary,
                            ),
                          ),
                        ],
                      ),
                      contentPadding:
                          EdgeInsets.symmetric(vertical: 3, horizontal: 8),
                      hintText: "key_enter_amount".tr(),
                      border: OutlineInputBorder()),
                ),
                10.h,
                ButtonCustom(
                  enabled: _userSelected.isNotEmpty &&
                      _textEditingController.text.isNotEmpty,
                  onPressed: _submit,
                  title: "key_pay_now".tr(),
                )
              ],
            ),
          ),
          body: BlocBuilder<AuthBloc, AuthState>(
            builder: (context, state) {
              return Container(
                padding: EdgeInsets.symmetric(
                  vertical: 10,
                  horizontal: 10,
                ),
                child: Column(
                  children: [
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                      width: getWidth(context),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: AppColors.white,
                          )),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "${state.schoolName}",
                            style: AppStyles.textSize20(
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          2.h,
                          Text(
                            "key_dear_parent".tr(),
                            style: AppStyles.textSize14(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          2.h,
                          Text(
                            "key_dear_parent_messages".tr(),
                            style: AppStyles.textSize14(
                                // fontWeight: FontWeight.w500,
                                ),
                          )
                        ],
                      ),
                    ),
                    12.h,
                    Column(
                      children: List.generate(state.students!.length, (index) {
                        var user = state.students![index];
                        bool isSelected = _userSelected.contains(user);
                        return UserItem(
                          isSelectMulti: true,
                          isSelected: isSelected,
                          onTap: () {
                            _userSelected.clear();
                            _userSelected.add(user);
                            setState(() {});
                          },
                          user: user,
                          showPhoneCall: false,
                        );
                      }),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
