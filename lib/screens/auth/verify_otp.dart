import 'dart:async';

import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/screens/main_tab.dart';
import 'package:chat_app/utils/app_asset.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/app_constants.dart';
import 'package:chat_app/utils/app_styles.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:chat_app/utils/helper.dart';
import 'package:chat_app/utils/local_storage.dart';
import 'package:chat_app/utils/navigator_service.dart';
import 'package:chat_app/widgets/button_custom.dart';
import 'package:chat_app/widgets/overlay_loading.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

class VerifyOtp extends StatefulWidget {
  const VerifyOtp({
    Key? key,
    required this.phoneNumber,
  }) : super(key: key);

  final String phoneNumber;

  @override
  State<VerifyOtp> createState() => _VerifyOtpState();
}

class _VerifyOtpState extends State<VerifyOtp> {
  TextEditingController textEditingController = TextEditingController();
  String? _verificationId;
  bool hasError = false;
  String currentText = "";
  final formKey = GlobalKey<FormState>();
  bool _canSubmit = false;
  bool _isLoading = false;
  int _countdown = 60;
  Timer? _timer;

  _sendOTP() async {
    if (kDebugMode) {
      setState(() {
        _verificationId = "1234";
      });
      return;
    }
    try {
      setState(() {
        _isLoading = true;
      });
      _verificationId = await FirestoreService.sendOtp(widget.phoneNumber);
      var number = int.tryParse(widget.phoneNumber);
      if ([9916773896, 9481577018].contains(number)) {
        _verificationId = "1234";
      }
      _resetCountdown();
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  void _startCountdown() {
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      setState(() {
        if (_countdown > 0) {
          _countdown--;
        } else {
          _timer?.cancel();
        }
      });
    });
  }

  void _resetCountdown() {
    setState(() {
      _countdown = 60;
      _startCountdown();
    });
  }

  _submit() async {
    if (_verificationId != null) {
      if (_verificationId != textEditingController.text.trim()) {
        showToast("OTP is incorrect!");
        return;
      }
      try {
        setState(() {
          _isLoading = true;
        });

        var teacherDocId = await FirestoreService.checkPhoneNumber(
            phoneNumber: widget.phoneNumber, type: AppConstants.teacherType);
        if (teacherDocId != null) {
          BlocProvider.of<AuthBloc>(context).add(GetProfile(
              docId: teacherDocId,
              onSuccess: () {
                replaceToRoot(context, MainTab());
              }));
        } else {
          String? docId = await FirestoreService.checkPhoneNumber(
              phoneNumber: widget.phoneNumber, type: AppConstants.parentType);
          List<String> students =
              await FirestoreService.findStudentsWithPhoneNumber(
                  phoneNumber: widget.phoneNumber);
          // ignore: prefer_conditional_assignment
          if (docId == null) {
            docId = await FirestoreService.addUser({
              "sid": "",
              "name": "",
              "whatsappNumber": int.parse(widget.phoneNumber),
              "contact_one": int.parse(widget.phoneNumber),
              "contact_two": int.parse(widget.phoneNumber),
              "pr_cgroup_name": "",
              "pr_course_name": "",
              "pr_section_name": "",
              "pr_ac_year": "",
              "activeStatus": 1,
              "type": "parent",
              "students": students,
            });
          } else {
            await FirestoreService.updateUser(
                docId: docId, data: {"students": students});
          }
          await FirestoreService.addParentPhoneNumberToStudents(
              parentPhoneNumber: int.parse(widget.phoneNumber),
              parentId: docId,
              students: students);

          BlocProvider.of<AuthBloc>(context).add(GetProfile(
              docId: docId,
              onSuccess: () {
                replaceToRoot(context, MainTab());
              }));
        }

        setState(() {
          _isLoading = false;
        });
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        showToast(e.toString());
      }
    }
  }

  @override
  void initState() {
    _sendOTP();
    super.initState();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(builder: (context, state) {
      return OverlayLoading(
        isLoading: _isLoading || state.getProfileLoading!,
        child: Scaffold(
          backgroundColor: AppColors.backgroundScaffold,
          body: GestureDetector(
            onTap: () {},
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
              child: Column(
                children: <Widget>[
                  const SizedBox(height: 30),
                  SizedBox(
                    height: 200,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(30),
                      child: Image.asset(AppAssets.verifyOtp),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 8.0),
                    child: Text(
                      'key_phone_number_verification'.tr(),
                      style: AppStyles.textSize24(),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 30.0, vertical: 8),
                    child: RichText(
                      text: TextSpan(
                        text: "key_enter_the_code_sent_to"
                            .tr()
                            .replaceAll("{phone}", "${widget.phoneNumber}"),
                        children: [
                          // TextSpan(
                          //     text: "${widget.phoneNumber}",
                          //     style: AppStyles.textSize14(
                          //       color: AppColors.primary,
                          //     )),
                        ],
                        style: AppStyles.textSize14(),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  // Text(
                  //   "_canSubmit: $_canSubmit \n verificationId: $_verificationId \n code: ${textEditingController.text}",
                  //   style: AppStyles.textSize16(),
                  // ),
                  const SizedBox(
                    height: 20,
                  ),
                  Form(
                    key: formKey,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 8.0,
                        horizontal: 60,
                      ),
                      child: PinCodeTextField(
                        appContext: context,
                        length: 4,
                        obscureText: true,
                        obscuringCharacter: '*',
                        blinkWhenObscuring: true,
                        animationType: AnimationType.fade,
                        validator: (v) {
                          return "";
                        },
                        pinTheme: PinTheme(
                          shape: PinCodeFieldShape.box,
                          borderRadius: BorderRadius.circular(5),
                          fieldHeight: 50,
                          fieldWidth: 40,
                          activeFillColor: AppColors.primary,
                          borderWidth: 0,
                          activeColor: AppColors.grey,
                          inactiveFillColor: AppColors.grey,
                          errorBorderWidth: 0,
                          inactiveBorderWidth: 0,
                          activeBorderWidth: 0,
                          selectedFillColor: AppColors.primary,
                          selectedColor: AppColors.primary,
                          inactiveColor: AppColors.primary,
                        ),
                        cursorColor: AppColors.primary,
                        animationDuration: const Duration(milliseconds: 300),
                        enableActiveFill: true,
                        controller: textEditingController,
                        keyboardType: TextInputType.number,
                        onCompleted: (v) {
                          _submit();
                        },
                        onChanged: (value) {
                          if (value.length < 4) {
                            setState(() {
                              _canSubmit = false;
                            });
                          } else {
                            setState(() {
                              _canSubmit = true;
                            });
                          }
                        },
                        beforeTextPaste: (text) {
                          debugPrint("Allowing to paste $text");
                          //if you return true then it will show the paste confirmation dialog. Otherwise if false, then nothing will happen.
                          //but you can show anything you want here, like your pop up saying wrong paste format or etc
                          return true;
                        },
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text("key_didnt_receive_a_pin".tr() + "?",
                          style: AppStyles.textSize14(
                            color: AppColors.white,
                          )),
                      TextButton(
                        onPressed: _countdown == 0 ? _sendOTP : null,
                        child: Text(
                            _countdown == 0 ? "key_resend".tr() : "$_countdown",
                            style:
                                AppStyles.textSize14(color: AppColors.primary)),
                      )
                    ],
                  ),
                  const SizedBox(
                    height: 14,
                  ),
                  ButtonCustom(
                      enabled: _canSubmit && _verificationId != null,
                      onPressed: _submit,
                      title: "key_verify".tr()),
                  const SizedBox(
                    height: 16,
                  ),
                  ButtonCustom(
                    onPressed: () {
                      pop(context);
                    },
                    isOutLine: true,
                    backgroundColor: Colors.transparent,
                    title: "key_back".tr(),
                  )
                ],
              ),
            ),
          ),
        ),
      );
    });
  }
}
