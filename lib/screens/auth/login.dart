import 'package:chat_app/screens/auth/verify_otp.dart';
import 'package:chat_app/utils/app_asset.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/app_styles.dart';
import 'package:chat_app/utils/extensions.dart';
import 'package:chat_app/utils/helper.dart';
import 'package:chat_app/utils/navigator_service.dart';
import 'package:chat_app/widgets/button_custom.dart';
import 'package:chat_app/widgets/overlay_loading.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_svg/svg.dart';

import '../../utils/database.dart';
import '../../utils/firestore_service.dart';

class Login extends StatefulWidget {
  const Login({super.key});

  @override
  State<Login> createState() => _LoginState();
}

class _LoginState extends State<Login> {
  bool _isLoading = false;
  bool _canSubmit = false;
  final TextEditingController _phoneNumberController = TextEditingController();

  _submit() async {
    hideKeyboard(context);
    try {
      // bool exist = FirestoreService.checkFirebaseProjectNameExist(
      //     _schoolCodeController.text);
      // if (exist == false) {
      //   showToast("School code not exist in system");
      //   return;
      // }
      // await FirestoreService.updateFirebaseProjectName(
      //     _schoolCodeController.text);

      setState(() {
        _isLoading = true;
      });

      String? docId = await FirestoreService.checkPhoneNumber(
          phoneNumber: _phoneNumberController.text, type: "teacher");
      if (docId != null) {
        push(
            context,
            VerifyOtp(
              phoneNumber: _phoneNumberController.text,
            ));
      } else {
        showToast("key_this_phone_number_not_exist_in_system".tr());
      }
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  _checkCanSubmit() {
    if (_phoneNumberController.text.length < 10) {
      setState(() {
        _canSubmit = false;
      });
    } else {
      setState(() {
        _canSubmit = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return OverlayLoading(
      isLoading: _isLoading,
      child: Scaffold(
        // floatingActionButton: FloatingActionButton(onPressed: () async {
        //   // await FirestoreService.addUser({
        //   //   "sid": "namadeva",
        //   //   "name": "namadeva",
        //   //   "whatsappNumber": 9916773896,
        //   //   "contact_one": 9916773896,
        //   //   "contact_two": 9916773896,
        //   //   "pr_cgroup_name": "",
        //   //   "pr_course_name": "",
        //   //   "pr_section_name": "",
        //   //   "pr_ac_year": "2023-24",
        //   //   "activeStatus": 1,
        //   //   "type": "student"
        //   // });
        //   var data = vpsudpData;
        //   var list = data;
        //   for (int i = 0; i < list.length; i++) {
        //     var student = list[i];
        //     var sid = student['SID'];
        //     var name = student['StudentName'];
        //     var contactOne = student['contact_one'];
        //     var pr_cgroup_name = student['pr_cgroup_name'];
        //     var pr_course_name = student['pr_course_name'];
        //     var pr_section_name = student['pr_section_name'];
        //     var pr_ac_year = student['pr_ac_year'];
        //     var activeStatus = student['active_status'];
        //     var whatsappNumber = student['whatsapp_number'];
        //     var contactTwo = student['contact_two'];
        //     await FirestoreService.addUser({
        //       "sid": sid,
        //       "name": name,
        //       "whatsappNumber": whatsappNumber,
        //       "contact_one": contactOne,
        //       "contact_two": contactTwo,
        //       "pr_cgroup_name": pr_cgroup_name,
        //       "pr_course_name": pr_course_name,
        //       "pr_section_name": pr_section_name,
        //       "pr_ac_year": pr_ac_year,
        //       "activeStatus": activeStatus,
        //       "type": "student"
        //     });
        //     print("done: $sid, index: $i");
        //   }
        // }),
        backgroundColor: AppColors.backgroundScaffold,
        body: SafeArea(
          bottom: false,
          child: Column(
            children: [
              // Row(
              //   children: [
              //     IconButton(
              //       onPressed: () {
              //         pop(context);
              //       },
              //       icon: Icon(
              //         Icons.arrow_back,
              //         color: AppColors.white,
              //       ),
              //     ),
              //   ],
              // ),
              40.h,
              Stack(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primary.withOpacity(0.8),
                          spreadRadius: 20,
                          blurRadius: 100,
                          offset: Offset(15, 10), // changes position of shadow
                        ),
                      ],
                    ),
                  ),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.asset(
                      "assets/images/app_logo_teacher.png",
                      width: 60,
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 20,
              ),
              Text(
                "Welcome to",
                style: AppStyles.textSize20(),
              ),
              Text(
                "SchoolsMessenger",
                style: AppStyles.textSize26(),
              ),
              SizedBox(
                height: 20,
              ),
              Align(
                alignment: Alignment.center,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      AppAssets.groupPeople,
                      width: 200,
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 24,
              ),
              Expanded(
                child: SingleChildScrollView(
                  child: Container(
                    height: getHeight(context) * 0.8,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30),
                      ),
                      color: AppColors.white,
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 24, vertical: 20),
                    child: Column(
                      children: [
                        Text(
                          "key_login_with_phone_number".tr(),
                          style: AppStyles.textSize18(
                            color: AppColors.black,
                          ),
                        ),
                        SizedBox(
                          height: 16,
                        ),

                        TextField(
                            controller: _phoneNumberController,
                            onChanged: (value) {
                              _checkCanSubmit();
                            },
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            style: AppStyles.textSize14(
                              color: AppColors.black,
                            ),
                            decoration: InputDecoration(
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 10, horizontal: 10),
                              hintText: "key_phone_number".tr(),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              hintStyle: AppStyles.textSize14(
                                color: AppColors.grey,
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide:
                                    const BorderSide(color: AppColors.primary),
                                borderRadius: BorderRadius.circular(8),
                              ),
                            )),
                        SizedBox(
                          height: 16,
                        ),
                        ButtonCustom(
                          padding: EdgeInsets.symmetric(vertical: 15),
                          onPressed: _submit,
                          title: "key_next".tr(),
                          enabled: _canSubmit,
                        ),
                        SizedBox(
                          height: 16,
                        ),
                        ButtonCustom(
                          padding: EdgeInsets.symmetric(vertical: 15),
                          onPressed: () {
                            pop(context);
                          },
                          title: "key_back".tr(),
                          isOutLine: true,
                        ),
                        // Padding(
                        //   padding: const EdgeInsets.symmetric(vertical: 10),
                        //   child: Row(
                        //     mainAxisAlignment: MainAxisAlignment.center,
                        //     children: [
                        //       Container(
                        //         height: 1,
                        //         margin: const EdgeInsets.symmetric(horizontal: 4),
                        //         width: 20,
                        //         color: Colors.black,
                        //       ),
                        //       Text(
                        //         "key_or".tr(),
                        //         style: AppStyles.textSize14(
                        //           color: AppColors.black,
                        //         ),
                        //       ),
                        //       Container(
                        //         height: 1,
                        //         margin: const EdgeInsets.symmetric(horizontal: 4),
                        //         width: 20,
                        //         color: Colors.black,
                        //       ),
                        //     ],
                        //   ),
                        // ),
                        // Container(
                        //   height: 50,
                        //   decoration: BoxDecoration(
                        //     color: Colors.lightBlueAccent,
                        //     borderRadius: BorderRadius.circular(5),
                        //   ),
                        //   child: Row(
                        //     mainAxisAlignment: MainAxisAlignment.center,
                        //     children: [
                        //       Padding(
                        //         padding: const EdgeInsets.symmetric(horizontal: 10),
                        //         child: SvgPicture.asset(
                        //           AppAssets.googleIcon,
                        //           width: 30,
                        //         ),
                        //       ),
                        //       Text(
                        //         "key_sign_in_with_google".tr(),
                        //         style: AppStyles.textSize16(
                        //           fontWeight: FontWeight.w500,
                        //         ),
                        //       ),
                        //     ],
                        //   ),
                        // ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
