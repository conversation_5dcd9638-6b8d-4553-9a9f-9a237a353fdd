import 'package:chat_app/screens/auth/login.dart';
import 'package:chat_app/screens/auth/verify_otp.dart';
import 'package:chat_app/utils/app_asset.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/app_styles.dart';
import 'package:chat_app/utils/extensions.dart';
import 'package:chat_app/utils/helper.dart';
import 'package:chat_app/utils/navigator_service.dart';
import 'package:chat_app/widgets/button_custom.dart';
import 'package:chat_app/widgets/overlay_loading.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_svg/svg.dart';
import 'package:upgrader/upgrader.dart';

import '../../utils/database.dart';
import '../../utils/firestore_service.dart';

class EnterSchoolCode extends StatefulWidget {
  const EnterSchoolCode({super.key});

  @override
  State<EnterSchoolCode> createState() => _EnterSchoolCodeState();
}

class _EnterSchoolCodeState extends State<EnterSchoolCode> {
  bool _isLoading = false;
  bool _canSubmit = false;

  final TextEditingController _schoolCodeController = TextEditingController();
  _submit() async {
    hideKeyboard(context);
    try {
      setState(() {
        _isLoading = true;
      });
      await FirestoreService.getFirebaseSettings();
      bool exist = FirestoreService.checkFirebaseProjectNameExist(
          _schoolCodeController.text);
      if (exist == false) {
        showToast("key_school_code_not_exist_in_system".tr());
        setState(() {
          _isLoading = false;
        });
        return;
      }
      await FirestoreService.updateFirebaseProjectName(
          _schoolCodeController.text);
      push(context, Login());
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  _checkCanSubmit() {
    if (_schoolCodeController.text.isEmpty) {
      setState(() {
        _canSubmit = false;
      });
    } else {
      setState(() {
        _canSubmit = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return UpgradeAlert(
      child: OverlayLoading(
        isLoading: _isLoading,
        child: Scaffold(
          // backgroundColor: AppColors.backgroundScaffold,
          body: SafeArea(
            bottom: false,
            child: Column(
              children: [
                40.h,
                Stack(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withOpacity(0.8),
                            spreadRadius: 20,
                            blurRadius: 100,
                            offset:
                                Offset(15, 10), // changes position of shadow
                          ),
                        ],
                      ),
                    ),
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.asset(
                        "assets/images/app_logo_teacher.png",
                        width: 60,
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 20,
                ),
                Text(
                  "key_welcome_to".tr(),
                  style: AppStyles.textSize20(),
                ),
                Text(
                  "SchoolsMessenger",
                  style: AppStyles.textSize26(),
                ),
                SizedBox(
                  height: 20,
                ),
                Align(
                  alignment: Alignment.center,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        AppAssets.groupPeople,
                        width: 200,
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 24,
                ),
                Expanded(
                  child: SingleChildScrollView(
                    child: Container(
                      height: getHeight(context) * 0.8,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(30),
                          topRight: Radius.circular(30),
                        ),
                        color: AppColors.white,
                      ),
                      padding:
                          EdgeInsets.symmetric(horizontal: 24, vertical: 20),
                      child: Column(
                        children: [
                          Text(
                            "key_enter_school_code".tr(),
                            style: AppStyles.textSize18(
                              color: AppColors.black,
                            ),
                          ),
                          SizedBox(
                            height: 16,
                          ),
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: TextField(
                                controller: _schoolCodeController,
                                onChanged: (value) {
                                  bool isValid =
                                      RegExp(r'^[a-zA-Z]+$').hasMatch(value);
                                  if (isValid == false) {
                                    String result = value.replaceAll(
                                        RegExp(r'[^a-zA-Z]'), '');
                                    _schoolCodeController.text = result;
                                  }
                                  _checkCanSubmit();
                                },
                                style: AppStyles.textSize14(
                                  color: AppColors.black,
                                ),
                                // maxLength: 6,
                                inputFormatters: [
                                  LengthLimitingTextInputFormatter(6),
                                ],
                                textCapitalization:
                                    TextCapitalization.characters,
                                decoration: InputDecoration(
                                  contentPadding: EdgeInsets.symmetric(
                                      vertical: 10, horizontal: 10),
                                  hintText: "Enter school code",
                                  hintStyle: AppStyles.textSize14(
                                    color: AppColors.grey,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: const BorderSide(
                                        color: AppColors.primary),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                )),
                          ),
                          SizedBox(
                            height: 16,
                          ),
                          ButtonCustom(
                            padding: EdgeInsets.symmetric(vertical: 15),
                            onPressed: _submit,
                            title: "key_next".tr(),
                            enabled: _canSubmit,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
