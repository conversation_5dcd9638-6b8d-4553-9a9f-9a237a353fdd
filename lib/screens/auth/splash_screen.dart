import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/screens/auth/enter_school_code.dart';
import 'package:chat_app/screens/auth/login.dart';
import 'package:chat_app/screens/main_tab.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:chat_app/utils/navigator_service.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:flutter_svg/svg.dart';

import '../../utils/app_asset.dart';
import '../../utils/app_colors.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    _init();
    super.initState();
  }

  _init() async {
    try {
      await _getLanguage();
      await FirestoreService.getFirebaseSettings();
      var schoolCode = await LocalStorage().getSchoolCode();
      await FirestoreService.updateFirebaseProjectName(schoolCode);

      BlocProvider.of<AuthBloc>(context).add(GetStarted((value) async {
        if (value) {
          replaceToRoot(context, MainTab());
          // Check for app updates after successful login (only on mobile)
        } else {
          replaceToRoot(context, EnterSchoolCode());
        }
      }));
    } catch (e) {
      print(e.toString());
    }
  }

  _getLanguage() async {
    var locale = await LocalStorage().getLanguage();
    await context
        .setLocale(Locale(locale.split('_').first, locale.split('_').last));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              children: [
                // Container(
                //   width: 40,
                //   height: 40,
                //   decoration: BoxDecoration(
                //     color: AppColors.primary,
                //     shape: BoxShape.circle,
                //     boxShadow: [
                //       BoxShadow(
                //         color: AppColors.primary.withOpacity(0.8),
                //         spreadRadius: 20,
                //         blurRadius: 100,
                //         offset: Offset(15, 10), // changes position of shadow
                //       ),
                //     ],
                //   ),
                // ),
                Positioned(
                  child: Align(
                    alignment: Alignment.center,
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        // borderRadius: BorderRadius.circular(10),
                      ),
                      child: Center(
                        child: SvgPicture.asset(
                          AppAssets.appLogo,
                          width: 50,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(top: 10),
              child: Text(
                "SchoolsMessenger",
                style: AppStyles.textSize16(
                    color: Colors.white, fontWeight: FontWeight.w700),
              ),
            )
            // Padding(
            //   padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 20)
            //       .copyWith(top: 40),
            //   child: LinearProgressIndicator(),
            // ),
          ],
        ),
      ),
    );
  }
}
