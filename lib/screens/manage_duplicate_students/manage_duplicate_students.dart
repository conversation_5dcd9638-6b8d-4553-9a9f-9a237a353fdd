import 'package:flutter/material.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/app_styles.dart';
import 'package:chat_app/widgets/custom_appbar.dart';

class ManageDuplicateStudents extends StatefulWidget {
  const ManageDuplicateStudents({super.key});

  @override
  State<ManageDuplicateStudents> createState() =>
      _ManageDuplicateStudentsState();
}

class _ManageDuplicateStudentsState extends State<ManageDuplicateStudents> {
  List<UserModel> allStudents = [];
  Map<String, List<UserModel>> duplicateGroups = {};
  bool isLoading = true;
  bool isDeleting = false;

  @override
  void initState() {
    super.initState();
    _loadStudents();
  }

  Future<void> _loadStudents() async {
    setState(() {
      isLoading = true;
    });

    try {
      // Lấy tất cả students từ local storage và Firestore
      var localStudents = await FirestoreService.searchUser(
        type: "student",
        fromLocal: true,
        fetchAll: true,
        hideMe: false,
      );

      // Lấy thêm từ Firestore để đảm bảo có đầy đủ dữ liệu
      var firestoreStudents = await FirestoreService.searchUser(
        type: "student",
        fromLocal: false,
        fetchAll: true,
        hideMe: false,
      );

      // Gộp và loại bỏ trùng lặp dựa trên docId
      Set<String> seenDocIds = {};
      allStudents = [];

      for (var student in [...localStudents, ...firestoreStudents]) {
        if (student.docId != null && !seenDocIds.contains(student.docId)) {
          seenDocIds.add(student.docId!);
          allStudents.add(student);
        }
      }

      _findDuplicates();
    } catch (e) {
      debugPrint('Error loading students: $e');
    }

    setState(() {
      isLoading = false;
    });
  }

  void _findDuplicates() {
    duplicateGroups.clear();

    // Nhóm students theo sid (student ID)
    Map<String, List<UserModel>> groupedBySid = {};

    for (var student in allStudents) {
      if (student.sid != null && student.sid!.isNotEmpty) {
        String sid = student.sid!;
        if (!groupedBySid.containsKey(sid)) {
          groupedBySid[sid] = [];
        }
        groupedBySid[sid]!.add(student);
      }
    }

    // Chỉ giữ lại những nhóm có nhiều hơn 1 student (trùng lặp)
    duplicateGroups = Map.fromEntries(
        groupedBySid.entries.where((entry) => entry.value.length > 1));

    // Sắp xếp students trong mỗi nhóm theo prAcYear (nhỏ nhất trước)
    duplicateGroups.forEach((sid, students) {
      students.sort((a, b) {
        if (a.prAcYear == null && b.prAcYear == null) return 0;
        if (a.prAcYear == null) return 1;
        if (b.prAcYear == null) return -1;
        return a.prAcYear!.compareTo(b.prAcYear!);
      });
    });
  }

  Future<void> _deleteDuplicatesWithSmallestYear() async {
    setState(() {
      isDeleting = true;
    });

    try {
      List<String> docIdsToDelete = [];

      for (var entry in duplicateGroups.entries) {
        List<UserModel> students = entry.value;

        // Find students with prAcYear (not null)
        List<UserModel> studentsWithYear = students
            .where((s) => s.prAcYear != null && s.prAcYear!.isNotEmpty)
            .toList();

        if (studentsWithYear.length > 1) {
          // Sort by prAcYear ascending
          studentsWithYear.sort((a, b) => a.prAcYear!.compareTo(b.prAcYear!));

          // Only delete student with smallest prAcYear (first in sorted list)
          if (studentsWithYear[0].docId != null) {
            docIdsToDelete.add(studentsWithYear[0].docId!);
          }
        }
      }

      // Perform deletion
      for (String docId in docIdsToDelete) {
        await FirestoreService.deleteUser(docId: docId);
      }

      // Reload data
      await _loadStudents();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Deleted ${docIdsToDelete.length} duplicate students'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }

    setState(() {
      isDeleting = false;
    });
  }

  // Helper method to check if a student will be deleted
  bool _willBeDeleted(UserModel student, List<UserModel> groupStudents) {
    // Find students with prAcYear in this group
    List<UserModel> studentsWithYear = groupStudents
        .where((s) => s.prAcYear != null && s.prAcYear!.isNotEmpty)
        .toList();

    if (studentsWithYear.length <= 1) {
      return false; // No deletion if only 1 or 0 students have prAcYear
    }

    // Sort by prAcYear ascending
    studentsWithYear.sort((a, b) => a.prAcYear!.compareTo(b.prAcYear!));

    // Return true if this student is the one with smallest prAcYear
    return studentsWithYear.isNotEmpty &&
        student.docId == studentsWithYear[0].docId;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: 'Manage Duplicate Students',
        actions: [
          if (duplicateGroups.isNotEmpty && !isDeleting)
            IconButton(
              onPressed: _deleteDuplicatesWithSmallestYear,
              icon: Icon(Icons.delete_sweep, color: Colors.red),
              tooltip: 'Delete students with smallest prAcYear',
            ),
          IconButton(
            onPressed: _loadStudents,
            icon: Icon(Icons.refresh),
            tooltip: 'Reload',
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : duplicateGroups.isEmpty
              ? _buildNoDataWidget()
              : _buildDuplicatesList(),
    );
  }

  Widget _buildNoDataWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle,
            size: 80,
            color: Colors.green,
          ),
          SizedBox(height: 16),
          Text(
            'No duplicate students found',
            style: AppStyles.textSize18().copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Total students: ${allStudents.length}',
            style: AppStyles.textSize14().copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDuplicatesList() {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(16),
          color: Colors.orange.withValues(alpha: 0.1),
          child: Row(
            children: [
              Icon(Icons.warning, color: Colors.orange),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Found ${duplicateGroups.length} duplicate student groups',
                  style: AppStyles.textSize16().copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.all(16),
            itemCount: duplicateGroups.length,
            itemBuilder: (context, index) {
              String sid = duplicateGroups.keys.elementAt(index);
              List<UserModel> students = duplicateGroups[sid]!;

              return _buildDuplicateGroup(sid, students);
            },
          ),
        ),
        if (isDeleting)
          Container(
            padding: EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('Deleting duplicate students...'),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildDuplicateGroup(String sid, List<UserModel> students) {
    return Card(
      margin: EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Student ID: $sid',
              style: AppStyles.textSize16().copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
            Text(
              '${students.length} duplicate records',
              style: AppStyles.textSize14().copyWith(
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 12),
            ...students.map((student) => _buildStudentItem(student, students)),
          ],
        ),
      ),
    );
  }

  Widget _buildStudentItem(UserModel student, List<UserModel> groupStudents) {
    bool willBeDeleted = _willBeDeleted(student, groupStudents);
    bool hasAcYear = student.prAcYear != null && student.prAcYear!.isNotEmpty;

    return Container(
      margin: EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: willBeDeleted
            ? Colors.red.withValues(alpha: 0.1)
            : Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: willBeDeleted ? Colors.red : Colors.grey,
          width: willBeDeleted ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  student.name ?? 'No name',
                  style: AppStyles.textSize14().copyWith(
                    fontWeight: FontWeight.bold,
                    color: willBeDeleted ? Colors.red[700] : null,
                  ),
                ),
              ),
              if (willBeDeleted)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Will be deleted',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 4),
          Text('DocID: ${student.docId ?? "N/A"}',
              style: AppStyles.textSize12()),
          Text('Phone: ${student.phoneNumber ?? "N/A"}',
              style: AppStyles.textSize12()),
          Text('Course: ${student.prCourseName ?? "N/A"}',
              style: AppStyles.textSize12()),
          Text('Section: ${student.prSectionName ?? "N/A"}',
              style: AppStyles.textSize12()),
          if (hasAcYear)
            Text(
              'prAcYear: ${student.prAcYear}',
              style: AppStyles.textSize12().copyWith(
                color: willBeDeleted ? Colors.red[700] : Colors.blue[700],
                fontWeight: FontWeight.bold,
              ),
            ),
        ],
      ),
    );
  }
}
