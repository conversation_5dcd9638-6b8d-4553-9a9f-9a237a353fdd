import 'package:chat_app/export.dart';
import 'package:chat_app/main.dart';
import 'package:chat_app/screens/auth/splash_screen.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/widgets/custom_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/placeholder.dart';

class Appearance extends StatefulWidget {
  const Appearance({super.key});

  @override
  State<Appearance> createState() => _AppearanceState();
}

class _AppearanceState extends State<Appearance> {
  bool isDark = AppColors.isDark;
  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  _setTheme() async {
    await LocalStorage().setTheme(isDark);
    replaceToRoot(context, SplashScreen());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundScaffold,
      appBar: CustomAppbar(title: "key_appearance".tr()),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
            child: Row(
              children: [
                Radio(
                    value: true,
                    fillColor: MaterialStatePropertyAll(AppColors.primary),
                    activeColor: AppColors.primary,
                    groupValue: isDark,
                    onChanged: (value) {
                      setState(() {
                        isDark = true;
                      });
                      _setTheme();
                    }),
                Text(
                  "key_dark".tr(),
                  style: AppStyles.textSize14(),
                ),
              ],
            ),
          ),
          const Divider(
            height: 1,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
            child: Row(
              children: [
                Radio(
                    fillColor: MaterialStatePropertyAll(AppColors.primary),
                    activeColor: AppColors.primary,
                    value: false,
                    groupValue: isDark,
                    onChanged: (value) {
                      setState(() {
                        isDark = false;
                      });
                      _setTheme();
                    }),
                Text(
                  "key_light".tr(),
                  style: AppStyles.textSize14(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
