import 'package:chat_app/export.dart';
import 'package:chat_app/screens/auth/splash_screen.dart';
import 'package:chat_app/utils/app_colors.dart';

import 'package:flutter/material.dart';

class Language extends StatefulWidget {
  const Language({super.key});

  @override
  State<Language> createState() => _LanguageState();
}

class _LanguageState extends State<Language> {
  List<Locale> _locales = [];
  late Locale _currentLocale;

  _changeLanguage(Locale locale) async {
    await context.setLocale(locale);
    await LocalStorage().changeLanguage(locale.toStringWithSeparator());
    replaceToRoot(context, SplashScreen());
  }

  String _getTitle(Locale locale) {
    switch (locale.languageCode) {
      case "en":
        return "English";
      case "hi":
        return "Hindi";
      case "ta":
        return "Tamil";
      case "kn":
        return "Kannada";
      case "te":
        return "Telugu";
    }
    return "";
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    _locales = context.supportedLocales.toList();
    _currentLocale = context.locale;
    setState(() {});
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundScaffold,
      appBar: CustomAppbar(
        title: "key_language".tr(),
        actions: [
          TextButton(
              onPressed: () {
                _changeLanguage(_currentLocale);
              },
              child: Text("key_save".tr()))
        ],
      ),
      body: Column(
          children: List.generate(_locales.length, (index) {
        return ItemRadio(
            groupValue: _currentLocale,
            value: _locales[index],
            onChanged: (value) {
              setState(() {
                _currentLocale = value;
              });
            },
            title: _getTitle(_locales[index]));
      })),
    );
  }
}

class ItemRadio extends StatelessWidget {
  final dynamic groupValue;
  final dynamic value;
  final Function(dynamic) onChanged;
  final String title;
  const ItemRadio(
      {super.key,
      required this.groupValue,
      required this.onChanged,
      required this.title,
      this.value});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
      child: Row(
        children: [
          Radio(
              value: value,
              fillColor: MaterialStatePropertyAll(AppColors.primary),
              activeColor: AppColors.primary,
              groupValue: groupValue,
              onChanged: (value) {
                onChanged(value);
              }),
          Text(
            title,
            style: AppStyles.textSize14(),
          ),
        ],
      ),
    );
  }
}
