import 'package:cached_network_image/cached_network_image.dart';
import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/screens/auth/enter_school_code.dart';
import 'package:chat_app/screens/settings/appearance.dart';
import 'package:chat_app/screens/settings/children_management.dart';

import 'package:chat_app/screens/settings/language.dart';
import 'package:chat_app/screens/update_database/update_database.dart';
import 'package:chat_app/utils/app_asset.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/app_constants.dart';
import 'package:chat_app/utils/app_styles.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:chat_app/utils/helper.dart';
import 'package:chat_app/utils/navigator_service.dart';
import 'package:chat_app/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:package_info_plus/package_info_plus.dart';

class Settings extends StatefulWidget {
  const Settings({super.key});

  @override
  State<Settings> createState() => _SettingsState();
}

class _SettingsState extends State<Settings> {
  PackageInfo? _packageInfo;
  @override
  void initState() {
    _init();
    super.initState();
  }

  _init() async {
    _packageInfo = await PackageInfo.fromPlatform();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(builder: (context, state) {
      var user = state.userModel;
      return Scaffold(
        appBar: CustomAppbar(
          title: "key_settings".tr(),
          onPopBack: () {
            if (Navigator.canPop(context)) {
              pop(context);
            } else {}
          },
        ),
        backgroundColor: AppColors.backgroundScaffold,
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 14),
            child: Column(
              children: [
                if (state.userModel?.type == AppConstants.teacherType)
                  Column(
                    children: [
                      const SizedBox(
                        height: 40,
                      ),
                      Align(
                        alignment: Alignment.center,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(1000),
                          child: Builder(builder: (context) {
                            if (user?.avatar?.isNotEmpty ?? false) {
                              return CachedNetworkImage(
                                imageUrl: "${user?.avatar}",
                                width: 80,
                                height: 80,
                                fit: BoxFit.cover,
                              );
                            }
                            return Container(
                              width: 80,
                              height: 80,
                              decoration: const BoxDecoration(
                                color: AppColors.grey,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.person,
                                color: Colors.white,
                              ),
                            );
                          }),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 10),
                        child: Text(
                          "${user?.name}",
                          style: AppStyles.textSize18(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      Builder(builder: (_) {
                        String? type;
                        if (state.userModel?.type == AppConstants.teacherType) {
                          type = "key_teacher".tr();
                        } else if (state.userModel?.type ==
                            AppConstants.parentType) {
                          type = "key_parent".tr();
                        }
                        if (type != null) {
                          return Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(right: 4),
                                child: SvgPicture.asset(
                                  state.userModel?.type ==
                                          AppConstants.teacherType
                                      ? AppAssets.glassesIcon
                                      : AppAssets.familyIcon,
                                  color: AppColors.primary,
                                  width: 16,
                                ),
                              ),
                              Text(
                                "$type",
                                style: AppStyles.textSize14(),
                              )
                            ],
                          );
                        }
                        return Container();
                      }),
                      Builder(builder: (_) {
                        return Column(
                          children: [
                            if (state.userModel?.contactOne != null)
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 2),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Padding(
                                      padding: EdgeInsets.only(right: 4),
                                      child: Icon(
                                        Icons.phone,
                                        size: 16,
                                        color: AppColors.primary,
                                      ),
                                    ),
                                    Text(
                                      "${state.userModel?.contactOne}",
                                      style: AppStyles.textSize12(),
                                    )
                                  ],
                                ),
                              ),
                            if (state.userModel?.contactTwo != null)
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 2),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Padding(
                                      padding: EdgeInsets.only(right: 4),
                                      child: Icon(
                                        Icons.phone,
                                        size: 16,
                                        color: AppColors.primary,
                                      ),
                                    ),
                                    Text(
                                      "${state.userModel?.contactTwo}",
                                      style: AppStyles.textSize12(),
                                    )
                                  ],
                                ),
                              ),
                            if (state.userModel?.whatsappNumber != null)
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 2),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Padding(
                                        padding:
                                            const EdgeInsets.only(right: 4),
                                        child: SvgPicture.asset(
                                          AppAssets.whatsappIcon,
                                          color: AppColors.primary,
                                          width: 16,
                                        )),
                                    Text(
                                      "${state.userModel?.whatsappNumber}",
                                      style: AppStyles.textSize12(),
                                    )
                                  ],
                                ),
                              ),
                            Text(
                              "ID: ${user?.sid}",
                              style: AppStyles.textSize12(),
                            ),
                            Text(
                              "School Code: ${FirestoreService.projectName.toUpperCase()}",
                              style: AppStyles.textSize12(),
                            ),
                          ],
                        );
                      }),
                    ],
                  ),

                // if (state.userModel?.type == AppConstants.teacherType)
                //   Padding(
                //     padding: const EdgeInsets.only(top: 10),
                //     child: SizedBox(
                //       width: 140,
                //       child: ButtonCustom(
                //         onPressed: () {
                //           Navigator.push(context,
                //               MaterialPageRoute(builder: (_) => EditProfile()));
                //         },
                //         height: 30,
                //         padding: const EdgeInsets.symmetric(vertical: 8),
                //         textStyle: AppStyles.textSize12(),
                //         title: "key_edit_profile".tr(),
                //         width: 200,
                //       ),
                //     ),
                //   ),
                const SizedBox(
                  height: 24,
                ),
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  decoration: BoxDecoration(
                    color: AppColors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(5),
                  ),
                  child: Column(children: [
                    // SettingItem(
                    //   title: "key_data_and_storage".tr(),
                    //   iconPath: AppAssets.storageIcon,
                    //   onTap: () {
                    //     Navigator.push(
                    //         context,
                    //         MaterialPageRoute(
                    //             builder: (_) => DataAndStorage()));
                    //   },
                    // ),
                    // Divider(
                    //   height: 1,
                    //   color: AppColors.white,
                    // ),
                    if (state.userModel?.type == AppConstants.parentType)
                      Column(
                        children: [
                          SettingItem(
                              onTap: () {
                                push(context, const ChildrenManagement());
                              },
                              title: "key_profile".tr(),
                              iconPath: AppAssets.studentCap),
                          Divider(
                            height: 1,
                            color: AppColors.white,
                          ),
                        ],
                      ),
                    SettingItem(
                        onTap: () {
                          push(context, const Appearance());
                        },
                        title: "key_appearance".tr(),
                        iconPath: AppAssets.darkModeIcon),
                    Divider(
                      height: 1,
                      color: AppColors.white,
                    ),
                    SettingItem(
                        onTap: () {
                          push(context, const Language());
                        },
                        title: "key_language".tr(),
                        iconPath: AppAssets.languageIcon),
                    Divider(
                      height: 1,
                      color: AppColors.white,
                    ),

                    SettingItem(
                        onTap: () {
                          Helper.showActionDialog(
                              context: context,
                              title: "key_notifications".tr(),
                              message: "key_do_you_want_to_log_out".tr(),
                              onClose: () {},
                              onConfirm: () {
                                BlocProvider.of<AuthBloc>(context)
                                    .add(Logout());
                                Navigator.of(context)
                                    .push(MaterialPageRoute(builder: (_) {
                                  return EnterSchoolCode();
                                }));
                              });
                        },
                        title: "key_log_out".tr(),
                        iconPath: AppAssets.logoutIcon),
                  ]),
                ),
                const Spacer(),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  child: Text(
                    "${"version".tr()}: ${_packageInfo?.version}(${_packageInfo?.buildNumber})",
                    style: AppStyles.textSize12(),
                  ),
                )
              ],
            ),
          ),
        ),
      );
    });
  }
}

class SettingItem extends StatelessWidget {
  final String title;
  final String? iconPath;
  final Function()? onTap;
  final EdgeInsets? padding;
  final Widget? suffixIcon;
  final double? iconWidth;
  final double? iconHeight;
  const SettingItem(
      {super.key,
      required this.title,
      required this.iconPath,
      this.onTap,
      this.padding,
      this.suffixIcon,
      this.iconWidth = 24,
      this.iconHeight});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding:
            padding ?? const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        child: Row(
          children: [
            if (iconPath != null)
              Padding(
                padding: const EdgeInsets.only(right: 10),
                child: SvgPicture.asset(
                  iconPath!,
                  color: AppColors.primary,
                  width: iconWidth,
                  height: iconHeight,
                ),
              ),
            Expanded(
              child: Text(
                title,
                style: AppStyles.textSize14(),
              ),
            ),
            if (suffixIcon != null) suffixIcon!
          ],
        ),
      ),
    );
  }
}
