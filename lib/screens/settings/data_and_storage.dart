import 'package:chat_app/utils/app_asset.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/app_styles.dart';
import 'package:chat_app/utils/extensions.dart';
import 'package:chat_app/widgets/button_custom.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class DataAndStorage extends StatefulWidget {
  const DataAndStorage({super.key});

  @override
  State<DataAndStorage> createState() => _DataAndStorageState();
}

class _DataAndStorageState extends State<DataAndStorage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundScaffold,
      appBar: AppBar(
        title: Text("key_data_and_storage".tr()),
      ),
      body: SafeArea(
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              24.h,
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.primary, width: 4),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    "356 MB",
                    style: AppStyles.textSize20(),
                  ),
                ),
              ),
              24.h,
              Item(
                title: "Video",
              ),
              Item(
                title: "Image",
              ),
              Item(
                title: "Other files",
              ),
              24.h,
              ButtonCustom(onPressed: () {}, title: "key_clear_cache".tr())
            ],
          ),
        ),
      ),
    );
  }
}

class Item extends StatelessWidget {
  final String title;

  const Item({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Row(
        children: [
          SvgPicture.asset(
            AppAssets.documentIcon,
            color: AppColors.primary,
          ),
          4.w,
          Expanded(
            child: Text(
              title,
              style: AppStyles.textSize14(),
            ),
          ),
          Text(
            "100 MB",
            style: AppStyles.textSize14(),
          )
        ],
      ),
    );
  }
}
