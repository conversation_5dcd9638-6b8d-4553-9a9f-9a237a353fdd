import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/app_constants.dart';
import 'package:chat_app/utils/extensions.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:chat_app/utils/helper.dart';
import 'package:chat_app/utils/navigator_service.dart';
import 'package:chat_app/widgets/button_custom.dart';
import 'package:chat_app/widgets/overlay_loading.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';

import '../../utils/app_styles.dart';

class EditProfile extends StatefulWidget {
  const EditProfile({super.key});

  @override
  State<EditProfile> createState() => _EditProfileState();
}

class _EditProfileState extends State<EditProfile> {
  File? _file;
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  bool _isLoading = false;
  @override
  void initState() {
    _phoneController.text = BlocProvider.of<AuthBloc>(context)
            .state
            .userModel
            ?.phoneNumber
            ?.toString() ??
        "";
    _nameController.text =
        BlocProvider.of<AuthBloc>(context).state.userModel?.name ?? "";
    super.initState();
  }

  _submit() async {
    try {
      setState(() {
        _isLoading = true;
      });
      String? avatar;
      if (_file != null) {
        var task = await FirestoreService.uploadFileToFireStorage(_file!);
        await task.whenComplete(() => null);
        String path = await task.storage
            .ref(task.snapshot.metadata!.fullPath)
            .getDownloadURL();
        avatar = path;
      }
      await FirestoreService.updateProfile(
          data: {
        "name": _nameController.text,
      }..addAll(avatar != null ? {"avatar": avatar} : {}));

      BlocProvider.of<AuthBloc>(context).add(GetStarted((p0) {}));
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        pop(context);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(builder: (context, state) {
      return OverlayLoading(
        isLoading: _isLoading,
        child: Scaffold(
          backgroundColor: AppColors.backgroundScaffold,
          appBar: AppBar(title: Text("key_edit_profile".tr())),
          body: SafeArea(
              child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Align(
                  alignment: Alignment.center,
                  child: GestureDetector(
                    onTap: () async {
                      var result = await ImagePicker()
                          .pickImage(source: ImageSource.gallery);
                      if (result != null) {
                        _file = File(result.path);
                        setState(() {});
                      }
                    },
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(1000),
                          child: Builder(builder: (context) {
                            if (_file != null) {
                              return Image.file(
                                _file!,
                                width: 80,
                                height: 80,
                                fit: BoxFit.cover,
                              );
                            }
                            if (state.userModel?.avatar?.isEmpty ?? true) {
                              return Container(
                                width: 80,
                                height: 80,
                              );
                            }
                            return CachedNetworkImage(
                              imageUrl: "${state.userModel?.avatar}",
                              width: 80,
                              height: 80,
                              fit: BoxFit.cover,
                            );
                          }),
                        ),
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.black.withOpacity(0.2),
                          ),
                          child: const Icon(
                            Icons.camera_alt_outlined,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                24.h,
                TextField(
                  controller: _nameController,
                  style: AppStyles.textSize14(),
                  enabled: state.userModel?.type == AppConstants.parentType
                      ? true
                      : false,
                  decoration: InputDecoration(
                    hintStyle: AppStyles.textSize14(
                      color: Colors.grey,
                    ),
                    constraints: BoxConstraints(maxHeight: 45),
                    contentPadding: EdgeInsets.symmetric(horizontal: 10),
                    // hintText: "key_first_name".tr(),
                    prefixIcon: const Icon(Icons.perm_contact_cal),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    fillColor: AppColors.white.withOpacity(0.1),
                    filled: true,
                  ),
                ),
                16.h,
                TextField(
                  controller: _phoneController,
                  style: AppStyles.textSize14(),
                  decoration: InputDecoration(
                    hintStyle: AppStyles.textSize14(
                      color: Colors.grey,
                    ),
                    constraints: BoxConstraints(maxHeight: 45),
                    contentPadding: EdgeInsets.symmetric(horizontal: 10),
                    hintText: "key_phone_number".tr(),
                    prefixIcon: const Icon(Icons.phone),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    enabled: false,
                    fillColor: AppColors.white.withOpacity(0.1),
                    filled: true,
                  ),
                ),
                16.h,
                ButtonCustom(
                  onPressed: _submit,
                  title: "key_save".tr(),
                ),
              ],
            ),
          )),
        ),
      );
    });
  }
}
