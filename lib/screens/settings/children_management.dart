import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/placeholder.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../chat/search/search_parent.dart';

class ChildrenManagement extends StatefulWidget {
  const ChildrenManagement({super.key});

  @override
  State<ChildrenManagement> createState() => _ChildrenManagementState();
}

class _ChildrenManagementState extends State<ChildrenManagement> {
  bool _isLoading = false;
  List<UserModel> _chilrens = [];
  @override
  void initState() {
    _init();
    super.initState();
  }

  _init() async {
    try {
      setState(() {
        _isLoading = true;
      });
      var users =
          BlocProvider.of<AuthBloc>(context).state.userModel?.students ?? [];
      for (var user in users) {
        var model = await FirestoreService.getUserDetails(docId: user);
        if (model != null) {
          _chilrens.add(model);
        }
      }
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return OverlayLoading(
      isLoading: _isLoading,
      child: Scaffold(
        appBar: CustomAppbar(
          title: "key_profile".tr(),
        ),
        body: Column(
          children: List.generate(_chilrens.length, (index) {
            var user = _chilrens[index];
            return Container(
              padding: EdgeInsets.symmetric(vertical: 16, horizontal: 10),
              margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.primary),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Column(
                children: [
                  UserItem(
                    isSelectMulti: false,
                    isSelected: false,
                    onTap: () {},
                    user: _chilrens[index],
                    showPhoneCall: false,
                  ),
                  if (user.contactOne != null)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 4),
                      color: Colors.transparent,
                      child: Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: AppColors.white.withOpacity(0.2),
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: SvgPicture.asset(
                                AppAssets.callIcon,
                                color: AppColors.primary,
                              ),
                            ),
                          ),
                          10.w,
                          Text(
                            "${user.contactOne} - Main",
                            style: AppStyles.textSize14(),
                          )
                        ],
                      ),
                    ),
                  if (user.contactTwo != null)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 4),
                      color: Colors.transparent,
                      child: Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: AppColors.white.withOpacity(0.2),
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: SvgPicture.asset(
                                AppAssets.callIcon,
                                color: AppColors.primary,
                              ),
                            ),
                          ),
                          10.w,
                          Text(
                            "${user.contactOne} - Secondary",
                            style: AppStyles.textSize14(),
                          )
                        ],
                      ),
                    ),
                  if (user.whatsappNumber != null)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 4),
                      color: Colors.transparent,
                      child: Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: AppColors.white.withOpacity(0.2),
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: SvgPicture.asset(
                                AppAssets.callIcon,
                                color: AppColors.primary,
                              ),
                            ),
                          ),
                          10.w,
                          Text(
                            "${user.whatsappNumber} - Whatsapp",
                            style: AppStyles.textSize14(),
                          )
                        ],
                      ),
                    ),
                ],
              ),
            );
          }),
        ),
      ),
    );
  }
}
