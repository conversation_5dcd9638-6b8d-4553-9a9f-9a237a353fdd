import 'dart:io';

import 'package:chat_app/screens/chat/chat_details/widgets/optimized_chat_item.dart';
import 'package:chat_app/utils/platform_helper.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pdfx/pdfx.dart';
import '../../../export.dart';
import '../../../models/chats/conversatoin_model.dart';
import '../../../utils/firestore_service.dart';
import '../../../blocs/auth_bloc/auth_bloc.dart';
import '../../../utils/app_colors.dart';

import '../chat_details/chat_details.dart';

class HomeworkAndNotesItem {
  final ChatModel message;
  final ConversationModel conversation;
  final bool isHomework;
  final DateTime timestamp;

  HomeworkAndNotesItem({
    required this.message,
    required this.conversation,
    required this.isHomework,
    required this.timestamp,
  });
}

// Global key để access HomeworkAndNotes từ bên ngoài
final GlobalKey<_HomeworkAndNotesState> homeworkAndNotesGlobalKey =
    GlobalKey<_HomeworkAndNotesState>();

class HomeworkAndNotes extends StatefulWidget {
  const HomeworkAndNotes({super.key});

  @override
  State<HomeworkAndNotes> createState() => _HomeworkAndNotesState();
}

class _HomeworkAndNotesState extends State<HomeworkAndNotes>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<HomeworkAndNotesItem> _homeworkItems = [];
  List<HomeworkAndNotesItem> _notesItems = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadHomeworkAndNotes();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // Public method để gọi từ bên ngoài
  Future<void> refreshHomeworkAndNotes() async {
    await _loadHomeworkAndNotes();
  }

  Future<void> _loadHomeworkAndNotes() async {
    try {
      setState(() {
        _isLoading = true;
      });
      var user = BlocProvider.of<AuthBloc>(context).state.userModel;

      // Get all conversations for the selected student
      var conversations =
          await FirestoreService.getAllUserConversations(user!.docId!);

      List<HomeworkAndNotesItem> allHomework = [];
      List<HomeworkAndNotesItem> allNotes = [];

      for (var conversation in conversations) {
        // Get all messages from each conversation
        var messages = await FirestoreService.getAllMessagesFromConversation(
            conversation.id!);

        for (var message in messages) {
          DateTime? messageTime;

          if (message.isHomework == true) {
            messageTime = message.homeworkSetAt != null
                ? DateTime.tryParse(message.homeworkSetAt!)
                : DateTime.tryParse(message.createdAt ?? '');

            if (messageTime != null) {
              allHomework.add(HomeworkAndNotesItem(
                message: message,
                conversation: conversation,
                isHomework: true,
                timestamp: messageTime,
              ));
            }
          }

          if (message.isNotes == true) {
            messageTime = message.notesSetAt != null
                ? DateTime.tryParse(message.notesSetAt!)
                : DateTime.tryParse(message.createdAt ?? '');

            if (messageTime != null) {
              allNotes.add(HomeworkAndNotesItem(
                message: message,
                conversation: conversation,
                isHomework: false,
                timestamp: messageTime,
              ));
            }
          }
        }
      }

      // Sort by timestamp (newest first)
      allHomework.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      allNotes.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      setState(() {
        _homeworkItems = allHomework;
        _notesItems = allNotes;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  _onDownload(ChatModel chat, int index) async {
    try {
      setState(() {
        _isLoading = true;
      });

      if (kIsWeb) {
        // On web, trigger browser download
        setState(() {
          _isLoading = false;
        });

        String fileName = chat.message!.split('/').last;
        if (fileName.isEmpty) {
          fileName = 'downloaded_file';
        }

        await PlatformHelper.downloadFromUrl(
            url: chat.message!, fileName: fileName);
        showToast("Download started...");
        return;
      }

      final directory =
          await PlatformHelper.getApplicationDocumentsDirectoryCompat();
      if (directory == null) {
        setState(() {
          _isLoading = false;
        });
        showToast("File operations not supported on this platform");
        return;
      }

      // if (await file.exists()) {
      //   file.delete();
      // }
      String url = chat.message!;

      // Use Uri.parse() to parse the URL
      Uri uri = Uri.parse(url);

      // Extract the 'path' component from the URI
      String path = uri.path;

      path = path.split('/').last;

      path = Uri.decodeComponent(path);

      File file = File('${directory.path}/$path');
      file.create(recursive: true);
      Reference storageReference = FirestoreService.storageRef.child(path);
      await storageReference.writeToFile(file);
      // _chats[index] = chat.copyWith(localPath: file.path);
      setState(() {
        _isLoading = false;
      });

      await PlatformHelper.openFile(file.path);
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundScaffold,
      body: OverlayLoading(
        isLoading: false,
        child: Column(
          children: [
            TabBar(
              onTap: (index) {
                _loadHomeworkAndNotes();
              },
              controller: _tabController,
              indicatorColor: AppColors.primary,
              labelColor: AppColors.white,
              unselectedLabelColor: AppColors.grey,
              tabs: [
                Tab(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SvgPicture.asset(
                        AppAssets.homeworkIcon,
                        width: 16,
                        height: 16,
                        colorFilter: ColorFilter.mode(
                          Colors.orange,
                          BlendMode.srcIn,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text("Homework (${_homeworkItems.length})"),
                    ],
                  ),
                ),
                Tab(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SvgPicture.asset(
                        AppAssets.documentIcon,
                        width: 16,
                        height: 16,
                        colorFilter: ColorFilter.mode(
                          Colors.blue,
                          BlendMode.srcIn,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text("Notes (${_notesItems.length})"),
                    ],
                  ),
                ),
              ],
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildHomeworkList(),
                  _buildNotesList(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHomeworkList() {
    if (_isLoading && _homeworkItems.isEmpty) {
      return Center(
        child: CupertinoActivityIndicator(),
      );
    }
    if (_homeworkItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              AppAssets.homeworkIcon,
              width: 64,
              height: 64,
              colorFilter: ColorFilter.mode(
                Colors.orange.withOpacity(0.5),
                BlendMode.srcIn,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              "No homework found",
              style: AppStyles.textSize16(color: AppColors.grey),
            ),
            const SizedBox(height: 8),
            Text(
              "Mark messages as homework to see them here",
              style: AppStyles.textSize14(color: AppColors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadHomeworkAndNotes,
      color: AppColors.primary,
      child: ListView.separated(
        separatorBuilder: (context, index) {
          return SizedBox(
            height: 8,
          );
        },
        padding: const EdgeInsets.all(16),
        itemCount: _homeworkItems.length,
        itemBuilder: (context, index) {
          return _buildHomeworkNotesCard(_homeworkItems[index], index);
        },
      ),
    );
  }

  Widget _buildNotesList() {
    if (_isLoading && _notesItems.isEmpty) {
      return Center(
        child: CupertinoActivityIndicator(),
      );
    }
    if (_notesItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              AppAssets.documentIcon,
              width: 64,
              height: 64,
              colorFilter: ColorFilter.mode(
                Colors.blue.withOpacity(0.5),
                BlendMode.srcIn,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              "No notes found",
              style: AppStyles.textSize16(color: AppColors.grey),
            ),
            const SizedBox(height: 8),
            Text(
              "Mark messages as notes to see them here",
              style: AppStyles.textSize14(color: AppColors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadHomeworkAndNotes,
      color: AppColors.primary,
      child: ListView.separated(
        separatorBuilder: (context, index) {
          return SizedBox(
            height: 8,
          );
        },
        padding: const EdgeInsets.all(16),
        itemCount: _notesItems.length,
        itemBuilder: (context, index) {
          return _buildHomeworkNotesCard(_notesItems[index], index);
        },
      ),
    );
  }

  Widget _buildHomeworkNotesCard(HomeworkAndNotesItem item, index) {
    final String groupName = _getGroupName(item.conversation);
    final String timeAgo = _getTimeAgo(item.timestamp);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.black.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: item.isHomework
              ? Colors.orange.withOpacity(0.3)
              : Colors.blue.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // Header with group info and type badge
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: item.isHomework
                  ? Colors.orange.withOpacity(0.1)
                  : Colors.blue.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                // Type badge
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: item.isHomework
                        ? Colors.orange.withOpacity(0.2)
                        : Colors.blue.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: item.isHomework ? Colors.orange : Colors.blue,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SvgPicture.asset(
                        item.isHomework
                            ? AppAssets.homeworkIcon
                            : AppAssets.documentIcon,
                        width: 12,
                        height: 12,
                        colorFilter: ColorFilter.mode(
                          item.isHomework ? Colors.orange : Colors.blue,
                          BlendMode.srcIn,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        item.isHomework ? "Homework" : "Notes",
                        style: AppStyles.textSize10().copyWith(
                          color: item.isHomework ? Colors.orange : Colors.blue,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                // Group name
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        groupName,
                        style: AppStyles.textSize14().copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        timeAgo,
                        style: AppStyles.textSize12(color: AppColors.grey),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Message content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Align(
              alignment: Alignment.centerRight,
              child: OptimizedChatItem(
                showMarkIcon: false,
                showMessageIndicators:
                    false, // Don't show indicators since we have them in header
                widthItem: getWidth(context) -
                    (32 * 1), // Account for container padding
                showAvatar: false,
                onReplyTap: (_) {},
                chat: item.message,
                onLongPress: (_) {},
                onDownload: (_, __) {
                  _onDownload(item.message, index);
                },
                index: index,
                onHomeworkTap: () {},
                onNotesTap: () {},
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getGroupName(ConversationModel conversation) {
    var user = BlocProvider.of<AuthBloc>(context).state.userModel;

    // First try conversation title
    if (conversation.title?.isNotEmpty == true) {
      return conversation.title!;
    }

    // For student context, show teacher name or other participants
    if (conversation.users != null && conversation.users!.isNotEmpty) {
      var otherUsers = conversation.users!
          .where((u) => u.docId != user?.docId)
          .map((u) => u.name ?? "Unknown")
          .take(2)
          .join(", ");

      if (otherUsers.isNotEmpty) {
        return otherUsers;
      }
    }

    // Fallback to teacher name if available
    if (conversation.teacherName?.isNotEmpty == true) {
      return conversation.teacherName!;
    }

    return "Group Chat";
  }

  String _getTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return "${difference.inDays}d ago";
    } else if (difference.inHours > 0) {
      return "${difference.inHours}h ago";
    } else if (difference.inMinutes > 0) {
      return "${difference.inMinutes}m ago";
    } else {
      return "Just now";
    }
  }

  String _getMessagePreview(ChatModel message) {
    if (message.message?.isNotEmpty == true) {
      return message.message!;
    }

    String type = message.type ?? "";
    switch (type) {
      case 'image':
        return "📷 Image";
      case 'video':
        return "🎥 Video";
      case 'audio':
        return "🎵 Audio";
      case 'file':
        return "📄 ${message.fileName ?? 'File'}";
      default:
        return "Message";
    }
  }

  void _navigateToChat(HomeworkAndNotesItem item) {
    // Navigate to chat details and automatically show homework/notes navigation
    push(
      context,
      ChatDetails(
        converationId: item.conversation.id,
        conversationModel: item.conversation,
        isNewMessage: false,
        jumpToMessageId: item.message.id,
        showHomeworkNotesNavigation: true,
        initialNavigationType: item.isHomework ? 'homework' : 'notes',
      ),
    );
  }
}
