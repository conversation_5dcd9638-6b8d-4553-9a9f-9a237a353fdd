import 'package:calendar_timeline/calendar_timeline.dart';
import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/screens/chat/search/search_parent.dart';
import 'package:chat_app/screens/chat/search/widgets/filter_dialog.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:chat_app/widgets/custom_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';

class AbsenteesMessages extends StatefulWidget {
  final List<UserModel> students;
  const AbsenteesMessages({super.key, required this.students});

  @override
  State<AbsenteesMessages> createState() => _AbsenteesMessagesState();
}

class _AbsenteesMessagesState extends State<AbsenteesMessages> {
  late List<UserModel> _students = widget.students.toList();
  DateTime _dateTime = DateTime.now();
  bool _isLoading = false;
  List<String> _groupSelected = [];
  List<String> _batchSelected = [];
  List<String> _sectionSelected = [];
  List<UserModel> _originalUsers = [];
  List<UserModel> users = [];
  List<UserModel> _studentAdded = [];
  final GlobalKey<SearchUserAutoCompleteState> _searchUserAutoCompleteKey =
      GlobalKey();
  final LocalStorage _localStorage = LocalStorage();
  @override
  void initState() {
    _getTempUsers();
    _getStudentAdded();
    // _searchUser();
    super.initState();
  }

  _getTempUsers() async {
    _students = await _localStorage.getTempUsers(_getFortmatDate());
    setState(() {});
  }

  _getFortmatDate() {
    var date = Helper.formatDateTimeToString(
        NavigationService.instance.navigationKey!.currentContext!, _dateTime,
        newPattern: "dd-MM-yyyy");
    return date;
  }

  _getStudentAdded() async {
    _studentAdded.clear();
    setState(() {});
    var date = Helper.formatDateTimeToString(
        NavigationService.instance.navigationKey!.currentContext!, _dateTime,
        newPattern: "dd-MM-yyyy");
    var list = await FirestoreService.searchUser(
      fromLocal: true,
      type: AppConstants.studentType,
    );
    list = list
        .where((element) => element.dateOfSentAbsentesMessages == date)
        .toList();
    _studentAdded = list.toList();
    _originalUsers = list.toList();
    setState(() {});
  }

  // _searchUser({String? query, bool isFromLocal = true}) async {
  //   try {
  //     users = await FirestoreService.searchUser(
  //       fromLocal: isFromLocal,
  //       type: AppConstants.studentType,
  //     );
  //     if (_originalUsers.isEmpty) {
  //       _originalUsers = users.toList();
  //     }
  //   } catch (e) {
  //     showToast(e.toString());
  //   }
  // }

  _submit(String text) async {
    try {
      setState(() {
        _isLoading = true;
      });
      var profile = BlocProvider.of<AuthBloc>(context).state.userModel;
      var list = _filterStudents(_students);
      for (var user in list) {
        var message = text;
        var date = Helper.formatDateTimeToString(context, _dateTime,
            newPattern: "dd-MM-yyyy");
        message = message
            .replaceAll('[student]', '${user.name}')
            .replaceAll('[date]', date);
        await FirestoreService.sendActivity(
            titleNotification: "key_absenteees_messages".tr(),
            receiverId: user.docId!,
            sid: user.sid!,
            message: message,
            senderName: profile!.name ?? "",
            receiverName: user.name ?? "",
            senderId: "");
        await FirestoreService.updateUser(
            docId: user.docId!, data: {"date_of_sent_absentes_messages": date});
      }

      await FirestoreService.sendActivity(
          sendNotification: false,
          titleNotification: "key_absenteees_messages".tr(),
          receiverId: "",
          sid: "",
          message: "key_absentees_messages_sent_successfully".tr(),
          senderName: profile!.name ?? "",
          receiverName: "key_notifications".tr(),
          senderId: profile.docId!);
      await _localStorage.saveTempUsers([], _getFortmatDate());
      setState(() {
        _isLoading = false;
      });
      Helper.showDialogSuccessMessages(
          context: context,
          message: "key_success2".tr(),
          onPressPrimaryButton: () {
            Navigator.popUntil(context, (route) => route.isFirst);
          });
    } catch (e) {
      showToast(e.toString());
      setState(() {
        _isLoading = false;
      });
    }
  }

  _addUser(UserModel p0) async {
    if (_students.contains(p0) == false) {
      try {
        setState(() {
          _isLoading = true;
        });
        var user = await FirestoreService.getUserDetails(docId: p0.docId!);
        var date = Helper.formatDateTimeToString(context, _dateTime,
            newPattern: "dd-MM-yyyy");
        if (user?.dateOfSentAbsentesMessages == date) {
          throw PlatformException(
              code: '-1', message: "key_sent_an_absentees_messages_today".tr());
        }

        setState(() {
          _students.insert(0, p0);
          _isLoading = false;
        });
        await _localStorage.saveTempUsers(_students, _getFortmatDate());
      } on PlatformException catch (e) {
        setState(() {
          _isLoading = false;
        });
        Helper.showDialogSuccessMessages(context: context, message: e.message!);
      }
    }
  }

  List<UserModel> _filterStudents(List<UserModel> users) {
    var list = users.toList().where((element) {
      if (_groupSelected.isNotEmpty) {
        return _groupSelected.contains(element.prCGroupName);
      }
      return true;
    }).where((element) {
      if (_batchSelected.isNotEmpty) {
        return _batchSelected.contains(element.prCourseName);
      }
      return true;
    }).where((element) {
      if (_sectionSelected.isNotEmpty) {
        return _sectionSelected.contains(element.prSectionName);
      }
      return true;
    }).toList();
    return list;
  }

  @override
  Widget build(BuildContext context) {
    var list = _studentAdded + _students;
    return BlocBuilder<AuthBloc, AuthState>(builder: (context, state) {
      return GestureDetector(
        onTap: () {
          hideKeyboard(context);
        },
        child: OverlayLoading(
          isLoading: _isLoading,
          child: Scaffold(
            appBar: CustomAppbar(
              title: "key_absenteees_messages".tr(),
            ),
            backgroundColor: AppColors.backgroundScaffold,
            floatingActionButton: list.isNotEmpty
                ? FloatingActionButton(
                    onPressed: () async {
                      var result = await showDialog(
                          context: context,
                          builder: (_) {
                            return FilterDialog(
                              users: list,
                              batchSelected: _batchSelected,
                              groupSelected: _groupSelected,
                              sectionSelected: _sectionSelected,
                            );
                          });
                      if (result != null) {
                        setState(() {
                          // _studentAdded = result['users'];
                          _groupSelected = result['groupSelected'];
                          _batchSelected = result['batchSelected'];
                          _sectionSelected = result['sectionSelected'];
                          _searchUserAutoCompleteKey.currentState?.updateFilter(
                              groupSelected: _groupSelected,
                              batchSelected: _batchSelected,
                              sectionSelected: _sectionSelected);
                        });
                      }
                    },
                    child: Icon(
                      Icons.filter_alt_rounded,
                      color: Colors.white,
                    ),
                  )
                : null,
            bottomNavigationBar: state.userModel?.role == 1
                ? Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 10),
                        child: ButtonCustom(
                          onPressed: () async {
                            final result = await showModalBottomSheet(
                                isScrollControlled: true,
                                backgroundColor: Colors.transparent,
                                context: context,
                                builder: (_) => SendMessageModal());
                            if (result != null) {
                              _submit(result);
                            }
                          },
                          enabled: _filterStudents(_students).isNotEmpty,
                          title: "key_send".tr(),
                        ),
                      ),
                    ],
                  )
                : null,
            body: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(bottom: 10),
                      child: CalendarTimeline(
                        // showYears: true,'
                        // shrink: true,
                        dayNameFontSize: 14,
                        fontSize: 20,
                        height: 70,
                        initialDate: _dateTime,
                        firstDate: DateTime(1900),
                        lastDate: _dateTime,
                        onDateSelected: (date) {
                          setState(() {
                            _dateTime = date;
                            // _students.clear();
                          });

                          _getStudentAdded();
                          _getTempUsers();
                        },

                        leftMargin: 150,
                        monthColor: Colors.blueGrey,
                        dayColor: Colors.teal[200],
                        activeDayColor: Colors.white,
                        activeBackgroundDayColor: AppColors.primary,
                        // dotsColor: Color(0xFF333A47),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: SearchUserAutoComplete(
                        onSubmitted: _addUser,
                        key: _searchUserAutoCompleteKey,
                        batchSelected: _batchSelected,
                        groupSelected: _groupSelected,
                        sectionSelected: _sectionSelected,
                        onSelected: _addUser,
                      ),
                    ),
                    Builder(builder: (context) {
                      var list = _filterStudents(_students);
                      return Wrap(
                        spacing: 10,
                        runSpacing: 10,
                        children: List.generate(list.length, (index) {
                          var user = list[index];
                          return _item(context, index, user, false);
                        }),
                      );
                    }),
                    10.h,
                    Builder(builder: (context) {
                      var list = _filterStudents(_studentAdded);
                      return Wrap(
                        spacing: 10,
                        runSpacing: 10,
                        children: List.generate(list.length, (index) {
                          var user = list[index];
                          return _item(context, index, user, true);
                        }),
                      );
                    }),
                  ],
                )),
          ),
        ),
      );
    });
  }

  Container _item(
      BuildContext context, int index, UserModel user, bool isHistory) {
    return Container(
      // height: 70,
      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        // mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: () {
              Helper.showActionDialog(
                  context: context,
                  title: "",
                  message: "key_are_you_sure_remove_this_student".tr(),
                  onClose: () {},
                  onConfirm: () async {
                    _students.removeAt(index);

                    setState(() {});
                    await _localStorage.saveTempUsers(
                        _students, _getFortmatDate());
                  });
            },
            child: Padding(
              padding: const EdgeInsets.only(right: 4),
              child: Icon(
                isHistory ? Icons.history_outlined : Icons.remove_circle,
                color: AppColors.primary,
              ),
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "${user.name}",
                      style: AppStyles.textSize14(),
                    ),
                    Text(
                      "ID: ${user.sid}",
                      style: AppStyles.textSize14(),
                    ),
                  ],
                ),
                4.h,
                Row(
                  children: [
                    Text(
                      "${user.prCGroupName} - ${user.prCourseName} - ${user.prSectionName}",
                      style: AppStyles.textSize12(),
                    ),
                  ],
                ),
                // 4.h,
                // if (user.dateOfSentAbsentesMessages?.isNotEmpty ?? false)
                //   Text(
                //     "${"key_sent".tr()}: ${user.dateOfSentAbsentesMessages}",
                //     style: AppStyles.textSize12(color: AppColors.primary),
                //   )
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class SendMessageModal extends StatefulWidget {
  const SendMessageModal({super.key});

  @override
  State<SendMessageModal> createState() => _SendMessageModalState();
}

class _SendMessageModalState extends State<SendMessageModal> {
  final TextEditingController _messageController = TextEditingController();
  @override
  void initState() {
    var profile = BlocProvider.of<AuthBloc>(context).state.userModel;
    _messageController.text = """Dear parent of [student],

I hope this message finds you well. I am writing to inform you that your son, [student], was absent on [date]. I wanted to bring this to your attention, as regular attendance is crucial for academic progress.

If there are any specific reasons for his absence or if there is any way I can support [student] in catching up on missed work, please feel free to reach out. It is important for us to work together to ensure he doesn't fall behind.

Thank you for your understanding, and I appreciate your cooperation in addressing this matter. If there are any concerns or if you require further information, please don't hesitate to contact me.

Best regards,

${profile!.name}
${BlocProvider.of<AuthBloc>(context).state.schoolName}""";
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(5),
          topRight: Radius.circular(5),
        ),
        color: AppColors.black,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: getHeight(context) * 0.6,
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: TextField(
                onChanged: (value) {
                  setState(() {});
                },
                minLines: 5,
                style: AppStyles.textSize16(),
                maxLines: 100,
                controller: _messageController,
                decoration: InputDecoration(
                  hintText: "key_type_something".tr(),
                ),
              ),
            ),
          ),
          Text(
            "key_student_and_date_will_be_replace_note".tr(),
            style: AppStyles.textSize12().copyWith(),
          ),
          16.h,
          ButtonCustom(
            onPressed: () {
              pop(context, result: _messageController.text);
            },
            enabled: _messageController.text.isNotEmpty,
            title: "key_send".tr(),
          )
        ],
      ),
    );
  }
}

class SearchUserAutoComplete extends StatefulWidget {
  final Function(UserModel)? onSelected;
  final List<String> groupSelected;
  final List<String> batchSelected;
  final List<String> sectionSelected;
  final SuggestionsController<UserModel>? suggestionsController;
  final Function(UserModel)? onSubmitted;
  const SearchUserAutoComplete(
      {super.key,
      this.onSelected,
      required this.groupSelected,
      required this.batchSelected,
      required this.sectionSelected,
      this.suggestionsController,
      this.onSubmitted});

  @override
  State<SearchUserAutoComplete> createState() => SearchUserAutoCompleteState();
}

class SearchUserAutoCompleteState extends State<SearchUserAutoComplete> {
  TextEditingController? _textEditingController;
  late List<String> groupSelected = widget.groupSelected;
  late List<String> batchSelected = widget.batchSelected;
  late List<String> sectionSelected = widget.sectionSelected;
  List<UserModel> _users = [];
  updateFilter({
    required List<String> groupSelected,
    required List<String> batchSelected,
    required List<String> sectionSelected,
  }) {
    setState(() {
      this.groupSelected = groupSelected;
      this.batchSelected = batchSelected;
      this.sectionSelected = sectionSelected;
    });
  }

  @override
  Widget build(BuildContext context) {
    return TypeAheadField<UserModel>(
      decorationBuilder: (context, child) {
        return Container(
          child: child,
          color: AppColors.black,
        );
      },
      suggestionsCallback: (search) async {
        if (search.isEmpty) {
          return [];
        }

        var list = await FirestoreService.searchUser();
        list = list.where((element) {
          if (widget.groupSelected.isNotEmpty) {
            return widget.groupSelected.contains(element.prCGroupName);
          }
          return true;
        }).where((element) {
          if (widget.batchSelected.isNotEmpty) {
            return widget.batchSelected.contains(element.prCourseName);
          }
          return true;
        }).where((element) {
          if (widget.sectionSelected.isNotEmpty) {
            return widget.sectionSelected.contains(element.prSectionName);
          }
          return true;
        }).toList();
        var sid = int.tryParse(search);
        if (sid != null) {
          list = list
              .where((element) => element.sid == search.toLowerCase())
              .toList();
          _users = list.toList();
          return list;
        }
        list = list
            .where((element) =>
                element.name?.toLowerCase().contains(search.toLowerCase()) ??
                false)
            .toList();
        _users = list.toList();
        return list;
      },
      emptyBuilder: (context) {
        return Container(
            color: AppColors.black,
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 16),
            child: Text(
              "key_no_users_found".tr(),
              style: AppStyles.textSize14(),
            ));
      },
      builder: (context, controller, focusNode) {
        _textEditingController = controller;
        return TextField(
            onSubmitted: (value) {
              var sid = int.tryParse(value);
              if (sid != null) {
                var index =
                    _users.indexWhere((element) => element.sid == value);
                if (index != -1) {
                  widget.onSubmitted?.call(_users[index]);
                }
              }
              controller.clear();
            },
            style: AppStyles.textSize16(),
            controller: controller,
            focusNode: focusNode,
            // autofocus: true,

            decoration: InputDecoration(
                focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.primary)),
                enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.primary)),
                border: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.primary)),
                hintText: 'key_search_student_id_or_name'.tr(),
                hintStyle: AppStyles.textSize16()));
      },
      itemBuilder: (context, user) {
        return Container(
          child: IgnorePointer(
            child: UserItem(
              onTap: () {},
              showPhoneCall: false,
              user: user,
              isSelectMulti: false,
              isSelected: false,
              showBirthdayWishes: false,
            ),
          ),
        );
      },
      suggestionsController: widget.suggestionsController,
      onSelected: (city) {
        _textEditingController?.clear();
        widget.onSelected?.call(city);
      },
    );
  }
}
