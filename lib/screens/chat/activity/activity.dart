import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/missing_widgets.dart';
import 'package:chat_app/models/chats/conversatoin_model.dart';

import 'package:chat_app/screens/chat/chat_details/widgets/play_image.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:chat_app/widgets/custom_appbar.dart';
import 'package:chat_app/widgets/pdf_thumbnail.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:open_filex/open_filex.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

class Activity extends StatefulWidget {
  const Activity({super.key});

  @override
  State<Activity> createState() => _ActivityState();
}

class _ActivityState extends State<Activity> {
  bool _isLoading = false;
  @override
  void initState() {
    _init();
    super.initState();
  }

  _init() async {
    if (BlocProvider.of<AuthBloc>(context).state.userModel?.type ==
        AppConstants.parentType) {
      await FirestoreService.markReadAllActivity(
          BlocProvider.of<AuthBloc>(context).state.students!.first.sid!);
    }
  }

  _generateReceipt(ActivityModel model) async {
    try {
      setState(() {
        _isLoading = true;
      });
      await Helper.downloadMedia(
          link: "${model.metadata!['receipt_path']}",
          onCompleted: (path) {
            setState(() {
              _isLoading = false;
            });
            OpenFilex.open(path);
          },
          onError: (error) {
            setState(() {
              _isLoading = false;
            });
          });
      // await Helper.generatePdfFromOrder(
      //     schoolName: model.metadata!['school_name'],
      //     orderId: model.metadata!['order_id'],
      //     schoolCode: model.metadata!['school_code']);
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      showToast(e.toString());
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    var user = BlocProvider.of<AuthBloc>(context).state.userModel;
    bool isTeacher = user?.type == AppConstants.teacherType;
    return BlocBuilder<AuthBloc, AuthState>(builder: (context, state) {
      return OverlayLoading(
        isLoading: _isLoading,
        child: Scaffold(
            // floatingActionButton: FloatingActionButton(onPressed: () async {
            //   await FirestoreService.instance.collection("activities").add({
            //     "created_at": DateTime.now().toUtc().toIso8601String(),
            //     "message": "test ok!!",
            //     "read_at": null,
            //     "receiver_id": "3uudlAFUYiPSDMGNLzQ1",
            //     "sender_id": "BUr4UK4R0E8DJouWNAeD",
            //     "sender_name": "Abc",
            //     "receiver_name": "ZZZ"
            //   });
            // }),
            backgroundColor: AppColors.backgroundScaffold,
            appBar: CustomAppbar(title: "key_notificiation_and_reminder".tr()),
            body: Builder(builder: (_) {
              if (isTeacher) {
                return ListActivity(
                  onViewReceipt: _generateReceipt,
                  isTeacher: isTeacher,
                  userId: user!.docId!,
                );
              }
              if (state.students?.length == 1) {
                return ListActivity(
                    onViewReceipt: _generateReceipt,
                    userId: state.students!.first.sid!,
                    isTeacher: isTeacher);
              }
              return DefaultTabController(
                length: state.students!.length,
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: TabBar(
                          onTap: (value) async {
                            var user = state.students![value];
                            await FirestoreService.markReadAllActivity(
                                user.sid!);
                          },
                          tabs: state.students!
                              .map((e) => Tab(
                                    text: e.name,
                                  ))
                              .toList()),
                    ),
                    Expanded(
                      child: TabBarView(
                          children: state.students!
                              .map((e) => ListActivity(
                                    onViewReceipt: _generateReceipt,
                                    isTeacher: isTeacher,
                                    userId: e.sid!,
                                  ))
                              .toList()),
                    ),
                  ],
                ),
              );
            })),
      );
    });
  }
}

class ListActivity extends StatelessWidget {
  final bool isTeacher;
  final String userId;
  final Function(ActivityModel activity) onViewReceipt;
  const ListActivity(
      {super.key,
      required this.isTeacher,
      required this.userId,
      required this.onViewReceipt});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<ActivityModel>>(
      stream: isTeacher
          ? FirestoreService.getActivityStream(senderId: userId)
          : FirestoreService.getActivityStreamStudent(receiverId: userId),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          if (snapshot.data!.isEmpty) {
            return Center(
              child: Text(
                "key_empty".tr(),
                style: AppStyles.textSize16(),
              ),
            );
          }
          var list = snapshot.data!;
          return ListView.separated(
              padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
              itemBuilder: (_, index) {
                var activity = list[index];
                return Container(
                  padding: EdgeInsets.symmetric(vertical: 12, horizontal: 10),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    color: AppColors.white.withOpacity(0.1),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          if (isTeacher == false && activity.readAt == null)
                            Container(
                              margin: EdgeInsets.only(right: 4),
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: AppColors.primary,
                                shape: BoxShape.circle,
                              ),
                            ),
                          Expanded(
                            child: Text(
                              !isTeacher
                                  ? "${activity.senderName}"
                                  : "${activity.receiverName}",
                              style: AppStyles.textSize14(
                                color: AppColors.primary,
                              ),
                            ),
                          ),
                          Text(
                            Helper.formatDateTime("${activity.createdAt}",
                                fomat: "dd/MM HH:mm", isHumanReadable: true),
                            style: AppStyles.textSize12(),
                          ),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Builder(builder: (context) {
                          var message = "${activity.message}";
                          if (isUrl(message)) {
                            if (isImageLink(message)) {
                              return GestureDetector(
                                onTap: () {
                                  push(
                                      context, PlayImage(imageUrl: "$message"));
                                },
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(4),
                                  child: CachedNetworkImage(
                                    imageUrl: "$message",
                                    width: getWidth(context),
                                    height: 200,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              );
                            }

                            if (getLinkType(message) == "pdf") {
                              return FutureBuilder<File>(
                                future: DefaultCacheManager()
                                    .getSingleFile(message),
                                builder: (context, snapshot) => snapshot.hasData
                                    ? GestureDetector(
                                        onTap: () {
                                          launchUrlString(message);
                                        },
                                        child: Stack(
                                          children: [
                                            PdfThumbnail.fromFile(
                                              snapshot.data!.path,
                                              currentPage: 1,
                                              height: 150,
                                              backgroundColor:
                                                  Colors.transparent,
                                              scrollToCurrentPage: false,
                                            ),
                                            Container(
                                              color: Colors.transparent,
                                              height: 150,
                                              width: getWidth(context) * 0.7,
                                            ),
                                          ],
                                        ),
                                      )
                                    : Container(
                                        child: Center(
                                            child:
                                                CupertinoActivityIndicator()),
                                      ),
                              );
                            }
                            return LinkPreviewMessage(
                                chatModel: ChatModel(),
                                linkErrorTextStyle: 16,
                                backgroundColor: Colors.transparent,
                                index: index,
                                onLongPress: (index) {},
                                link: message);
                          }
                          if (message.startsWith("upi://pay")) {
                            var linkContent =
                                "key_dear_parent_payment_link_content".tr();
                            if (activity.linkContent?.isNotEmpty ?? false) {
                              linkContent = activity.linkContent!;
                            }
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Text(
                                //   linkContent,
                                //   style: AppStyles.textSize14(),
                                // ),
                                HtmlTextCustom(message: linkContent),
                                Divider(
                                  height: 16,
                                  color: AppColors.white,
                                ),
                                GestureDetector(
                                  onTap: () {
                                    String link = message;
                                    if (link.startsWith("upi")) {
                                      Uri uri = Uri.parse(link);
                                      Map<String, dynamic> params =
                                          Map.from(uri.queryParameters);
                                      String tr = params['tr'];
                                      var randomNumber = tr.split("_").last;
                                      randomNumber =
                                          Helper.getRandomNumber().toString();

                                      tr = tr.replaceAll(
                                          tr.split("_").last, randomNumber);
                                      params.addAll({"tr": tr, "tn": tr});
                                      uri =
                                          uri.replace(queryParameters: params);
                                      launchUrl(uri);
                                    } else {
                                      launchUrl(Uri.parse(link),
                                          mode: LaunchMode.externalApplication);
                                    }
                                  },
                                  child: Container(
                                    color: Colors.transparent,
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(right: 4),
                                          child: Icon(
                                            Icons.open_in_new,
                                            size: 16,
                                            color: AppColors.primary,
                                          ),
                                        ),
                                        Text(
                                          "Pay now",
                                          style: AppStyles.textSize14(
                                              fontWeight: FontWeight.w500,
                                              color: AppColors.primary),
                                        )
                                      ],
                                    ),
                                  ),
                                )
                              ],
                            );
                          }
                          if (containsHtmlTags(message)) {
                            return HtmlTextCustom(message: message);
                          }

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${activity.message}",
                                style: AppStyles.textSize16(),
                              ),
                              if (activity.type ==
                                      AppConstants.transactionActivityType &&
                                  activity.metadata != null)
                                Center(
                                  child: Padding(
                                    padding: const EdgeInsets.only(top: 8),
                                    child: SizedBox(
                                      width: 100,
                                      height: 35,
                                      child: ButtonCustom(
                                        padding:
                                            EdgeInsets.symmetric(vertical: 4),
                                        textStyle: AppStyles.textSize12(),
                                        onPressed: () {
                                          // launchUrlString(activity
                                          //     .metadata!['receipt_path']);
                                          onViewReceipt(activity);
                                        },
                                        title: "key_view_receipt".tr(),
                                      ),
                                    ),
                                  ),
                                )
                            ],
                          );
                          // return HtmlTextCustom(message: message);
                        }),
                      ),
                    ],
                  ),
                );
              },
              separatorBuilder: (_, index) {
                return 10.h;
              },
              itemCount: list.length);
        }
        if (snapshot.hasError) {
          return Center(
            child: Text(
              snapshot.error.toString(),
              style: AppStyles.textSize14(),
            ),
          );
        }
        return Container();
      },
    );
  }
}

class HtmlTextCustom extends StatelessWidget {
  const HtmlTextCustom({
    super.key,
    required this.message,
  });

  final String message;

  @override
  Widget build(BuildContext context) {
    return HtmlWidget(message, onTapUrl: (url) {
      return launchUrlString(url);
    }, textStyle: AppStyles.textSize14());
  }
}
