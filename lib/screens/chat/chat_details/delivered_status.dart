import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/missing_widgets.dart';
import 'package:chat_app/models/chats/conversatoin_model.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../utils/app_colors.dart';

class DeliveredStatus extends StatefulWidget {
  final ChatModel chatModel;
  final ConversationModel conversationModel;
  const DeliveredStatus(
      {super.key, required this.chatModel, required this.conversationModel});

  @override
  State<DeliveredStatus> createState() => _DeliveredStatusState();
}

class _DeliveredStatusState extends State<DeliveredStatus> {
  @override
  Widget build(BuildContext context) {
    var users = widget.conversationModel.users ?? [];
    return BlocBuilder<AuthBloc, AuthState>(builder: (context, state) {
      return Scaffold(
        backgroundColor: AppColors.backgroundScaffold,
        appBar: CustomAppbar(
          title: "key_delivered_status".tr(),
        ),
        body: Column(
            children: List.generate(users.length, (index) {
          var user = users[index];
          if (state.userModel?.docId == user.docId) {
            return Container();
          }
          bool isReaded =
              widget.chatModel.readBy!.contains(user.parentId ?? "");
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  margin: EdgeInsets.only(right: 10),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey.withOpacity(0.5),
                  ),
                  child: Icon(
                    Icons.person,
                    color: AppColors.white,
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "${user.name}",
                        style: AppStyles.textSize14(color: AppColors.white),
                      ),
                      // Padding(
                      //   padding: const EdgeInsets.only(top: 4),
                      //   child: Text(
                      //     "${user.prCGroupName} - ${user.prCourseName} - ${user.prSectionName}",
                      //     style: AppStyles.textSize12(),
                      //   ),
                      // ),
                    ],
                  ),
                ),
                MarkIcon(
                  isRead: isReaded,
                ),
              ],
            ),
          );
        })),
      );
    });
  }
}
