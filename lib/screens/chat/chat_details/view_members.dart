import 'package:chat_app/export.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/models/chats/conversatoin_model.dart';
import 'package:chat_app/utils/firestore_service.dart';

import 'package:flutter/material.dart';

import '../../../utils/app_colors.dart';
import '../search/search_parent.dart';

class ViewMembers extends StatefulWidget {
  final String conversationId;
  const ViewMembers({super.key, required this.conversationId});

  @override
  State<ViewMembers> createState() => _ViewMembersState();
}

class _ViewMembersState extends State<ViewMembers> {
  ConversationModel? _conversationModel;
  bool _isLoading = false;
  List<UserModel> _users = [];
  String _searchQuery = "";
  final TextEditingController _searchController = TextEditingController();
  @override
  void initState() {
    _init();
    super.initState();
  }

  _init() async {
    try {
      setState(() {
        _isLoading = true;
      });
      _conversationModel =
          await FirestoreService.getConversationDetails(widget.conversationId);
      _users = _conversationModel!.users?.toList() ?? [];
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  _removeMember(String userId) async {
    Helper.showActionDialog(
        context: context,
        title: "key_notifications".tr(),
        message: "do_you_want_to_remove_this_member".tr(),
        onClose: () {},
        onConfirm: () async {
          try {
            setState(() {
              _isLoading = true;
            });
            _conversationModel = await FirestoreService.removePeopleInGroup(
                conversationId: widget.conversationId, userId: userId);
            setState(() {
              _isLoading = false;
            });
            showToast("key_success2".tr());
          } catch (e) {
            setState(() {
              _isLoading = false;
            });
            showToast(e.toString());
          }
        });
  }

  @override
  Widget build(BuildContext context) {
    return OverlayLoading(
      isLoading: _isLoading,
      child: Scaffold(
        backgroundColor: AppColors.backgroundScaffold,
        appBar: CustomAppbar(
          title: "key_members".tr(),
          actions: [
            IconButton(
                onPressed: () async {
                  var result = await push(context, SearchParent(users: []));
                  if (result != null) {
                    try {
                      setState(() {
                        _isLoading = true;
                      });
                      await FirestoreService.addMembersToConversation(
                          members: result,
                          conversationId: _conversationModel!.id!);
                      await _init();
                      setState(() {
                        _isLoading = false;
                      });
                    } catch (e) {
                      setState(() {
                        _isLoading = false;
                      });
                      showToast(e.toString());
                    }
                  }
                },
                icon: Icon(Icons.add))
          ],
        ),
        body: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: TextField(
                  controller: _searchController,
                  onChanged: (value) {
                    // if (value.isEmpty) {
                    //   _init();
                    // } else {
                    //   _users = _users.where((element) {
                    //     return (element.contactOne
                    //                 ?.toString()
                    //                 .toLowerCase()
                    //                 .contains(value.toLowerCase()) ??
                    //             false) ||
                    //         (element.contactTwo
                    //                 ?.toString()
                    //                 .toLowerCase()
                    //                 .contains(value.toLowerCase()) ??
                    //             false) ||
                    //         (element.whatsappNumber
                    //                 ?.toString()
                    //                 .toLowerCase()
                    //                 .contains(value.toLowerCase()) ??
                    //             false);
                    //   }).toList();
                    // }
                    setState(() {
                      _searchQuery = value.toLowerCase();
                    });
                  },
                  style: AppStyles.textSize14(color: AppColors.black),
                  decoration: InputDecoration(
                    prefixIcon: Icon(
                      Icons.search,
                      color: AppColors.primary,
                    ),
                    suffixIcon: TextButton(
                        child: Text("key_search".tr()),
                        onPressed: () {
                          hideKeyboard(context);
                          // _searchUser(query: _searchController.text);
                        }),
                    // contentPadding:
                    //     EdgeInsets.symmetric(vertical: 4, horizontal: 10),
                    border: InputBorder.none,
                    filled: true,
                    constraints: BoxConstraints(maxHeight: 45),

                    hintStyle: AppStyles.textSize14(color: AppColors.black),
                    hintText: "key_search_hint".tr(),
                    fillColor: AppColors.white.withOpacity(1),
                  ),
                ),
              ),
            ),
            Expanded(
              child: Builder(builder: (context) {
                if (_conversationModel != null) {
                  var _searchUsers = _users.toList();
                  if (_searchQuery.isNotEmpty) {
                    _searchUsers = _users.where((element) {
                      return (element.contactOne
                                  ?.toString()
                                  .toLowerCase()
                                  .contains(_searchQuery) ??
                              false) ||
                          (element.contactTwo
                                  ?.toString()
                                  .toLowerCase()
                                  .contains(_searchQuery) ??
                              false) ||
                          (element.whatsappNumber
                                  ?.toString()
                                  .toLowerCase()
                                  .contains(_searchQuery) ??
                              false) ||
                          (element.name
                                  ?.toString()
                                  .toLowerCase()
                                  .contains(_searchQuery.toLowerCase()) ??
                              false) ||
                          (element.sid
                                  ?.toString()
                                  .toLowerCase()
                                  .contains(_searchQuery.toLowerCase()) ??
                              false);
                    }).toList();
                  }
                  return ListView.separated(
                      itemBuilder: (_, index) {
                        var user = _searchUsers[index];
                        bool isMe = getProfile(context).docId == user.docId;
                        return Row(
                          children: [
                            Expanded(
                              child: UserItem(
                                  showPhoneCall: false,
                                  onTap: () {},
                                  user: user,
                                  isSelectMulti: false,
                                  isSelected: false),
                            ),
                            if (!isMe)
                              IconButton(
                                onPressed: () {
                                  _removeMember(user.docId ?? "");
                                },
                                icon: const Icon(Icons.delete),
                              )
                          ],
                        );
                      },
                      separatorBuilder: (_, index) {
                        return Divider(
                          height: 1,
                          color: AppColors.white,
                        );
                      },
                      itemCount: _searchUsers.length);
                }
                return Container();
              }),
            ),
          ],
        ),
      ),
    );
  }
}
