import 'package:chat_app/screens/chat/chat_details/widgets/chat_attachment_modal.dart';
import 'package:flutter/material.dart';

import '../../../../export.dart';
import '../../../../utils/app_colors.dart';

class MessageOptionsModal extends StatelessWidget {
  final bool isTeacher;
  final bool canDelete;
  final bool isGroupChat;
  final bool fromMe;
  const MessageOptionsModal(
      {super.key,
      required this.isTeacher,
      required this.isGroupChat,
      required this.canDelete,
      required this.fromMe});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(5),
          topRight: Radius.circular(5),
        ),
        color: AppColors.black,
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isTeacher && isGroupChat)
              Column(
                children: [
                  GestureDetector(
                    onTap: () async {
                      pop(context, result: 'delivered_status');
                    },
                    child: Item(
                      title: "key_delivered_status".tr(),
                      iconPath: AppAssets.markReadAllIcon,
                    ),
                  ),
                  Divider(
                    height: 10,
                  ),
                ],
              ),
            GestureDetector(
              onTap: () async {
                pop(context, result: 'share');
              },
              child: Item(
                title: "key_share".tr(),
                iconPath: AppAssets.shareIcon,
              ),
            ),
            Divider(
              height: 10,
            ),
            if (isTeacher)
              GestureDetector(
                onTap: () async {
                  pop(context, result: 'forward');
                },
                child: Item(
                  title: "key_forward".tr(),
                  iconPath: AppAssets.forwardIcon,
                ),
              ),
            if (canDelete)
              Divider(
                height: 10,
              ),
            if (canDelete)
              GestureDetector(
                onTap: () async {
                  pop(context, result: 'delete');
                },
                child: Item(
                  title: "key_delete".tr(),
                  iconPath: AppAssets.deleteIcon,
                ),
              ),
            if (!fromMe)
              Column(
                children: [
                  Divider(
                    height: 10,
                  ),
                  if (isTeacher)
                    GestureDetector(
                      onTap: () async {
                        pop(context, result: 'reply');
                      },
                      child: Item(
                        title: "key_reply".tr(),
                        iconPath: AppAssets.forwardIcon,
                      ),
                    ),
                ],
              ),
            if (isTeacher)
              Column(
                children: [
                  Divider(
                    height: 10,
                  ),
                  if (isTeacher)
                    GestureDetector(
                      onTap: () async {
                        pop(context, result: 'set_as_homework');
                      },
                      child: Item(
                        title: "Set as Homework",
                        iconPath: AppAssets.homeworkIcon,
                      ),
                    ),
                ],
              ),
            if (isTeacher)
              Column(
                children: [
                  Divider(
                    height: 10,
                  ),
                  if (isTeacher)
                    GestureDetector(
                      onTap: () async {
                        pop(context, result: 'set_as_notes');
                      },
                      child: Item(
                        title: "Set as Notes",
                        iconPath: AppAssets.documentIcon,
                      ),
                    ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
