import 'package:cached_network_image/cached_network_image.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/placeholder.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../blocs/auth_bloc/auth_bloc.dart';
import '../../../../models/chats/conversatoin_model.dart';
import '../../../../utils/firestore_service.dart';
import '../../chat_page.dart';
import '../../search/search_parent.dart';

class ForwardMessageModal extends StatefulWidget {
  final ChatModel chatModel;
  const ForwardMessageModal({super.key, required this.chatModel});

  @override
  State<ForwardMessageModal> createState() => _ForwardMessageModalState();
}

class _ForwardMessageModalState extends State<ForwardMessageModal>
    with TickerProviderStateMixin {
  late final TabController _tabController =
      TabController(length: 2, vsync: this);
  List<ConversationModel> _selectedConversations = [];
  final GlobalKey<SearchParentState> _searchParentState =
      GlobalKey<SearchParentState>();
  bool _isLoading = false;
  _onSend() async {
    try {
      setState(() {
        _isLoading = true;
      });
      var user = BlocProvider.of<AuthBloc>(context).state.userModel;
      var chat = widget.chatModel.copyWith(
        createdAt: DateTime.now().toUtc().toIso8601String(),
        updatedAt: DateTime.now().toUtc().toIso8601String(),
      );
      if (_selectedConversations.isNotEmpty) {
        for (var e in _selectedConversations) {
          await FirestoreService.sendMessage(
              senderName: getProfile(context).name ?? "",
              conversationModel: e,
              message: chat.message ?? "",
              senderId: user!.docId!,
              chatModel: chat);
        }
      }
      var users = _searchParentState.currentState?.getSelectedUsers();
      if (users?.isNotEmpty ?? false) {
        for (var e in users!) {
          var id = await FirestoreService.checkConversationExist(users: [e]);
          if (id == null) {
            var room = await FirestoreService.createConversation(
                teacherId: user!.docId!,
                lastedMessage: chat.message ?? "",
                title: "${e.name}",
                users: [e]);
            if (mounted) {
              await FirestoreService.sendMessage(
                  senderName: getProfile(context).name ?? "",
                  conversationModel: room,
                  message: chat.message ?? "",
                  senderId: user.docId!,
                  chatModel: chat);
            }
          } else {
            var room = await FirestoreService.getConversationDetails(id);
            if (mounted) {
              await FirestoreService.sendMessage(
                  senderName: getProfile(context).name ?? "",
                  conversationModel: room,
                  message: chat.message ?? "",
                  senderId: user!.docId!,
                  chatModel: chat);
            }
          }
        }
      }
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        pop(context);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return OverlayLoading(
      isLoading: _isLoading,
      child: GestureDetector(
        onTap: () {
          hideKeyboard(context);
        },
        child: Container(
          height: getHeight(context) * 0.8,
          decoration: BoxDecoration(
            color: AppColors.black,
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(4), topRight: Radius.circular(4)),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                      onPressed: () {
                        pop(context);
                      },
                      child: Text("key_cancel".tr())),
                  Text(
                    "key_forward".tr(),
                    style: AppStyles.textSize16(fontWeight: FontWeight.w500),
                  ),
                  TextButton(onPressed: _onSend, child: Text("key_send".tr())),
                ],
              ),
              TabBar(controller: _tabController, tabs: [
                Tab(
                  text: "key_recent_chats".tr(),
                ),
                Tab(
                  text: "key_contact".tr(),
                ),
              ]),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    BlocBuilder<AuthBloc, AuthState>(builder: (context, state) {
                      if (state.userModel != null) {
                        bool isTeacher =
                            state.userModel?.type == AppConstants.teacherType;
                        return StreamBuilder<List<ConversationModel>>(
                            stream: FirestoreService.getConversationStream(
                                userId: isTeacher
                                    ? state.userModel!.docId!
                                    : state.students!.first.docId!,
                                isTeacher: isTeacher),
                            builder: (context, snapshot) {
                              if (snapshot.hasError) {
                                return Center(
                                  child: Text(
                                    snapshot.error.toString(),
                                    style: AppStyles.textSize14(),
                                  ),
                                );
                              }
                              if (snapshot.hasData) {
                                var list = snapshot.data!;
                                if (list.isEmpty) {
                                  return Center(
                                      child: Text(
                                    "key_empty".tr(),
                                    style: AppStyles.textSize14(),
                                  ));
                                }
                                return ListView.separated(
                                    itemBuilder: (_, index) {
                                      var conversationModel = list[index];
                                      bool isSelected = _selectedConversations
                                          .where((element) =>
                                              element.id ==
                                              conversationModel.id)
                                          .isNotEmpty;
                                      return InkWell(
                                        onTap: () {
                                          if (isSelected == false) {
                                            _selectedConversations
                                                .add(conversationModel);
                                          } else {
                                            _selectedConversations.removeWhere(
                                                (element) =>
                                                    element.id ==
                                                    conversationModel.id);
                                          }
                                          setState(() {});
                                        },
                                        child: Container(
                                          color: Colors.transparent,
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 10, vertical: 6),
                                          child: Row(
                                            children: [
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                    right: 10),
                                                child:
                                                    Builder(builder: (context) {
                                                  if (conversationModel.image !=
                                                      null) {
                                                    return Container(
                                                      margin: EdgeInsets.only(
                                                          right: 10),
                                                      child: ClipRRect(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(1000),
                                                        child:
                                                            CachedNetworkImage(
                                                          imageUrl:
                                                              "${conversationModel.image}",
                                                          width: 40,
                                                          height: 40,
                                                          fit: BoxFit.cover,
                                                        ),
                                                      ),
                                                    );
                                                  }
                                                  return Container(
                                                    width: 40,
                                                    height: 40,
                                                    margin: EdgeInsets.only(
                                                        right: 10),
                                                    decoration: BoxDecoration(
                                                      shape: BoxShape.circle,
                                                      color: Colors.grey
                                                          .withOpacity(0.5),
                                                    ),
                                                    child: Icon(
                                                      conversationModel.users
                                                                  ?.length ==
                                                              1
                                                          ? Icons.person
                                                          : Icons.people,
                                                      size: 20,
                                                      color: AppColors.white,
                                                    ),
                                                  );
                                                }),
                                              ),
                                              Expanded(
                                                child: Text(
                                                  "${conversationModel.title}",
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  style: AppStyles.textSize14(
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                              ),
                                              Checkbox(
                                                  value: isSelected,
                                                  onChanged: (value) {
                                                    if (value == true) {
                                                      _selectedConversations.add(
                                                          conversationModel);
                                                    } else {
                                                      _selectedConversations
                                                          .removeWhere((element) =>
                                                              element.id ==
                                                              conversationModel
                                                                  .id);
                                                    }
                                                    setState(() {});
                                                  })
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                    separatorBuilder: (_, index) {
                                      return Divider(
                                        height: 1,
                                        color: AppColors.white,
                                      );
                                    },
                                    itemCount: list.length);
                              }
                              return Center(
                                  child: Text(
                                "key_empty".tr(),
                                style: AppStyles.textSize14(),
                              ));
                            });
                      }
                      return Container();
                    }),
                    SearchParent(
                      key: _searchParentState,
                      showAppBar: false,
                      users: [],
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
