import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/missing_widgets.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/models/chats/conversatoin_model.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/list_conversations.dart';
import 'package:chat_app/screens/pay_now/checking_payment.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:chat_app/widgets/pdf_thumbnail.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:highlight_text/highlight_text.dart';

import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

class ReplyChatItem extends StatelessWidget {
  final UserModel? teacher;

  final String? query;
  final ChatModel chat;
  final Function() onReplyTap;

  const ReplyChatItem(
      {super.key,
      this.teacher,
      this.query,
      required this.chat,
      required this.onReplyTap});

  @override
  Widget build(BuildContext context) {
    bool fromMe = false;
    var teacherName = chat.senderName ?? "";

    return GestureDetector(
      onTap: onReplyTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 10),
        padding: const EdgeInsets.symmetric(
          vertical: 10,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: AppColors.primary.withOpacity(0.1),
        ),
        // height: 45,
        width: getWidth(context) * 0.6,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 0),
          child: Row(
            children: [
              Expanded(
                child: Builder(builder: (_) {
                  var message = chat.message;
                  if (chat.deletedAt != null) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 14, horizontal: 10),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.white),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: fromMe
                            ? CrossAxisAlignment.end
                            : CrossAxisAlignment.start,
                        children: [
                          TeacherNameWidget(
                            teacherName: teacherName,
                            fromMe: fromMe,
                          ),
                          Text(
                            "key_this_message_has_been_delete".tr(),
                            style: AppStyles.textSize14()
                                .copyWith(fontStyle: FontStyle.italic),
                          ),
                        ],
                      ),
                    );
                  }
                  if (message != null && isUrl(message)) {
                    var type = chat.type;
                    switch (type) {
                      case "image":
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(4),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Icon(
                                Icons.format_quote_rounded,
                                color: AppColors.primary,
                                size: 18,
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (teacherName.isNotEmpty)
                                    TeacherNameWidget(
                                        fromMe: fromMe,
                                        teacherName: teacherName),

                                  CachedNetworkImage(
                                    imageUrl: message,
                                    width: 50,
                                    height: 50,
                                    fit: BoxFit.cover,
                                  ),
                                  // if (chat.fileContent?.isNotEmpty ?? false)
                                  //   FileContent(
                                  //     fileContent: chat.fileContent,
                                  //   )
                                ],
                              ),
                            ],
                          ),
                        );
                      case "video":
                        return GestureDetector(
                          onLongPress: () {},
                          onTap: () {
                            // push(
                            //     context,
                            //     PlayVideo(
                            //       videoUrl: chat.message!,
                            //       localPath: chat.localPath,
                            //     ));
                          },
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(4),
                            child: Column(
                              children: [
                                if (teacherName.isNotEmpty)
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 4, horizontal: 4),
                                    width: getWidth(context) * 0.4,
                                    color: AppColors.white.withOpacity(0.05),
                                    child: TeacherNameWidget(
                                        fromMe: fromMe,
                                        teacherName: teacherName),
                                  ),
                                Stack(
                                  children: [
                                    CachedNetworkImage(
                                      imageUrl: chat.fileThumbnail ?? "",
                                      width: getWidth(context) * 0.4,
                                      fit: BoxFit.fitWidth,
                                    ),
                                    const Positioned.fill(
                                      child: Center(
                                        child: Icon(
                                          Icons.play_circle_fill_rounded,
                                          color: Colors.white,
                                          size: 30,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                FileContent(
                                  fileContent: chat.fileContent,
                                )
                              ],
                            ),
                          ),
                        );
                      default:
                        if (isUrl(chat.message ?? "") &&
                            Helper.getTypeOfMessage(chat.message ?? "") ==
                                null) {
                          return LinkPreviewMessage(
                              chatModel: chat,
                              index: 0,
                              onLongPress: (_) {},
                              link: "${chat.message}");
                        }
                        if (chat.fileSize == null) {
                          return GestureDetector(
                            onTap: () {
                              launchUrlString(chat.message!);
                            },
                            child: Container(
                              width: getWidth(context) * 0.7,
                              padding: const EdgeInsets.symmetric(
                                  vertical: 14, horizontal: 10),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                "${chat.message}",
                                style: AppStyles.textSize14().copyWith(
                                    fontStyle: FontStyle.italic,
                                    color: AppColors.primary,
                                    decoration: TextDecoration.underline),
                              ),
                            ),
                          );
                        }
                        return GestureDetector(
                          child: SizedBox(
                            width: getWidth(context) * 0.7,
                            child: Container(
                                // margin: EdgeInsets.only(left: 4, right: 4),
                                padding: const EdgeInsets.symmetric(
                                    vertical: 8, horizontal: 10),
                                decoration: BoxDecoration(
                                  // color: AppColors.white.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    if (teacherName.isNotEmpty)
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 4),
                                        child: TeacherNameWidget(
                                            fromMe: fromMe,
                                            teacherName: teacherName),
                                      ),
                                    Builder(builder: (_) {
                                      if (chat.message
                                              ?.split(".")
                                              .last
                                              .startsWith("pdf") ??
                                          false) {
                                        return FutureBuilder<File>(
                                          future: DefaultCacheManager()
                                              .getSingleFile(chat.message!),
                                          builder: (context, snapshot) =>
                                              snapshot.hasData
                                                  ? Stack(
                                                      children: [
                                                        PdfThumbnail.fromFile(
                                                          snapshot.data!.path,
                                                          currentPage: 1,
                                                          height: 150,
                                                          backgroundColor:
                                                              Colors
                                                                  .transparent,
                                                          scrollToCurrentPage:
                                                              false,
                                                        ),
                                                        Container(
                                                          color: Colors
                                                              .transparent,
                                                          height: 150,
                                                          width: getWidth(
                                                                  context) *
                                                              0.7,
                                                        ),
                                                      ],
                                                    )
                                                  : Container(
                                                      child: const Center(
                                                          child:
                                                              CupertinoActivityIndicator()),
                                                    ),
                                        );
                                      }
                                      return Container();
                                    }),
                                    Row(
                                      children: [
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(right: 4),
                                          child: SvgPicture.asset(
                                            AppAssets.attachmentIcon,
                                            color: AppColors.primary,
                                          ),
                                        ),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                chat.fileName ?? "",
                                                style: AppStyles.textSize14(),
                                              ),
                                              if (chat.fileSize != null)
                                                Text(
                                                  Helper.formatFileSize(
                                                      chat.fileSize!),
                                                  style: AppStyles.textSize12(),
                                                ),
                                            ],
                                          ),
                                        ),
                                        Builder(builder: (context) {
                                          if (chat.localPath != null) {
                                            return Container(
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                color: AppColors.black
                                                    .withOpacity(0.2),
                                              ),
                                              width: 30,
                                              height: 30,
                                              child: const Icon(
                                                Icons.folder,
                                                size: 16,
                                                color: AppColors.primary,
                                              ),
                                            );
                                          }
                                          return const Icon(
                                            Icons.download,
                                            color: AppColors.primary,
                                          );
                                        })
                                      ],
                                    ),
                                    FileContent(
                                      fileContent: chat.fileContent,
                                    )
                                  ],
                                )),
                          ),
                        );
                    }
                  }
                  return GestureDetector(
                    child: Row(
                      // mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize:
                          fromMe ? MainAxisSize.min : MainAxisSize.max,
                      children: [
                        SizedBox(
                          width: getWidth(context) * 0.5,
                          child: Container(
                            // margin: EdgeInsets.only(left: 4, right: 4),
                            padding: const EdgeInsets.symmetric(
                                vertical: 12, horizontal: 10),
                            decoration: BoxDecoration(
                              // color: AppColors.white.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Icon(
                                  Icons.format_quote_rounded,
                                  color: AppColors.primary,
                                  size: 18,
                                ),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      if (teacherName.isNotEmpty)
                                        TeacherNameWidget(
                                            fromMe: fromMe,
                                            teacherName: teacherName),
                                      Builder(builder: (context) {
                                        if (containsHtmlTags(
                                            "${chat.message}")) {
                                          return HtmlWidget("${chat.message}",

                                              // all other parameters are optional, a few notable params:

                                              // specify custom styling for an element
                                              // see supported inline styling below
                                              customStylesBuilder: (element) {
                                            if (element.classes
                                                .contains('foo')) {
                                              return {'color': 'red'};
                                            }

                                            return null;
                                          },

                                              // this callback will be triggered when user taps a link
                                              onTapUrl: (url) {
                                            return launchUrlString(url);
                                          },

                                              // select the render mode for HTML body
                                              // by default, a simple `Column` is rendered
                                              // consider using `ListView` or `SliverList` for better performance
                                              renderMode: RenderMode.column,

                                              // set the default styling for text
                                              textStyle:
                                                  AppStyles.textSize14());
                                        }
                                        if (chat.linkButton != null &&
                                            isUrl(
                                                "${chat.linkButton!['link']}")) {
                                          return LinkPreviewMessage(
                                              chatModel: chat,
                                              backgroundColor:
                                                  Colors.transparent,
                                              index: 0,
                                              onLongPress: (_) {},
                                              link:
                                                  "${chat.linkButton!['link']}");
                                        }
                                        return TextHighlight(
                                          text:
                                              "${chat.message}", // You need to pass the string you want the highlights
                                          words: (query?.isNotEmpty ?? false)
                                              ? {
                                                  "$query": HighlightedWord(
                                                    onTap: () {},
                                                    textStyle:
                                                        AppStyles.textSize14(),
                                                    decoration: BoxDecoration(
                                                      color: Colors.yellow[800],
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              4),
                                                    ),
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        vertical: 1,
                                                        horizontal: 2),
                                                  ),
                                                }
                                              : {}, // Your dictionary words
                                          textStyle: AppStyles.textSize14(),
                                          // textAlign: TextAlign
                                          //     .justify, // You can use any attribute of the RichText widget
                                        );
                                      }),
                                      if (chat.linkButton != null)
                                        Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Divider(
                                              height: 16,
                                              color: AppColors.white,
                                            ),
                                            GestureDetector(
                                              onTap: () async {
                                                String link =
                                                    chat.linkButton!['link'];
                                                try {
                                                  if (link.startsWith("upi") ||
                                                      link.contains(
                                                          "feepayindia.in/opay/index.php")) {
                                                    Uri uri = Uri.parse(link);
                                                    Map<String, dynamic>
                                                        params = Map.from(uri
                                                            .queryParameters);
                                                    String tr = params['tr'];
                                                    var randomNumber =
                                                        tr.split("_").last;
                                                    randomNumber =
                                                        Helper.getRandomNumber()
                                                            .toString();

                                                    tr = tr.replaceAll(
                                                        tr.split("_").last,
                                                        randomNumber);
                                                    params.addAll(
                                                        {"tr": tr, "tn": tr});
                                                    uri = uri.replace(
                                                        queryParameters:
                                                            params);
                                                    var studentId =
                                                        tr.split('_')[1];
                                                    var user =
                                                        await FirestoreService
                                                            .getUserDetailsBySid(
                                                                sid: studentId);
                                                    if (user == null) {
                                                      throw Exception(
                                                          "User not found!");
                                                    }
                                                    await Helper
                                                        .processUpiPayment(
                                                            onSuccess: (res) {
                                                              replace(
                                                                  context,
                                                                  CheckingPayment(
                                                                      orderId: uri
                                                                              .queryParameters[
                                                                          'tn']!,
                                                                      student:
                                                                          user));
                                                            },
                                                            onError: (error) {
                                                              showToast(error);
                                                            },
                                                            deeplink:
                                                                uri.toString());
                                                  } else {
                                                    launchUrl(Uri.parse(link),
                                                        mode: LaunchMode
                                                            .externalApplication);
                                                  }
                                                } catch (e) {
                                                  launchUrl(Uri.parse(link),
                                                      mode: LaunchMode
                                                          .externalApplication);
                                                }
                                              },
                                              child: Container(
                                                color: Colors.transparent,
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    const Padding(
                                                      padding: EdgeInsets.only(
                                                          right: 4),
                                                      child: Icon(
                                                        Icons.open_in_new,
                                                        size: 16,
                                                        color:
                                                            AppColors.primary,
                                                      ),
                                                    ),
                                                    Text(
                                                      "${chat.linkButton!['label']}",
                                                      style:
                                                          AppStyles.textSize14(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              color: AppColors
                                                                  .primary),
                                                    )
                                                  ],
                                                ),
                                              ),
                                            )
                                          ],
                                        ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
