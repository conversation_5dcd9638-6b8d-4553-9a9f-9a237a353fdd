import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/helper.dart';
import 'package:chat_app/export.dart';
import 'package:url_launcher/url_launcher_string.dart';

class YouTubeVideoPreview extends StatelessWidget {
  final String youtubeUrl;
  final double? width;
  final double? height;

  const YouTubeVideoPreview({
    Key? key,
    required this.youtubeUrl,
    this.width,
    this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final thumbnailUrl = Helper.getYouTubeThumbnail(youtubeUrl);
    final videoId = Helper.getYouTubeVideoId(youtubeUrl);

    return GestureDetector(
      onTap: () {
        if (kIsWeb) {
          // On web, open YouTube in new tab
          launchUrlString(youtubeUrl);
        } else {
          // On mobile, try to open in YouTube app or browser
          launchUrlString(youtubeUrl);
        }
      },
      child: Container(
        width: width ?? getWidth(context) * 0.7,
        height: height ?? 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: AppColors.grey.withValues(alpha: 0.1),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            children: [
              // Thumbnail image
              if (thumbnailUrl != null)
                CachedNetworkImage(
                  imageUrl: thumbnailUrl,
                  width: double.infinity,
                  height: double.infinity,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => _buildLoadingThumbnail(),
                  errorWidget: (context, url, error) =>
                      _buildFallbackWithMediumQuality(),
                )
              else
                _buildFallbackThumbnail(),

              // Dark overlay for better visibility
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.3),
                    ],
                  ),
                ),
              ),

              // Play button overlay
              Center(
                child: Container(
                  width: 70,
                  height: 70,
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.9),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.play_arrow,
                    color: Colors.white,
                    size: 35,
                  ),
                ),
              ),

              // YouTube logo/badge
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.play_arrow,
                        color: Colors.white,
                        size: 14,
                      ),
                      const SizedBox(width: 2),
                      Text(
                        'YouTube',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 11,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Video duration placeholder (if we had access to YouTube API)
              // Positioned(
              //   bottom: 8,
              //   right: 8,
              //   child: Container(
              //     padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              //     decoration: BoxDecoration(
              //       color: Colors.black.withValues(alpha: 0.8),
              //       borderRadius: BorderRadius.circular(4),
              //     ),
              //     child: Text(
              //       '0:00', // Would need YouTube API to get actual duration
              //       style: TextStyle(
              //         color: Colors.white,
              //         fontSize: 11,
              //         fontWeight: FontWeight.w500,
              //       ),
              //     ),
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingThumbnail() {
    return Container(
      color: AppColors.grey.withValues(alpha: 0.3),
      child: const Center(
        child: CupertinoActivityIndicator(),
      ),
    );
  }

  Widget _buildFallbackWithMediumQuality() {
    // Try medium quality thumbnail as fallback
    final mediumThumbnailUrl = Helper.getYouTubeThumbnailMedium(youtubeUrl);

    if (mediumThumbnailUrl != null) {
      return CachedNetworkImage(
        imageUrl: mediumThumbnailUrl,
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
        placeholder: (context, url) => _buildLoadingThumbnail(),
        errorWidget: (context, url, error) => _buildFallbackThumbnail(),
      );
    }

    return _buildFallbackThumbnail();
  }

  Widget _buildFallbackThumbnail() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.red.withValues(alpha: 0.8),
            Colors.red.withValues(alpha: 0.6),
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.play_circle_fill,
            color: Colors.white,
            size: 50,
          ),
          const SizedBox(height: 8),
          Text(
            'YouTube Video',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Tap to play',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}
