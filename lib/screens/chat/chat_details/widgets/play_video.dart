import 'dart:io';

import 'package:chat_app/utils/app_colors.dart';
import 'package:chewie/chewie.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_player/video_player.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../../../export.dart';

class PlayVideo extends StatefulWidget {
  final String videoUrl;
  final String? localPath;
  const PlayVideo({Key? key, required this.videoUrl, this.localPath})
      : super(key: key);

  @override
  State<PlayVideo> createState() => _PlayVideoState();
}

class _PlayVideoState extends State<PlayVideo> {
  VideoPlayerController? _videoPlayerController;
  ChewieController? _chewieController;
  bool _isLoading = false;
  bool _hasError = false;
  String? _localPath;

  @override
  void initState() {
    super.initState();
    _localPath = widget.localPath;
    _initializeVideo();
  }

  Future<void> _initializeVideo() async {
    try {
      if (kIsWeb) {
        // For web, always use network URL
        _videoPlayerController = VideoPlayerController.networkUrl(
          Uri.parse(widget.videoUrl),
        );
      } else {
        // For mobile, use local path if available, otherwise network
        if (_localPath != null && await File(_localPath!).exists()) {
          _videoPlayerController =
              VideoPlayerController.file(File(_localPath!));
        } else {
          _videoPlayerController = VideoPlayerController.networkUrl(
            Uri.parse(widget.videoUrl),
          );
        }
      }

      await _videoPlayerController!.initialize();

      // Initialize Chewie controller with custom settings
      _chewieController = ChewieController(
        videoPlayerController: _videoPlayerController!,
        autoPlay: false,
        looping: false,
        allowFullScreen: true,
        allowMuting: true,
        allowPlaybackSpeedChanging: true,
        showControls: true,
        materialProgressColors: ChewieProgressColors(
          playedColor: AppColors.primary,
          handleColor: AppColors.primary,
          backgroundColor: Colors.grey.withValues(alpha: 0.3),
          bufferedColor: Colors.grey.withValues(alpha: 0.5),
        ),
        placeholder: Container(
          color: Colors.black,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
        errorBuilder: (context, errorMessage) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error,
                  color: Colors.white,
                  size: 42,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error playing video',
                  style: const TextStyle(color: Colors.white),
                ),
              ],
            ),
          );
        },
      );

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
    }
  }

  Future<void> _onDownload(String url) async {
    if (kIsWeb) {
      // For web, open video in new tab
      launchUrlString(url);
      return;
    }

    setState(() {
      _isLoading = true;
    });
    try {
      var ref = FirebaseStorage.instance.refFromURL(url);
      var bytes = await ref.getData();
      var dir = await getApplicationDocumentsDirectory();
      var file = File("${dir.path}/${ref.name}");
      await file.writeAsBytes(bytes!);
      setState(() {
        _localPath = file.path;
        _isLoading = false;
      });
      showToast("Downloaded");
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast("Download failed");
    }
  }

  @override
  void dispose() {
    _chewieController?.dispose();
    _videoPlayerController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return OverlayLoading(
      isLoading: _isLoading,
      child: Scaffold(
        backgroundColor: AppColors.backgroundScaffold,
        appBar: CustomAppbar(
          title: "",
          actions: [
            IconButton(
              onPressed: () async {
                if (kIsWeb) {
                  launchUrlString(widget.videoUrl);
                } else if (_localPath != null) {
                  OpenFilex.open(_localPath ?? "");
                } else {
                  _videoPlayerController?.pause();
                  await _onDownload(widget.videoUrl);
                }
              },
              icon: kIsWeb
                  ? Icon(
                      Icons.open_in_new,
                      color: AppColors.white,
                    )
                  : _localPath != null
                      ? Icon(
                          Icons.folder,
                          color: AppColors.white,
                        )
                      : Icon(
                          Icons.download,
                          color: AppColors.white,
                        ),
            )
          ],
        ),
        body: SafeArea(
          child: Center(
            child: _buildVideoPlayer(),
          ),
        ),
      ),
    );
  }

  Widget _buildVideoPlayer() {
    // if (_isLoading) {
    //   return const Center(child: CircularProgressIndicator());
    // }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              "Failed to load video",
              style: AppStyles.textSize16(color: AppColors.grey),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => launchUrlString(widget.videoUrl),
              child: const Text("Open in Browser"),
            ),
          ],
        ),
      );
    }

    if (_chewieController == null ||
        _videoPlayerController == null ||
        !_videoPlayerController!.value.isInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    return AspectRatio(
      aspectRatio: _videoPlayerController!.value.aspectRatio,
      child: Chewie(controller: _chewieController!),
    );
  }
}
