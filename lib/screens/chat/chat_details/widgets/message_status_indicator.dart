import 'package:flutter/material.dart';
import '../../../../export.dart';
import '../../../../utils/app_colors.dart';
import '../../../../utils/app_styles.dart';

class MessageStatusIndicator extends StatelessWidget {
  final bool? isPending;
  final bool? isFailed;
  final bool fromMe;
  final VoidCallback? onRetry;

  const MessageStatusIndicator({
    super.key,
    this.isPending,
    this.isFailed,
    required this.fromMe,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    if (!fromMe) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (isPending == true) _buildPendingIndicator(),
          if (isFailed == true) _buildFailedIndicator(),
        ],
      ),
    );
  }

  Widget _buildPendingIndicator() {
    return Container(
      padding: const EdgeInsets.all(4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 12,
            height: 12,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                AppColors.grey.withOpacity(0.7),
              ),
            ),
          ),
          const SizedBox(width: 6),
          Text(
            "Sending...",
            style: AppStyles.textSize10().copyWith(
              color: AppColors.grey,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFailedIndicator() {
    return GestureDetector(
      onTap: onRetry,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.red.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: 12,
              color: Colors.red,
            ),
            const SizedBox(width: 4),
            Text(
              "Failed • Tap to retry",
              style: AppStyles.textSize10().copyWith(
                color: Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
