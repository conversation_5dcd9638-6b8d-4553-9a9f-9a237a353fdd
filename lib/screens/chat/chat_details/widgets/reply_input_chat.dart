import 'package:cached_network_image/cached_network_image.dart';
import 'package:chat_app/models/chats/conversatoin_model.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/app_styles.dart';
import 'package:chat_app/utils/helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';

class ReplyInputChat extends StatelessWidget {
  final ChatModel replyModel;
  final Function() onClearReply;
  const ReplyInputChat(
      {super.key, required this.replyModel, required this.onClearReply});

  @override
  Widget build(BuildContext context) {
    return Container(
        decoration: BoxDecoration(
          color: AppColors.secondaryColor,
        ),
        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 2),
        child: Row(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Icon(
                Icons.reply,
                color: Colors.white,
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    replyModel.senderName ?? "",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: AppStyles.textSize12(
                      color: AppColors.white,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  Builder(builder: (context) {
                    final message = replyModel.message;
                    final linkButton = replyModel.linkButton;

                    if (replyModel.type == 'image') {
                      return CachedNetworkImage(
                        imageUrl: message ?? '',
                        width: 40,
                        height: 40,
                        fit: BoxFit.cover,
                      );
                    }

                    if (message != null) {
                      if (containsHtmlTags(message)) {
                        return HtmlWidget(
                          message,
                          customStylesBuilder: (element) {
                            if (element.classes.contains('foo')) {
                              return {'color': 'red'};
                            }
                            return null;
                          },
                          renderMode: RenderMode.column,
                          textStyle: AppStyles.textSize14(),
                        );
                      }

                      return Text(
                        message,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: AppStyles.textSize14(
                          color: AppColors.white,
                        ),
                      );
                    }

                    if (linkButton != null) {
                      return Text(
                        linkButton['link'] ?? '',
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: AppStyles.textSize14(
                          color: AppColors.white,
                        ),
                      );
                    }

                    return Text(
                      '',
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: AppStyles.textSize14(
                        color: AppColors.white,
                      ),
                    );
                  }),
                ],
              ),
            ),
            GestureDetector(
              onTap: onClearReply,
              child: Icon(
                Icons.clear,
                color: Colors.white,
              ),
            ),
          ],
        ));
  }
}
