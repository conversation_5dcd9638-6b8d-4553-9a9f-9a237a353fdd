import 'package:chat_app/export.dart';
import 'package:flutter/material.dart';

import '../../../../utils/app_colors.dart';

class SearchMessageInput extends StatelessWidget {
  final TextEditingController textEditingController;
  final Function() onNext;
  final Function() onPrevious;
  final Function(String value) onChanged;
  final Function() onClose;
  final int currentIndex;
  final int total;
  const SearchMessageInput(
      {super.key,
      required this.textEditingController,
      required this.onNext,
      required this.onPrevious,
      required this.onChanged,
      required this.onClose,
      required this.currentIndex,
      required this.total});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.grey[800]!,
      ),
      width: MediaQuery.of(context).size.width,
      child: Row(
        children: [
          GestureDetector(
            onTap: onClose,
            child: Container(
              width: 24,
              height: 24,
              margin: const EdgeInsets.only(right: 10),
              decoration: BoxDecoration(
                color: AppColors.white.withOpacity(0.8),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.close,
                size: 16,
                color: AppColors.primary,
              ),
            ),
          ),
          Expanded(
            child: TextField(
              controller: textEditingController,
              onChanged: onChanged,
              decoration: InputDecoration(
                border: InputBorder.none,
                hintStyle: AppStyles.textSize14(color: AppColors.grey),
                hintText: "key_search_this_chat".tr(),
                // suffix: Text("$currentIndex/$total"),
              ),
              style: AppStyles.textSize14(),
            ),
          ),
          IconButton(
            onPressed: currentIndex > 1 ? onPrevious : null,
            icon: Icon(
              Icons.arrow_downward,
              size: 20,
              color: currentIndex > 1 ? AppColors.primary : AppColors.grey,
            ),
          ),
          IconButton(
            onPressed: currentIndex < total ? onNext : null,
            icon: Icon(
              Icons.arrow_upward,
              size: 20,
              color: currentIndex < total ? AppColors.primary : AppColors.grey,
            ),
          ),
        ],
      ),
    );
  }
}
