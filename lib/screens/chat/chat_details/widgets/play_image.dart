import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:chat_app/widgets/overlay_loading.dart';
import 'package:extended_image/extended_image.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';

import '../../../../utils/platform_helper.dart';

import '../../../../utils/app_colors.dart';
import '../../../../utils/firestore_service.dart';
import '../../../../utils/helper.dart';
import '../../../../widgets/custom_appbar.dart';

class PlayImage extends StatefulWidget {
  final String imageUrl;
  final String? localPath;
  const PlayImage({super.key, required this.imageUrl, this.localPath});

  @override
  State<PlayImage> createState() => _PlayImageState();
}

class _PlayImageState extends State<PlayImage>
    with SingleTickerProviderStateMixin {
  bool _isLoading = false;
  String? _localPath;
  final _transformationController = TransformationController();
  TapDownDetails? _doubleTapDetails;
  AnimationController? _animationController;
  Animation<Matrix4>? _animation;
  @override
  void initState() {
    super.initState();
    _localPath = widget.localPath;
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 200),
    )..addListener(() {
        _transformationController.value = _animation!.value;
      });
  }

  @override
  void dispose() {
    _animationController?.dispose();
    super.dispose();
  }

  _onDownload(String link) async {
    try {
      setState(() {
        _isLoading = true;
      });

      if (kIsWeb) {
        // On web, we can trigger browser download
        setState(() {
          _isLoading = false;
        });

        // Extract filename from URL
        String fileName = link.split('/').last;
        if (fileName.isEmpty) {
          fileName = 'downloaded_file';
        }

        await PlatformHelper.downloadFromUrl(url: link, fileName: fileName);
        showToast("Download started...");
        return;
      }

      final directory =
          await PlatformHelper.getApplicationDocumentsDirectoryCompat();
      if (directory == null) {
        setState(() {
          _isLoading = false;
        });
        showToast("File operations not supported on this platform");
        return;
      }

      // if (await file.exists()) {
      //   file.delete();
      // }
      String url = link;

      // Use Uri.parse() to parse the URL
      Uri uri = Uri.parse(url);

      // Extract the 'path' component from the URI
      String path = uri.path;

      path = path.split('/').last;

      path = Uri.decodeComponent(path);

      File file = File('${directory.path}/$path');
      file.create(recursive: true);
      Reference storageReference = FirestoreService.storageRef.child(path);
      await storageReference.writeToFile(file);

      setState(() {
        _isLoading = false;
        _localPath = file.path;
      });

      await PlatformHelper.openFile(file.path);
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  void _handleDoubleTap() {
    Matrix4 _endMatrix;
    Offset _position = _doubleTapDetails!.localPosition;

    if (_transformationController.value != Matrix4.identity()) {
      _endMatrix = Matrix4.identity();
    } else {
      _endMatrix = Matrix4.identity()
        ..translate(-_position.dx * 2, -_position.dy * 2)
        ..scale(3.0);
    }

    _animation = Matrix4Tween(
      begin: _transformationController.value,
      end: _endMatrix,
    ).animate(
      CurveTween(curve: Curves.easeOut).animate(_animationController!),
    );
    _animationController?.forward(from: 0);
  }

  @override
  Widget build(BuildContext context) {
    return OverlayLoading(
      isLoading: _isLoading,
      child: Scaffold(
        backgroundColor: AppColors.backgroundScaffold,
        appBar: CustomAppbar(
          title: "",
          actions: [
            IconButton(
              onPressed: () async {
                if (_localPath != null) {
                  await PlatformHelper.openFile(_localPath!);
                } else {
                  await _onDownload(widget.imageUrl);
                }
              },
              icon: _localPath != null
                  ? Icon(
                      Icons.folder,
                      color: AppColors.white,
                    )
                  : Icon(
                      Icons.download,
                      color: AppColors.white,
                    ),
            )
          ],
        ),
        // body: ExtendedImage.network(
        //   widget.imageUrl,
        //   mode: ExtendedImageMode.gesture,
        //   cache: true,
        //   width: getWidth(context),
        //   fit: BoxFit.contain,
        // ),
        body: Center(
          child: GestureDetector(
            onDoubleTapDown: (d) => _doubleTapDetails = d,
            onDoubleTap: _handleDoubleTap,
            child: InteractiveViewer(
              transformationController: _transformationController,
              minScale: 0.1,
              maxScale: 10,
              child: SizedBox(
                width: getWidth(context),
                height: getHeight(context),
                child: Hero(
                  tag: widget.imageUrl,
                  child: CachedNetworkImage(
                    imageUrl: widget.imageUrl,
                    width: getWidth(context),
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
