import 'dart:io';
import 'dart:typed_data';

import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/models/chats/conversatoin_model.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/html_input.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/reply_input_chat.dart';
import 'package:chat_app/utils/app_asset.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/app_styles.dart';
import 'package:chat_app/utils/helper.dart';
import 'package:chat_app/utils/navigator_service.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/chat_attachment_modal.dart';
import 'package:flutter_svg/svg.dart';

import 'package:image_picker/image_picker.dart';
import 'package:mime_type/mime_type.dart';

class ChatInput extends StatelessWidget {
  final TextEditingController textEditingController;
  final FocusNode focusNode;
  final Function(String?) onSend;
  final Function() onTap;
  final Function(FilePickerResult result) onFilePickerResult;
  final Function(FilePickerResult result) onMuliFilePickerResult;
  final Function(String) onAddLink;
  final String? tempLink;
  final Function() onClearLink;
  final UserModel? userModel;
  final List<File> tempFiles;
  final List<PlatformFile> tempPlatformFiles;
  final ChatModel? replyModel;
  final Function() onClearReply;
  final Function(String) onChanged;
  final Function() onVoiceRecording;
  const ChatInput(
      {super.key,
      required this.textEditingController,
      required this.focusNode,
      required this.onSend,
      required this.onTap,
      required this.onAddLink,
      required this.onFilePickerResult,
      required this.onClearLink,
      this.tempLink,
      this.userModel,
      required this.tempFiles,
      this.tempPlatformFiles = const [],
      required this.onMuliFilePickerResult,
      this.replyModel,
      required this.onClearReply,
      required this.onChanged,
      required this.onVoiceRecording});

  @override
  Widget build(BuildContext context) {
    bool isDark = AppColors.isDark;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (replyModel != null)
          ReplyInputChat(
            replyModel: replyModel!,
            onClearReply: onClearReply,
          ),
        if (tempLink != null)
          Container(
            decoration: BoxDecoration(
              color: AppColors.secondaryColor,
            ),
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 2),
            child: Row(
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Icon(
                    Icons.link,
                    color: Colors.white,
                  ),
                ),
                Expanded(
                  child: Text(
                    "$tempLink",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: AppStyles.textSize12(),
                  ),
                ),
                GestureDetector(
                  onTap: onClearLink,
                  child: Icon(
                    Icons.clear,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        if (tempFiles.isNotEmpty || tempPlatformFiles.isNotEmpty)
          Container(
              width: getWidth(context),
              decoration: BoxDecoration(
                color: AppColors.secondaryColor,
              ),
              padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Builder(builder: (_) {
                    // Check if we have platform files (for web) or regular files (for mobile)
                    bool hasFiles = tempFiles.isNotEmpty;
                    bool hasPlatformFiles = tempPlatformFiles.isNotEmpty;

                    String type = 'file'; // default
                    if (hasFiles) {
                      type = mime(tempFiles.first.path)!.split('/').first;
                    } else if (hasPlatformFiles) {
                      // For platform files, check the extension or name
                      var fileName = tempPlatformFiles.first.name.toLowerCase();
                      if (fileName.contains('.jpg') ||
                          fileName.contains('.jpeg') ||
                          fileName.contains('.png') ||
                          fileName.contains('.gif') ||
                          fileName.contains('.webp')) {
                        type = 'image';
                      } else if (fileName.contains('.mp4') ||
                          fileName.contains('.mov') ||
                          fileName.contains('.avi') ||
                          fileName.contains('.mkv')) {
                        type = 'video';
                      }
                    }

                    if (type == 'image') {
                      return Expanded(
                        child: Wrap(
                          spacing: 10,
                          runSpacing: 10,
                          children: [
                            // Show regular files
                            ...List.generate(tempFiles.length, (index) {
                              return _buildImagePreview(tempFiles[index]);
                            }),
                            // Show platform files
                            ...List.generate(tempPlatformFiles.length, (index) {
                              return _buildPlatformFilePreview(
                                  tempPlatformFiles[index]);
                            }),
                          ],
                        ),
                      );
                    }
                    if (type == 'video') {
                      return Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: Colors.blue,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Icon(Icons.video_collection_rounded),
                      );
                    }
                    return Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.blue,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Icon(Icons.file_present_rounded),
                    );
                  }),
                  if ((tempFiles.length + tempPlatformFiles.length) == 1)
                    Expanded(
                        flex: 3,
                        child: Padding(
                          padding: const EdgeInsets.only(left: 10),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _getFileName(),
                                style: AppStyles.textSize12(
                                  color: Colors.white,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Padding(
                                padding: const EdgeInsets.only(top: 2),
                                child: Text(
                                  _getFileSize(),
                                  style: AppStyles.textSize10(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              )
                            ],
                          ),
                        )),
                  GestureDetector(
                    onTap: onClearLink,
                    child: Icon(
                      Icons.clear,
                      color: Colors.white,
                    ),
                  ),
                ],
              )),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          decoration: BoxDecoration(
            color: isDark ? Colors.grey[800]! : Colors.grey.withOpacity(0.5),
          ),
          width: MediaQuery.of(context).size.width,
          child: Row(
            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // GestureDetector(
              //   onTap: () async {
              //     var result = await push(context, HtmlInput());
              //     if (result != null) {
              //       onSend(result);
              //     }
              //   },
              //   child: AnimatedContainer(
              //     duration: Duration(milliseconds: 200),
              //     width: focusNode.hasFocus ? 0 : 40,
              //     height: focusNode.hasFocus ? 0 : 40,
              //     margin: EdgeInsets.only(right: focusNode.hasFocus ? 0 : 6),
              //     decoration: BoxDecoration(
              //       shape: BoxShape.circle,
              //       color: AppColors.primary,
              //     ),
              //     child: focusNode.hasFocus
              //         ? Container()
              //         : Center(
              //             child: Icon(
              //             Icons.text_fields_sharp,
              //             size: 20,
              //             color: Colors.white,
              //           )),
              //   ),
              // ),
              GestureDetector(
                onTap: () async {
                  var result = await showModalBottomSheet(
                      context: context,
                      backgroundColor: Colors.transparent,
                      builder: (_) {
                        return ChatAttachmentModal(
                          userModel: userModel,
                        );
                      });
                  if (result != null) {
                    if (context.mounted) {
                      if (result is String) {
                        onAddLink(result);
                      } else if (result is FilePickerResult) {
                        onMuliFilePickerResult(result);
                      } else if (result is List<XFile>) {
                        // Convert XFile list to FilePickerResult with proper names and sizes
                        var platformFiles = <PlatformFile>[];
                        for (var xFile in result) {
                          int size = 0;
                          try {
                            if (kIsWeb) {
                              // On web, try to get size from bytes
                              var bytes = await xFile.readAsBytes();
                              size = bytes.length;
                            } else {
                              // On mobile, get size from file
                              size = await xFile.length();
                            }
                          } catch (e) {
                            size = 0;
                          }

                          platformFiles.add(PlatformFile(
                            name: xFile.name.isNotEmpty
                                ? xFile.name
                                : 'selected_file',
                            size: size,
                            path: xFile.path,
                            bytes: kIsWeb ? await xFile.readAsBytes() : null,
                          ));
                        }
                        onMuliFilePickerResult(FilePickerResult(platformFiles));
                      } else {
                        onFilePickerResult(result);
                      }
                      // pop(context);
                    }
                  }
                },
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.primary,
                  ),
                  child: Center(
                    child: SvgPicture.asset(
                      AppAssets.linkIcon,
                      color: Colors.white,
                      width: 20,
                    ),
                  ),
                ),
              ),
              Expanded(
                child: TextField(
                  controller: textEditingController,
                  focusNode: focusNode,
                  textCapitalization: TextCapitalization.sentences,
                  keyboardType: TextInputType.multiline,
                  minLines: 1,
                  onTap: onTap,
                  onChanged: onChanged,
                  maxLines: 6,
                  decoration: InputDecoration(
                      contentPadding: EdgeInsets.symmetric(horizontal: 10),
                      border: InputBorder.none,
                      hintText: "key_type_something".tr(),
                      hintStyle: AppStyles.textSize14(color: AppColors.grey)),
                  style: AppStyles.textSize14(
                    color: AppColors.white,
                  ),
                ),
              ),
              Builder(builder: (context) {
                var hasText = (textEditingController.text.isNotEmpty);
                return Row(
                  spacing: 8,
                  children: [
                    GestureDetector(
                      onTap: () {
                        onSend.call(null);
                      },
                      child: AnimatedContainer(
                        duration: Duration(milliseconds: 200),
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.primary,
                        ),
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppColors.primary,
                          ),
                          child: Center(
                              child: Icon(
                            Icons.send,
                            color: Colors.white,
                            size: 20,
                          )),
                        ),
                      ),
                    ),
                    // Only show voice recording button on mobile platforms
                    if (!kIsWeb)
                      GestureDetector(
                        onTap: () {
                          onVoiceRecording();
                        },
                        child: AnimatedContainer(
                            duration: Duration(milliseconds: 200),
                            width: hasText ? 0 : 40,
                            height: hasText ? 0 : 40,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: AppColors.primary,
                            ),
                            child: hasText
                                ? Container()
                                : Container(
                                    width: 40,
                                    height: 40,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: AppColors.primary,
                                    ),
                                    child: Center(
                                      child: SvgPicture.asset(
                                        AppAssets.microphoneIcon,
                                        color: Colors.white,
                                        width: 20,
                                      ),
                                    ),
                                  )),
                      ),
                  ],
                );
              })
            ],
          ),
        ),
      ],
    );
  }

  /// Build image preview widget that works on both web and mobile
  Widget _buildImagePreview(File file) {
    if (kIsWeb) {
      // On web, try to use the file bytes if available
      try {
        return FutureBuilder<Uint8List>(
          future: file.readAsBytes(),
          builder: (context, snapshot) {
            if (snapshot.hasData) {
              return ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: Image.memory(
                  snapshot.data!,
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                ),
              );
            } else if (snapshot.hasError) {
              return Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Icon(
                  Icons.image,
                  color: Colors.grey,
                ),
              );
            } else {
              return Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Center(
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
              );
            }
          },
        );
      } catch (e) {
        return Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Icon(
            Icons.error,
            color: Colors.red,
          ),
        );
      }
    } else {
      // On mobile, use Image.file as usual
      return ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: Image.file(
          file,
          width: 60,
          height: 60,
          fit: BoxFit.cover,
        ),
      );
    }
  }

  /// Get file name from either tempFiles or tempPlatformFiles
  String _getFileName() {
    if (tempFiles.isNotEmpty) {
      return tempFiles.first.path.split('/').last;
    } else if (tempPlatformFiles.isNotEmpty) {
      var platformFile = tempPlatformFiles.first;
      // On web, name might be empty, so provide a fallback
      if (platformFile.name.isNotEmpty && platformFile.name != 'name') {
        return platformFile.name;
      } else {
        // Generate a name based on extension or use a default
        String extension = platformFile.extension ?? 'file';
        return 'selected_file.$extension';
      }
    }
    return 'Unknown file';
  }

  /// Get file size from either tempFiles or tempPlatformFiles
  String _getFileSize() {
    if (tempFiles.isNotEmpty) {
      return Helper.formatFileSize(tempFiles.first.lengthSync());
    } else if (tempPlatformFiles.isNotEmpty) {
      var platformFile = tempPlatformFiles.first;
      // On web, size might be 0, so try to get size from bytes
      if (platformFile.size > 0) {
        return Helper.formatFileSize(platformFile.size);
      } else if (kIsWeb && platformFile.bytes != null) {
        return Helper.formatFileSize(platformFile.bytes!.length);
      } else {
        return 'Unknown size';
      }
    }
    return '0 B';
  }

  /// Build platform file preview widget for web compatibility
  Widget _buildPlatformFilePreview(PlatformFile platformFile) {
    if (kIsWeb && platformFile.bytes != null) {
      // On web, use bytes to display image
      return ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: Image.memory(
          platformFile.bytes!,
          width: 60,
          height: 60,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Icon(
                Icons.error,
                color: Colors.red,
              ),
            );
          },
        ),
      );
    } else {
      // Fallback for when bytes are not available
      return Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(4),
        ),
        child: const Icon(
          Icons.image,
          color: Colors.grey,
        ),
      );
    }
  }
}
