import 'package:flutter/material.dart';
import '../../../../export.dart';

class MessageIndicators extends StatelessWidget {
  final bool? isHomework;
  final bool? isNotes;
  final bool fromMe;
  final VoidCallback? onHomeworkTap;
  final VoidCallback? onNotesTap;
  final Widget? bottomWidget;

  const MessageIndicators({
    super.key,
    this.isHomework,
    this.isNotes,
    required this.fromMe,
    this.onHomeworkTap,
    this.onNotesTap,
    this.bottomWidget,
  });

  @override
  Widget build(BuildContext context) {
    if ((isHomework != true) && (isNotes != true)) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(top: 4),
      child: Column(
        spacing: 8,
        mainAxisAlignment:
            fromMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment:
                fromMe ? MainAxisAlignment.end : MainAxisAlignment.start,
            children: [
              if (fromMe) const Spacer(),
              if (isHomework == true)
                GestureDetector(
                  onTap: onHomeworkTap,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    margin: const EdgeInsets.only(right: 4),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.orange,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SvgPicture.asset(
                          AppAssets.homeworkIcon,
                          width: 12,
                          height: 12,
                          colorFilter: const ColorFilter.mode(
                            Colors.orange,
                            BlendMode.srcIn,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          "Homework",
                          style: AppStyles.textSize10().copyWith(
                            color: Colors.orange,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              if (isNotes == true)
                GestureDetector(
                  onTap: onNotesTap,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.blue,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SvgPicture.asset(
                          AppAssets.documentIcon,
                          width: 12,
                          height: 12,
                          colorFilter: const ColorFilter.mode(
                            Colors.blue,
                            BlendMode.srcIn,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          "Notes",
                          style: AppStyles.textSize10().copyWith(
                            color: Colors.blue,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              if (!fromMe) const Spacer(),
            ],
          ),
          if (bottomWidget != null) bottomWidget!
        ],
      ),
    );
  }
}
