import 'package:flutter/material.dart';
import '../../../../export.dart';
import '../../../../utils/app_colors.dart';
import '../../../../utils/app_asset.dart';

enum NavigationType { homework, notes }

class HomeworkNotesNavigation extends StatelessWidget {
  final NavigationType currentType;
  final int currentIndex;
  final int totalCount;
  final VoidCallback? onPrevious;
  final VoidCallback? onNext;
  final VoidCallback? onClose;
  final VoidCallback? onToggleType;
  final String currentMessagePreview;

  const HomeworkNotesNavigation({
    super.key,
    required this.currentType,
    required this.currentIndex,
    required this.totalCount,
    this.onPrevious,
    this.onNext,
    this.onClose,
    this.onToggleType,
    required this.currentMessagePreview,
  });

  @override
  Widget build(BuildContext context) {
    if (totalCount == 0) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.black.withOpacity(0.9),
        border: Border(
          bottom: BorderSide(
            color: AppColors.white.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        bottom: false,
        child: Row(
          children: [
            // Close button
            GestureDetector(
              onTap: onClose,
              child: Container(
                padding: const EdgeInsets.all(8),
                child: Icon(
                  Icons.close,
                  color: AppColors.white,
                  size: 20,
                ),
              ),
            ),

            const SizedBox(width: 8),

            // Type toggle button
            GestureDetector(
              onTap: onToggleType,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: currentType == NavigationType.homework
                      ? Colors.orange.withOpacity(0.2)
                      : Colors.blue.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: currentType == NavigationType.homework
                        ? Colors.orange
                        : Colors.blue,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SvgPicture.asset(
                      currentType == NavigationType.homework
                          ? AppAssets.homeworkIcon
                          : AppAssets.documentIcon,
                      width: 14,
                      height: 14,
                      colorFilter: ColorFilter.mode(
                        currentType == NavigationType.homework
                            ? Colors.orange
                            : Colors.blue,
                        BlendMode.srcIn,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      currentType == NavigationType.homework
                          ? "Homework"
                          : "Notes",
                      style: AppStyles.textSize12().copyWith(
                        color: currentType == NavigationType.homework
                            ? Colors.orange
                            : Colors.blue,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(width: 12),

            // Message preview and counter
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "${currentIndex + 1} of $totalCount",
                    style: AppStyles.textSize12().copyWith(
                      color: AppColors.white.withOpacity(0.7),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    currentMessagePreview,
                    style: AppStyles.textSize14().copyWith(
                      color: AppColors.white,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            const SizedBox(width: 12),

            // Navigation buttons
            Row(
              spacing: 4,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Previous button
                GestureDetector(
                  onTap: currentIndex > 0 ? onPrevious : null,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: currentIndex > 0
                          ? AppColors.white
                          : AppColors.white.withOpacity(0.3),
                      size: 24,
                    ),
                  ),
                ),

                // Next button
                GestureDetector(
                  onTap: currentIndex < totalCount - 1 ? onNext : null,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    child: Icon(
                      Icons.keyboard_arrow_up,
                      color: currentIndex < totalCount - 1
                          ? AppColors.white
                          : AppColors.white.withOpacity(0.3),
                      size: 24,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
