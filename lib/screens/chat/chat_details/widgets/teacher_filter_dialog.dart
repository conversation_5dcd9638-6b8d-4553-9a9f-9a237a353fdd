import 'package:flutter/material.dart';
import '../../../../export.dart';
import '../../../../models/auths/user_model.dart';
import '../../../../utils/app_colors.dart';
import '../../../../utils/app_styles.dart';

class TeacherFilterDialog extends StatefulWidget {
  final List<UserModel> teachers;
  final String? selectedTeacherId;

  const TeacherFilterDialog({
    super.key,
    required this.teachers,
    this.selectedTeacherId,
  });

  @override
  State<TeacherFilterDialog> createState() => _TeacherFilterDialogState();
}

class _TeacherFilterDialogState extends State<TeacherFilterDialog> {
  String? _selectedTeacherId;

  @override
  void initState() {
    super.initState();
    _selectedTeacherId = widget.selectedTeacherId;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: AppColors.backgroundScaffold,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400, maxHeight: 500),
        padding: const EdgeInsets.all(20),
        child: Column(
          spacing: 4,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.filter_list,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  "Filter by User",
                  style: AppStyles.textSize16(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(
                    Icons.close,
                    color: AppColors.grey,
                  ),
                ),
              ],
            ),

            // Show All option
            _buildTeacherOption(
              name: "Show All Messages",
              teacherId: null,
              icon: Icons.all_inclusive,
              isSelected: _selectedTeacherId == null,
            ),

            const Divider(),

            // Teachers list
            Flexible(
              child: ListView.separated(
                separatorBuilder: (context, index) {
                  return SizedBox(
                    height: 4,
                  );
                },
                shrinkWrap: true,
                itemCount: widget.teachers.length,
                itemBuilder: (context, index) {
                  final teacher = widget.teachers[index];
                  return _buildTeacherOption(
                    name: teacher.name ?? "Unknown Teacher",
                    teacherId: teacher.docId,
                    icon: Icons.person,
                    isSelected: _selectedTeacherId == teacher.docId,
                  );
                },
              ),
            ),
            SizedBox(height: 12),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: AppColors.grey),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      "Cancel",
                      style: AppStyles.textSize12(color: AppColors.grey),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context, _selectedTeacherId ?? 'all');
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      "Apply Filter",
                      style: AppStyles.textSize12(color: AppColors.white),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTeacherOption({
    required String name,
    required String? teacherId,
    required IconData icon,
    required bool isSelected,
  }) {
    return InkWell(
      onTap: () {
        setState(() {
          _selectedTeacherId = teacherId;
        });
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: isSelected
              ? AppColors.primary.withOpacity(0.1)
              : Colors.transparent,
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.transparent,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppColors.primary
                    : AppColors.grey.withOpacity(0.2),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                icon,
                size: 14,
                color: isSelected ? AppColors.white : AppColors.grey,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                name,
                style: AppStyles.textSize14(
                  color: isSelected ? AppColors.primary : null,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: AppColors.primary,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }
}
