import 'package:flutter/material.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/models/chats/conversatoin_model.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/optimized_chat_item.dart';
import 'package:chat_app/utils/app_colors.dart';

/// Demo screen to showcase OptimizedChatItem
class ChatItemComparisonScreen extends StatelessWidget {
  const ChatItemComparisonScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Sample chat data for testing
    final sampleChats = _generateSampleChats();
    final sampleTeacher = UserModel(
      docId: "teacher1",
      name: "<PERSON>",
      avatar: "https://via.placeholder.com/150",
    );

    return Scaffold(
      backgroundColor: AppColors.backgroundScaffold,
      appBar: AppBar(
        title: const Text("Chat Item Comparison"),
        backgroundColor: AppColors.black,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader("OptimizedChatItem Examples"),
            const SizedBox(height: 16),
            ...sampleChats.map((chat) => Padding(
                  padding: const EdgeInsets.only(bottom: 20),
                  child: OptimizedChatItem(
                    chat: chat,
                    index: 0,
                    teacher: sampleTeacher,
                    onLongPress: (index) {},
                    onDownload: (chat, index) {},
                    onReplyTap: (chat) {},
                    onHomeworkTap: () {},
                    onNotesTap: () {},
                  ),
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.primary.withOpacity(0.3)),
      ),
      child: Text(
        title,
        style: AppStyles.textSize16(
          fontWeight: FontWeight.bold,
          color: AppColors.primary,
        ),
      ),
    );
  }

  List<ChatModel> _generateSampleChats() {
    return [
      // Text message
      ChatModel(
        id: "1",
        message:
            "Hello! This is a sample text message to test the chat item layout and styling.",
        senderId: "user1",
        senderName: "Alice Smith",
        createdAt: DateTime.now()
            .subtract(const Duration(minutes: 5))
            .toIso8601String(),
        type: "text",
        readAt: DateTime.now().toIso8601String(),
      ),

      // Text message from me
      ChatModel(
        id: "2",
        message:
            "This is my reply message. It should appear on the right side with different styling.",
        senderId: "currentUser",
        senderName: "Me",
        createdAt: DateTime.now()
            .subtract(const Duration(minutes: 3))
            .toIso8601String(),
        type: "text",
        readAt: DateTime.now().toIso8601String(),
      ),

      // Image message
      ChatModel(
        id: "3",
        message: "https://via.placeholder.com/300x200",
        senderId: "user1",
        senderName: "Alice Smith",
        createdAt: DateTime.now()
            .subtract(const Duration(minutes: 2))
            .toIso8601String(),
        type: "image",
        fileContent: "Beautiful sunset photo",
      ),

      // File message
      ChatModel(
        id: "4",
        message: "https://example.com/document.pdf",
        senderId: "user1",
        senderName: "Alice Smith",
        createdAt: DateTime.now()
            .subtract(const Duration(minutes: 1))
            .toIso8601String(),
        type: "file",
        fileName: "Important_Document.pdf",
        fileSize: 1024000, // 1MB
      ),

      // Homework message
      ChatModel(
        id: "5",
        message:
            "Please complete the math exercises on page 45-47 by tomorrow.",
        senderId: "user1",
        senderName: "Teacher",
        createdAt: DateTime.now().toIso8601String(),
        type: "text",
        isHomework: true,
      ),

      // Notes message
      ChatModel(
        id: "6",
        message:
            "Remember: The exam will cover chapters 1-5. Focus on the key concepts we discussed in class.",
        senderId: "user1",
        senderName: "Teacher",
        createdAt: DateTime.now().toIso8601String(),
        type: "text",
        isNotes: true,
      ),

      // Deleted message
      ChatModel(
        id: "7",
        message: "This message was deleted",
        senderId: "user1",
        senderName: "Alice Smith",
        createdAt:
            DateTime.now().subtract(const Duration(hours: 1)).toIso8601String(),
        type: "text",
        deletedAt: DateTime.now()
            .subtract(const Duration(minutes: 30))
            .toIso8601String(),
      ),
    ];
  }
}

/// Extension to add comparison navigation
extension ChatItemComparisonNavigation on BuildContext {
  void showChatItemComparison() {
    Navigator.of(this).push(
      MaterialPageRoute(
        builder: (context) => const ChatItemComparisonScreen(),
      ),
    );
  }
}
