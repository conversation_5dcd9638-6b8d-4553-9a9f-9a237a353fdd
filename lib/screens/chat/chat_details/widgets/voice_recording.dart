import 'dart:async';
import 'dart:io';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/helper.dart';
import 'package:chat_app/utils/platform_helper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';

class VoiceRecording extends StatefulWidget {
  const VoiceRecording({
    super.key,
  });

  @override
  State<VoiceRecording> createState() => _VoiceRecordingState();
}

class _VoiceRecordingState extends State<VoiceRecording> {
  bool isRecording = false;
  bool isRecordingCompleted = false;
  late Directory appDirectory;
  late final RecorderController recorderController;
  bool isLoading = true;
  @override
  void initState() {
    super.initState();
    _getDir();
    _init();
  }

  String? path;
  void _getDir() async {
    if (kIsWeb) {
      // On web, we can't access file system
      path = "web_recording_${DateTime.now().millisecondsSinceEpoch}.m4a";
      isLoading = false;
      setState(() {});
      return;
    }

    var tempDir = await PlatformHelper.getTemporaryDirectoryCompat();
    if (tempDir != null) {
      appDirectory = tempDir;
      path =
          "${appDirectory.path}/recording_${DateTime.now().millisecondsSinceEpoch}.m4a";
    } else {
      path = "recording_${DateTime.now().millisecondsSinceEpoch}.m4a";
    }
    isLoading = false;
    setState(() {});
  }

  void _init() {
    recorderController = RecorderController()
      ..androidEncoder = AndroidEncoder.aac
      ..androidOutputFormat = AndroidOutputFormat.mpeg4
      ..iosEncoder = IosEncoder.kAudioFormatMPEG4AAC
      ..sampleRate = 44100;
    recorderController.checkPermission();
    _startOrStopRecording();
  }

  @override
  void dispose() {
    recorderController.dispose();
    super.dispose();
  }

  void _startOrStopRecording() async {
    try {
      if (isRecording) {
        recorderController.reset();

        path = await recorderController.stop(false);

        if (path != null) {
          isRecordingCompleted = true;
          debugPrint(path);
          debugPrint("Recorded file size: ${File(path!).lengthSync()}");
        }
      } else {
        isRecordingCompleted = false;
        await recorderController.record(path: path); // Path is optional
      }
      setState(() {});
    } catch (e) {
      debugPrint(e.toString());
    } finally {
      if (recorderController.hasPermission) {
        setState(() {
          isRecording = !isRecording;
        });
      }
    }
  }

  void _refreshWave() {
    recorderController.reset();
    if (isRecording) recorderController.refresh();
  }

  @override
  Widget build(BuildContext context) {
    // On web, show a message that voice recording is not supported
    if (kIsWeb) {
      return Container(
        height: 200,
        padding: EdgeInsets.symmetric(horizontal: 20),
        child: SafeArea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.mic_off,
                size: 48,
                color: Colors.grey,
              ),
              SizedBox(height: 16),
              Text(
                'Voice recording is not supported on web',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8),
              Text(
                'Please use the mobile app for voice messages',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: Text('Close'),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      height: 200,
      padding: EdgeInsets.symmetric(horizontal: 20),
      child: SafeArea(
        child: Column(
          spacing: 16,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (isRecordingCompleted)
              Row(
                children: [
                  Spacer(),
                  WaveBubble(
                    file: File(path!),
                    isSender: true,
                  ),
                  Spacer(),
                ],
              ),
            if (isRecordingCompleted == false)
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                child: AudioWaveforms(
                  enableGesture: true,
                  size: Size(MediaQuery.of(context).size.width, 40),
                  recorderController: recorderController,
                  waveStyle: const WaveStyle(
                    waveColor: Colors.white,
                    extendWaveform: true,
                    showMiddleLine: false,
                  ),
                  padding: const EdgeInsets.only(left: 18),
                  margin: const EdgeInsets.symmetric(horizontal: 15),
                ),
              ),
            Row(
                spacing: 16,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: _startOrStopRecording,
                    child: Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.red.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        isRecording ? Icons.stop : Icons.mic,
                        color: Colors.red,
                        size: 24,
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      if (isRecording) {
                        _refreshWave();
                      } else {
                        Navigator.pop(context, path);
                      }
                    },
                    child: Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        isRecording ? Icons.refresh : Icons.send,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                ]),
          ],
        ),
      ),
    );
  }
}

class ChatBubble extends StatelessWidget {
  final String text;
  final bool isSender;
  final bool isLast;

  const ChatBubble({
    super.key,
    required this.text,
    this.isSender = false,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 20, bottom: 10, right: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (isSender) const Spacer(),
              Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: isSender
                        ? const Color(0xFF276bfd)
                        : const Color(0xFF343145)),
                padding: const EdgeInsets.only(
                    bottom: 9, top: 8, left: 14, right: 12),
                child: Text(
                  text,
                  style: const TextStyle(color: Colors.white, fontSize: 20),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class WaveBubble extends StatefulWidget {
  final bool isSender;

  final double? width;
  final File? file;
  final String? networkPath;

  const WaveBubble({
    super.key,
    this.file,
    this.width,
    this.isSender = false,
    this.networkPath,
  });

  @override
  State<WaveBubble> createState() => _WaveBubbleState();
}

class _WaveBubbleState extends State<WaveBubble> {
  File? file;

  late PlayerController controller;
  late StreamSubscription<PlayerState> playerStateSubscription;

  final playerWaveStyle = const PlayerWaveStyle(
    fixedWaveColor: Colors.white54,
    liveWaveColor: Colors.white,
    spacing: 6,
  );
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _init();
  }

  _init() async {
    controller = PlayerController();
    _preparePlayer();
    playerStateSubscription = controller.onPlayerStateChanged.listen((_) {
      setState(() {});
    });
  }

  @override
  void didUpdateWidget(covariant WaveBubble oldWidget) {
    if (oldWidget.networkPath != widget.networkPath) {
      _refresh();
    }
    super.didUpdateWidget(oldWidget);
  }

  _refresh() async {
    _preparePlayer();
  }

  void _preparePlayer() async {
    try {
      // Opening file from assets folder
      if (widget.file != null) {
        file = widget.file;
        setState(() {});
      } else if (widget.networkPath != null) {
        file = await Helper.downloadMedia(
            link: widget.networkPath!,
            useCache: true,
            onCompleted: (path) {},
            onError: () {});
        setState(() {});
      }

      if (file != null) {
        await controller.preparePlayer(
          path: file!.path,
          shouldExtractWaveform: true,
        );
      }
    } catch (e) {
      print(e.toString());
    }
  }

  @override
  void dispose() {
    playerStateSubscription.cancel();
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: widget.isSender
            ? AppColors.primary.withOpacity(0.1)
            : const Color(0xFF343145),
      ),
      child: Builder(builder: (context) {
        if (isLoading) {
          return Center(
            child: CupertinoActivityIndicator(),
          );
        }
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (!controller.playerState.isStopped)
              IconButton(
                onPressed: () async {
                  controller.playerState.isPlaying
                      ? await controller.pausePlayer()
                      : await controller.startPlayer();
                  controller.setFinishMode(finishMode: FinishMode.loop);
                },
                icon: Icon(
                  controller.playerState.isPlaying
                      ? Icons.stop
                      : Icons.play_arrow,
                ),
                color: Colors.white,
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
              ),
            AudioFileWaveforms(
              size: Size(MediaQuery.of(context).size.width / 2, 30),
              playerController: controller,
              waveformType: WaveformType.long,
              playerWaveStyle: playerWaveStyle,
            ),
            if (widget.isSender) const SizedBox(width: 10),
          ],
        );
      }),
    );
  }
}
