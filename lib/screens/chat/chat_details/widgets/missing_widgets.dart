import 'package:flutter/material.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/models/chats/conversatoin_model.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:any_link_preview/any_link_preview.dart';
import 'package:flutter/cupertino.dart';
import 'package:url_launcher/url_launcher_string.dart';

/// Missing widgets that were removed during optimization

/// MarkIcon widget for read status
class MarkIcon extends StatelessWidget {
  final bool isRead;
  final Color? color;
  final double? size;

  const MarkIcon({
    super.key,
    this.isRead = false,
    this.color,
    this.size = 14,
  });

  @override
  Widget build(BuildContext context) {
    return SvgPicture.asset(
      AppAssets.markReadAllIcon,
      color: isRead ? (color ?? AppColors.primary) : AppColors.grey,
      width: size,
      height: size,
    );
  }
}

/// TeacherNameWidget for displaying sender name
class TeacherNameWidget extends StatelessWidget {
  final String teacherName;
  final bool fromMe;
  final TextStyle? style;

  const TeacherNameWidget({
    super.key,
    required this.teacherName,
    required this.fromMe,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    if (teacherName.isEmpty || fromMe) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Text(
        teacherName,
        style: style ??
            AppStyles.textSize12(
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
      ),
    );
  }
}

/// FileContent widget for displaying file descriptions
class FileContent extends StatelessWidget {
  final String? fileContent;
  final TextStyle? style;
  final EdgeInsets? padding;

  const FileContent({
    super.key,
    this.fileContent,
    this.style,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    if (fileContent?.isEmpty ?? true) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: padding ?? const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: AppColors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        fileContent!,
        style: style ?? AppStyles.textSize12(),
      ),
    );
  }
}

/// LinkPreviewMessage widget for URL previews
class LinkPreviewMessage extends StatelessWidget {
  final Function(int index) onLongPress;
  final String link;
  final int index;
  final double linkErrorTextStyle;
  final Color? backgroundColor;
  final String? title;
  final ChatModel chatModel;

  const LinkPreviewMessage({
    super.key,
    required this.onLongPress,
    required this.index,
    required this.link,
    this.backgroundColor,
    this.linkErrorTextStyle = 14,
    required this.chatModel,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onLongPress: () => onLongPress(index),
      child: SizedBox(
        width: getWidth(context) * 0.7,
        child: AnyLinkPreview(
          key: Key(DateTime.now().toIso8601String()),
          link: link,
          displayDirection: UIDirection.uiDirectionHorizontal,
          placeholderWidget: Container(
            width: 40,
            height: 40,
            child: const CupertinoActivityIndicator(),
          ),
          showMultimedia: false,
          bodyMaxLines: 5,
          bodyTextOverflow: TextOverflow.ellipsis,
          titleStyle: AppStyles.textSize16(
            color: AppColors.primary,
          ),
          bodyStyle: AppStyles.textSize14(),
          errorWidget: GestureDetector(
            onTap: () => launchUrlString(link),
            child: Text(
              title ?? link,
              style: AppStyles.textSize14().copyWith(
                fontSize: linkErrorTextStyle,
                fontStyle: FontStyle.italic,
                color: AppColors.primary,
                decoration: TextDecoration.underline,
              ),
            ),
          ),
          errorTitle: "",
          errorBody: chatModel.message ?? link,
          cache: const Duration(days: 7),
          backgroundColor: backgroundColor ?? AppColors.white.withOpacity(0.1),
          borderRadius: 4,
          removeElevation: true,
          onTap: () => launchUrlString(link),
        ),
      ),
    );
  }
}

/// WaveBubble widget for audio messages (simplified version)
class WaveBubble extends StatelessWidget {
  final bool isSender;
  final double width;
  final String? networkPath;

  const WaveBubble({
    super.key,
    required this.isSender,
    required this.width,
    this.networkPath,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: isSender
            ? AppColors.primary.withOpacity(0.1)
            : AppColors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primary.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              Icons.play_arrow,
              color: AppColors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Audio Message",
                  style: AppStyles.textSize14(fontWeight: FontWeight.w500),
                ),
                Text(
                  "Tap to play",
                  style: AppStyles.textSize12().copyWith(color: AppColors.grey),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
