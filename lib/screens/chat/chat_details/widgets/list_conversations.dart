import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/models/chats/conversatoin_model.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/optimized_chat_item.dart';
import 'package:flutter/material.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class ListConversations extends StatelessWidget {
  final List<ChatModel> chats;
  final UserModel? teacher;
  final ScrollController scrollController;
  final Function(int index) onLongPress;
  final Function(ChatModel message, int index) onDownload;
  final String? query;
  final ItemScrollController itemScrollController;
  final ScrollOffsetController scrollOffsetController;
  final ItemPositionsListener itemPositionsListener;
  final ScrollOffsetListener scrollOffsetListener;
  final Function(ChatModel) onReplyTap;
  final VoidCallback? onHomeworkTap;
  final VoidCallback? onNotesTap;
  final Function(ChatModel)? onRetryMessage;

  const ListConversations({
    super.key,
    required this.chats,
    required this.scrollController,
    this.teacher,
    required this.onDownload,
    required this.onLongPress,
    this.query,
    required this.itemScrollController,
    required this.scrollOffsetController,
    required this.itemPositionsListener,
    required this.scrollOffsetListener,
    required this.onReplyTap,
    this.onHomeworkTap,
    this.onNotesTap,
    this.onRetryMessage,
  });

  @override
  Widget build(BuildContext context) {
    return ScrollablePositionedList.separated(
        // controller: scrollController,
        reverse: true,
        physics: const BouncingScrollPhysics(),
        itemScrollController: itemScrollController,
        scrollOffsetController: scrollOffsetController,
        itemPositionsListener: itemPositionsListener,
        scrollOffsetListener: scrollOffsetListener,
        itemBuilder: (_, index) {
          var chat = chats[index];
          return OptimizedChatItem(
              teacher: teacher,
              onReplyTap: onReplyTap,
              chat: chat,
              onLongPress: onLongPress,
              onDownload: onDownload,
              index: index,
              onHomeworkTap: onHomeworkTap,
              onNotesTap: onNotesTap,
              onRetryMessage: onRetryMessage,
              query: query);
        },
        separatorBuilder: (_, index) {
          return const SizedBox(height: 20);
        },
        itemCount: chats.length);
  }
}
