import 'dart:io';
import 'dart:typed_data';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';

import '../../../../export.dart';

class ChangeRoomName extends StatefulWidget {
  final String name;
  final String? image;
  const ChangeRoomName({super.key, required this.name, this.image});

  @override
  State<ChangeRoomName> createState() => _ChangeRoomNameState();
}

class _ChangeRoomNameState extends State<ChangeRoomName> {
  final TextEditingController _textEditingController = TextEditingController();
  File? _file;
  Uint8List? _webImage;
  String? _fileName;
  bool _isLoading = false;
  @override
  void initState() {
    _textEditingController.text = widget.name;
    super.initState();
  }

  _submit() async {
    try {
      setState(() {
        _isLoading = true;
      });
      String? avatar;

      if (kIsWeb && _webImage != null) {
        // Web upload using bytes
        String path =
            "room_images/${DateTime.now().millisecondsSinceEpoch}_${_fileName ?? 'image.jpg'}";
        Reference storageReference = FirestoreService.storageRef.child(path);

        UploadTask uploadTask = storageReference.putData(_webImage!);
        await uploadTask.whenComplete(() {});

        avatar = await storageReference.getDownloadURL();
      } else if (!kIsWeb && _file != null) {
        // Mobile upload using file
        var task = await FirestoreService.uploadFileToFireStorage(_file!);
        await task.whenComplete(() => null);
        String path = await task.storage
            .ref(task.snapshot.metadata!.fullPath)
            .getDownloadURL();
        avatar = path;
      }

      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        pop(context, result: {
          "name": _textEditingController.text,
          "image": avatar ?? widget.image,
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return OverlayLoading(
      isLoading: _isLoading,
      child: Scaffold(
          appBar: CustomAppbar(
            title: "key_group_name".tr(),
            actions: [
              TextButton(
                  onPressed: _textEditingController.text.isNotEmpty
                      ? () {
                          _submit();
                        }
                      : null,
                  child: Text(
                    "key_save".tr(),
                  ))
            ],
          ),
          backgroundColor: AppColors.backgroundScaffold,
          body: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20)
                .copyWith(bottom: 0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Align(
                  alignment: Alignment.center,
                  child: GestureDetector(
                    onTap: () async {
                      var result = await ImagePicker()
                          .pickImage(source: ImageSource.gallery);
                      if (result != null) {
                        if (kIsWeb) {
                          // Web: Use bytes
                          _webImage = await result.readAsBytes();
                          _fileName = result.name;
                        } else {
                          // Mobile: Use file path
                          _file = File(result.path);
                        }
                        setState(() {});
                      }
                    },
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(1000),
                          child: Builder(builder: (context) {
                            // Show selected image
                            if (kIsWeb && _webImage != null) {
                              return Image.memory(
                                _webImage!,
                                width: 80,
                                height: 80,
                                fit: BoxFit.cover,
                              );
                            }
                            if (!kIsWeb && _file != null) {
                              return Image.file(
                                _file!,
                                width: 80,
                                height: 80,
                                fit: BoxFit.cover,
                              );
                            }

                            // Show existing image or placeholder
                            if (widget.image?.isEmpty ?? true) {
                              return SizedBox(
                                width: 80,
                                height: 80,
                                child: Container(
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: AppColors.grey.withOpacity(0.3),
                                  ),
                                  child: Icon(
                                    Icons.person,
                                    color: AppColors.white,
                                    size: 40,
                                  ),
                                ),
                              );
                            }
                            return CachedNetworkImage(
                              imageUrl: "${widget.image}",
                              width: 80,
                              height: 80,
                              fit: BoxFit.cover,
                            );
                          }),
                        ),
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white.withOpacity(0.2),
                          ),
                          child: const Icon(
                            Icons.camera_alt_outlined,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                24.h,
                TextField(
                  controller: _textEditingController,
                  style: AppStyles.textSize14(),
                  decoration: InputDecoration(
                    hintText: "key_enter_group_name".tr(),
                    hintStyle: AppStyles.textSize14(
                      color: AppColors.grey,
                    ),
                    border: UnderlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {});
                  },
                ),
              ],
            ),
          )),
    );
  }
}
