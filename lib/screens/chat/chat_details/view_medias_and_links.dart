import 'package:cached_network_image/cached_network_image.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/models/chats/conversatoin_model.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/play_image.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/play_video.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:open_filex/open_filex.dart';
import 'package:url_launcher/url_launcher_string.dart';

class ViewMediasAndLinks extends StatefulWidget {
  final List<ChatModel> chats;
  const ViewMediasAndLinks({super.key, required this.chats});

  @override
  State<ViewMediasAndLinks> createState() => _ViewMediasAndLinksState();
}

class _ViewMediasAndLinksState extends State<ViewMediasAndLinks>
    with TickerProviderStateMixin {
  List<ChatModel> chats = [];

  @override
  void initState() {
    chats = widget.chats
        .toList()
        .where((element) => element.deletedAt == null)
        .toList();
    super.initState();
  }

  late final TabController _tabController =
      TabController(length: 3, vsync: this);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppColors.backgroundScaffold,
        appBar: CustomAppbar(
          height: kToolbarHeight * 2,
          title: "key_view_media_file_and_link".tr(),
          bottom: TabBar(
            controller: _tabController,
            labelStyle: AppStyles.textSize14(),
            labelColor: AppColors.white,
            tabs: [
              Tab(
                text: "key_media".tr(),
              ),
              Tab(
                text: "key_file".tr(),
              ),
              Tab(
                text: "key_link".tr(),
              )
            ],
          ),
        ),
        body: TabBarView(controller: _tabController, children: [
          Builder(builder: (context) {
            var list = chats
                .where((element) => ['image', 'video'].contains(element.type))
                .toList();
            return MediaGridView(chats: list);
          }),
          Builder(builder: (context) {
            var list = chats
                .where((element) =>
                    ['image', 'video'].contains(element.type) == false &&
                    isUrl(element.message ?? "") &&
                    element.fileSize != null)
                .toList();
            return FileListView(chats: list);
          }),
          Builder(builder: (context) {
            var list = chats
                .where((element) =>
                    ['image', 'video'].contains(element.type) == false &&
                    isUrl(element.message ?? "") &&
                    element.fileSize == null)
                .toList();
            return LinkListView(chats: list);
          }),
        ]));
  }
}

class MediaGridView extends StatelessWidget {
  final List<ChatModel> chats;
  const MediaGridView({super.key, required this.chats});

  @override
  Widget build(BuildContext context) {
    return GridView.count(
        crossAxisCount: 3,
        mainAxisSpacing: 10,
        crossAxisSpacing: 10,
        children: List.generate(chats.length, (index) {
          var chat = chats[index];
          if (chat.type == 'image') {
            return GestureDetector(
              onTap: () {
                push(
                    context,
                    PlayImage(
                        imageUrl: chat.message!, localPath: chat.localPath));
              },
              child: CachedNetworkImage(
                imageUrl: chat.message!,
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
              ),
            );
          }
          if (chat.type == 'video') {
            return GestureDetector(
              onTap: () {
                push(
                    context,
                    PlayVideo(
                      videoUrl: chat.message!,
                      localPath: chat.localPath,
                    ));
              },
              child: Stack(
                children: [
                  CachedNetworkImage(
                    imageUrl: chat.fileThumbnail ?? "",
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                  ),
                  const Positioned.fill(
                    child: Center(
                      child: Icon(
                        Icons.play_circle_fill_rounded,
                        color: Colors.white,
                        size: 30,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }
          return Container();
        }));
  }
}

class FileListView extends StatefulWidget {
  final List<ChatModel> chats;
  const FileListView({super.key, required this.chats});

  @override
  State<FileListView> createState() => _FileListViewState();
}

class _FileListViewState extends State<FileListView> {
  List<ChatModel> _chats = [];
  @override
  void initState() {
    _chats = widget.chats.toList();
    super.initState();
  }

  bool _isLoading = false;
  @override
  Widget build(BuildContext context) {
    return OverlayLoading(
      isLoading: _isLoading,
      child: ListView.separated(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          itemBuilder: (_, index) {
            var chat = _chats[index];
            return GestureDetector(
              onTap: () async {
                if (chat.localPath != null) {
                  OpenFilex.open(chat.localPath!);
                } else {
                  setState(() {
                    _isLoading = true;
                  });
                  await Helper.downloadMedia(
                      link: chat.message!,
                      onCompleted: (path) {
                        setState(() {
                          _chats[index] = chat.copyWith(localPath: path);
                          _isLoading = false;
                        });
                      },
                      onError: () {
                        setState(() {
                          _isLoading = false;
                        });
                      });
                }
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                decoration: BoxDecoration(
                  color: AppColors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Row(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 4),
                      child: SvgPicture.asset(
                        AppAssets.attachmentIcon,
                        color: AppColors.primary,
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            chat.fileName ?? "",
                            style: AppStyles.textSize14(),
                          ),
                          if (chat.fileSize != null)
                            Text(
                              Helper.formatFileSize(chat.fileSize!),
                              style: AppStyles.textSize12(),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
          separatorBuilder: (_, index) {
            return 10.h;
          },
          itemCount: _chats.length),
    );
  }
}

class LinkListView extends StatelessWidget {
  final List<ChatModel> chats;
  const LinkListView({super.key, required this.chats});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        itemBuilder: (_, index) {
          var chat = chats[index];
          return GestureDetector(
            onTap: () {
              launchUrlString(chat.message!);
            },
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 14, horizontal: 10),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                "${chat.message}",
                style: AppStyles.textSize14().copyWith(
                    fontStyle: FontStyle.italic,
                    color: AppColors.primary,
                    decoration: TextDecoration.underline),
              ),
            ),
          );
        },
        separatorBuilder: (_, index) {
          return 10.h;
        },
        itemCount: chats.length);
  }
}
