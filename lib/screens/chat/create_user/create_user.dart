import 'package:chat_app/export.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:flutter/material.dart';

class CreateUser extends StatefulWidget {
  @override
  _UserFormState createState() => _UserFormState();
}

class _UserFormState extends State<CreateUser> {
  final _formKey = GlobalKey<FormState>();

  // Controllers for each form field
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _contactOneController = TextEditingController();
  final TextEditingController _contactTwoController = TextEditingController();
  final TextEditingController _typeController = TextEditingController();
  final TextEditingController _prAcYearController = TextEditingController();
  final TextEditingController _prCgroupNameController = TextEditingController();
  final TextEditingController _prCourseNameController = TextEditingController();
  final TextEditingController _prSectionNameController =
      TextEditingController();
  final TextEditingController _whatsappNumberController =
      TextEditingController();
  final TextEditingController _sidController = TextEditingController();
  final TextEditingController _activeStatusController = TextEditingController();
  final TextEditingController _dobController = TextEditingController();
  DateTime? _dob;
  bool _isLoading = false;

  _submit() async {
    try {
      setState(() {
        _isLoading = true;
      });
      var type = int.parse(_typeController.text);
      var contactOne = int.tryParse(_contactOneController.text);
      var contactTwo = int.tryParse(_contactTwoController.text);
      var whatsappNumber = int.tryParse(_whatsappNumberController.text);
      var activeStatus = int.parse(_activeStatusController.text);
      await FirestoreService.addUser({
        "sid": _sidController.text,
        "name": _nameController.text,
        "whatsappNumber": whatsappNumber,
        "contact_one": contactOne,
        "contact_two": contactTwo,
        "pr_cgroup_name": _prCgroupNameController.text,
        "pr_course_name": _prCourseNameController.text,
        "pr_section_name": _prSectionNameController.text,
        "pr_ac_year": _prAcYearController.text,
        "activeStatus": activeStatus,
        "type": type == 0 ? "student" : "teacher",
        "dob": _dob != null ? DateFormat("yyyy-MM-dd").format(_dob!) : null
      });
      setState(() {
        _isLoading = false;
      });
      showToast("Success!");
      pop(context);
    } catch (e) {
      showToast(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return OverlayLoading(
      isLoading: _isLoading,
      child: Scaffold(
        appBar: AppBar(
          title: Text('Create User'),
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: ListView(
              children: [
                _buildTextField('Student ID', _sidController),
                _buildTextField('Name', _nameController, isRequired: true),
                Stack(
                  children: [
                    GestureDetector(
                      onTap: () async {
                        final result = await showDatePicker(
                            context: context,
                            initialDate: DateTime.now(),
                            firstDate: DateTime(1900),
                            lastDate: DateTime(3000));
                        if (result != null) {
                          setState(() {
                            _dob = result;
                            _dobController.text =
                                DateFormat("dd-MM-yyyy").format(result);
                          });
                        }
                      },
                      child: _buildTextField(
                        'Birth day',
                        _dobController,
                        isRequired: false,
                        enabled: false,
                      ),
                    ),
                    Positioned(
                      right: 0,
                      bottom: 0,
                      top: 0,
                      child: IconButton(
                        onPressed: () {
                          _dobController.clear();
                          setState(() {
                            _dob = null;
                          });
                        },
                        icon: Icon(
                          Icons.clear,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                _buildNumberField('Contact One', _contactOneController),
                _buildNumberField('Contact Two', _contactTwoController),
                _buildNumberField('WhatsApp Number', _whatsappNumberController),
                _buildTextField('Type(0: student, 1: teacher)', _typeController,
                    isRequired: true),
                _buildTextField('Academic Year', _prAcYearController),
                _buildTextField('Class Group', _prCgroupNameController),
                _buildTextField('Course', _prCourseNameController),
                _buildTextField('Section', _prSectionNameController),
                _buildNumberField(
                    'Active Status (0/1)', _activeStatusController,
                    isRequired: true),
                SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () async {
                    if (_formKey.currentState!.validate()) {
                      await _submit();
                    }
                  },
                  child: Text('Add User'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField(
    String label,
    TextEditingController controller, {
    bool isRequired = false,
    bool enabled = true,
    Widget? suffixIcon,
  }) {
    return TextFormField(
      enabled: enabled,
      controller: controller,
      style: AppStyles.textSize16(),
      decoration: InputDecoration(
        labelText: label,
        suffixIcon: suffixIcon,
        labelStyle: AppStyles.textSize15(),
      ),
      validator: (value) {
        if (isRequired == false) {
          return null;
        } else {
          if (value == null || value.isEmpty) {
            return 'Please enter $label';
          }
          return null;
        }
      },
    );
  }

  Widget _buildNumberField(String label, TextEditingController controller,
      {bool isRequired = false}) {
    return TextFormField(
      controller: controller,
      style: AppStyles.textSize16(),
      keyboardType: TextInputType.number,
      decoration: InputDecoration(
        labelText: label,
        labelStyle: AppStyles.textSize15(),
      ),
      validator: (value) {
        if (isRequired == false) {
          return null;
        } else {
          if (value == null || value.isEmpty) {
            return 'Please enter $label';
          }
          if (int.tryParse(value) == null) {
            return 'Please enter a valid number';
          }
        }
      },
    );
  }
}
