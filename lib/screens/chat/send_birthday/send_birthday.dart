import 'package:calendar_timeline/calendar_timeline.dart';
import 'package:chat_app/screens/chat/search/search_parent.dart';
import 'package:flutter/material.dart';
import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/screens/chat/search/show_phone_number_modal.dart';
import 'package:chat_app/screens/chat/search/widgets/filter_dialog.dart';
import 'package:chat_app/utils/app_colors.dart';

import 'package:chat_app/utils/firestore_service.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SendBirthday extends StatefulWidget {
  const SendBirthday({super.key});

  @override
  State<SendBirthday> createState() => _SendBirthdayState();
}

class _SendBirthdayState extends State<SendBirthday> {
  List<UserModel> _originalUsers = [];
  List<UserModel> users = [];

  List<UserModel> _usersSelected = [];
  // String? _type;
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = false;
  DateTime _dateTime = DateTime.now();

  @override
  void initState() {
    _searchUser();
    super.initState();
  }

  _searchUser(
      {String? query, bool isFromLocal = true, bool autoSelect = false}) async {
    try {
      setState(() {
        _isLoading = true;
      });
      bool isPhone = false;
      if (query != null) {
        isPhone = Helper.checkStringIsNumberFormat(query);
      }
      var profile = BlocProvider.of<AuthBloc>(context).state.userModel;
      users = await FirestoreService.searchUser(
        name: isPhone ? null : query,
        phoneNumber: isPhone ? query : null,
        schoolCode: profile?.schoolCode,
        fromLocal: isFromLocal,
      );
      if (_originalUsers.isEmpty) {
        _originalUsers = users.toList();
      }
      var dob = Helper.formatDateTimeToString(context, _dateTime,
          newPattern: "MM-dd");
      _originalUsers = _originalUsers.where((element) {
        if (element.dob?.isNotEmpty ?? false) {
          var userDob =
              element.dob!.split("-")[1] + "-" + element.dob!.split("-")[2];
          return dob == userDob;
        }
        return false;
      }).toList();

      users = users.where((element) {
        if (element.dob?.isNotEmpty ?? false) {
          var userDob =
              element.dob!.split("-")[1] + "-" + element.dob!.split("-")[2];
          return dob == userDob;
        }
        return false;
      }).toList();
      if (autoSelect) {
        _usersSelected = users.toList();
      }
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  List<UserModel> getSelectedUsers() {
    return _usersSelected;
  }

  _sendBirthDayWishes() async {
    try {
      setState(() {
        _isLoading = true;
      });
      var profile = BlocProvider.of<AuthBloc>(context).state.userModel;
      for (var user in _usersSelected) {
        await FirestoreService.sendActivity(
            receiverId: user.docId!,
            titleNotification: "Happy Birthday 🎉🎉🎉",
            sid: user.sid!,
            message:
                "Happy Birthday, ${user.name}! 🎉🎂 Wishing you a day as bright and promising as your future. May this year bring you great success in your studies, moments of joy, and the accomplishment of your academic goals. Keep up the hard work and enthusiasm! Here's to a year filled with learning, growth, and memorable achievements. Enjoy your special day!",
            senderName: profile!.name ?? "",
            receiverName: user.name ?? "",
            senderId: "");
        var date = Helper.formatDateTimeToString(context, DateTime.now(),
            newPattern: "dd-MM-yyyy");
        await FirestoreService.updateUser(
            docId: user.docId!, data: {"date_of_sent_birthday_wishes": date});
      }
      await FirestoreService.sendActivity(
          sendNotification: false,
          titleNotification: "key_notifications".tr(),
          receiverId: "",
          sid: "",
          message: "key_birthday_wishes_sent_successfully".tr(),
          senderName: profile!.name ?? "",
          receiverName: "key_notifications".tr(),
          senderId: profile.docId!);
      await _searchUser();
      setState(() {
        _usersSelected = [];
        _isLoading = false;
      });
      showToast("key_success_message".tr());
    } catch (e) {
      showToast(e.toString());
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard(context);
      },
      child: OverlayLoading(
        isLoading: _isLoading,
        child: Scaffold(
          bottomNavigationBar: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                child: ButtonCustom(
                  enabled: _usersSelected.isNotEmpty,
                  onPressed: _sendBirthDayWishes,
                  title: "key_send_birthday_wishes".tr() +
                      (_usersSelected.isEmpty
                          ? ""
                          : "(${_usersSelected.length})"),
                ),
              ),
            ],
          ),
          backgroundColor: AppColors.backgroundScaffold,
          appBar: CustomAppbar(
            title: "key_send_birthday_wishes".tr(),
            // actions: [
            //   IconButton(
            //       onPressed: () async {
            //         await _searchUser(isFromLocal: false);
            //       },
            //       icon: Icon(Icons.sync)),
            // ],
          ),
          body: Column(
            children: [
              // GestureDetector(
              //   onTap: () async {
              //     var result = await showDatePicker(
              //         context: context,
              //         initialDate: _dateTime,
              //         firstDate: DateTime(1900),
              //         lastDate: DateTime.now());
              //     if (result != null) {
              //       _dateTime = result;
              //       _usersSelected = [];
              //       setState(() {});
              //       _searchUser();
              //     }
              //   },
              //   child: Container(
              //       decoration: BoxDecoration(
              //           color: AppColors.white,
              //           borderRadius: BorderRadius.circular(4)),
              //       padding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
              //       margin: const EdgeInsets.symmetric(
              //           horizontal: 10, vertical: 16),
              //       child: Row(
              //         children: [
              //           Expanded(
              //             child: Text(
              //               "${Helper.formatDateTimeToString(context, _dateTime, newPattern: "dd-MM-yyyy")}",
              //               style: AppStyles.textSize16(
              //                 color: AppColors.black,
              //               ),
              //             ),
              //           ),
              //           Icon(
              //             Icons.calendar_month,
              //             color: AppColors.primary,
              //           )
              //         ],
              //       )),
              // ),
              Padding(
                padding: const EdgeInsets.only(bottom: 10),
                child: CalendarTimeline(
                  // showYears: true,'
                  // shrink: true,
                  dayNameFontSize: 14,
                  fontSize: 20,
                  height: 70,
                  initialDate: _dateTime,
                  firstDate: DateTime(1900),
                  lastDate: _dateTime,
                  onDateSelected: (date) {
                    setState(() {
                      _dateTime = date;
                      _searchUser(autoSelect: true);
                    });
                  },

                  leftMargin: 150,
                  monthColor: Colors.blueGrey,
                  dayColor: Colors.teal[200],
                  activeDayColor: Colors.white,
                  activeBackgroundDayColor: AppColors.primary,
                  // dotsColor: Color(0xFF333A47),
                ),
              ),
              Expanded(
                child: ListView.separated(
                    itemBuilder: (_, index) {
                      var user = users[index];
                      bool isSelected = _usersSelected.contains(user);
                      bool sent = false;
                      if (user.dob?.isNotEmpty ?? false) {
                        try {
                          var dob = Helper.formatDateTimeToString(
                              context, DateTime.now(),
                              newPattern: "dd-MM-yyyy");
                          var userDob = user.dateOfSentBirthdayWishes;
                          if (dob == userDob) {
                            sent = true;
                          }
                        } catch (e) {}
                      }

                      return UserItem(
                          showBirthdayWishes: true,
                          showPhoneCall: false,
                          onTap: () {
                            if (sent == false) {
                              if (isSelected) {
                                _usersSelected.remove(user);
                              } else {
                                _usersSelected.add(user);
                              }
                              setState(() {});
                            }
                          },
                          user: user,
                          isSelectMulti: !sent,
                          isSelected: isSelected);
                    },
                    separatorBuilder: (_, index) {
                      return Divider(
                        height: 1,
                        color: AppColors.white,
                      );
                    },
                    itemCount: users.length),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
