import 'package:chat_app/export.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

class FilterDialog extends StatefulWidget {
  final List<UserModel> users;
  final List<String>? groupSelected;
  final List<String>? batchSelected;
  final List<String>? sectionSelected;

  const FilterDialog(
      {super.key,
      required this.users,
      this.groupSelected,
      this.batchSelected,
      this.sectionSelected});

  @override
  State<FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends State<FilterDialog> {
  List<String> _groupSelected = [];
  List<String> _batchSelected = [];
  List<String> _sectionSelected = [];
  List<String> _groups = [];
  List<String> _batchs = [];
  List<String> _sections = [];

  @override
  void initState() {
    _groupSelected = widget.groupSelected ?? [];
    _batchSelected = widget.batchSelected ?? [];
    _sectionSelected = widget.sectionSelected ?? [];
    {
      var x = groupBy(widget.users, (p0) => p0.prCGroupName);
      _groups = x.keys
          .whereType<String>()
          .toList()
          .where((e) => e != "" && e != "null")
          .toList();
    }
    {
      var x = groupBy(widget.users, (p0) => p0.prCourseName);
      _batchs = x.keys
          .whereType<String>()
          .toList()
          .where((e) => e != "" && e != "null")
          .toList();
    }
    {
      var x = groupBy(widget.users, (p0) => p0.prSectionName);
      _sections = x.keys
          .whereType<String>()
          .toList()
          .where((e) => e != "" && e != "null")
          .toList();
    }

    super.initState();
  }

  _filterBatchByGroup() {
    if (_groupSelected.isEmpty) {
      return _batchs;
    } else {
      var list = widget.users.toList().where((element) {
        if (_groupSelected.isNotEmpty) {
          return _groupSelected.contains(element.prCGroupName);
        }
        return true;
      }).toList();
      {
        var x = groupBy(list, (p0) => p0.prCourseName);
        var filterBatchs = x.keys.whereType<String>().toList();
        return filterBatchs;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    var filterBatchs = _filterBatchByGroup();
    return Dialog(
      backgroundColor: AppColors.backgroundScaffold,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "key_group".tr(),
              style: AppStyles.textSize16(),
            ),
            SizedBox(
              height: 80,
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child: Wrap(
                    spacing: 10,
                    runSpacing: 10,
                    children: List.generate(_groups.length, (index) {
                      var value = _groups[index];
                      bool isSelected = _groupSelected.contains(value);
                      return GestureDetector(
                        onTap: () {
                          if (isSelected) {
                            _groupSelected.remove(value);
                          } else {
                            _groupSelected.add(value);
                          }
                          setState(() {});
                        },
                        child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 10, vertical: 4),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: isSelected
                                  ? AppColors.primary
                                  : AppColors.white.withOpacity(0.1),
                            ),
                            child:
                                Text("$value", style: AppStyles.textSize12())),
                      );
                    }),
                  ),
                ),
              ),
            ),
            10.h,
            Text(
              "key_batch".tr(),
              style: AppStyles.textSize16(),
            ),
            SizedBox(
              height: 100,
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child: Wrap(
                    spacing: 10,
                    runSpacing: 10,
                    children: List.generate(filterBatchs.length, (index) {
                      var value = filterBatchs[index];
                      bool isSelected = _batchSelected.contains(value);
                      return GestureDetector(
                        onTap: () {
                          if (isSelected) {
                            _batchSelected.remove(value);
                          } else {
                            _batchSelected.add(value);
                          }
                          setState(() {});
                        },
                        child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 10, vertical: 4),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: isSelected
                                  ? AppColors.primary
                                  : AppColors.white.withOpacity(0.1),
                            ),
                            child:
                                Text("$value", style: AppStyles.textSize12())),
                      );
                    }),
                  ),
                ),
              ),
            ),
            10.h,
            Text(
              "key_section".tr(),
              style: AppStyles.textSize16(),
            ),
            SizedBox(
              height: 150,
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child: Wrap(
                    spacing: 10,
                    runSpacing: 10,
                    children: List.generate(_sections.length, (index) {
                      var value = _sections[index];
                      bool isSelected = _sectionSelected.contains(value);
                      return GestureDetector(
                        onTap: () {
                          if (isSelected) {
                            _sectionSelected.remove(value);
                          } else {
                            _sectionSelected.add(value);
                          }
                          setState(() {});
                        },
                        child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 10, vertical: 4),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: isSelected
                                  ? AppColors.primary
                                  : AppColors.white.withOpacity(0.1),
                            ),
                            child:
                                Text("$value", style: AppStyles.textSize12())),
                      );
                    }),
                  ),
                ),
              ),
            ),
            10.h,
            Row(
              children: [
                Expanded(
                    child: ButtonCustom(
                  onPressed: () {
                    _groupSelected = [];
                    _batchSelected = [];
                    _sectionSelected = [];
                    pop(context, result: {
                      "users": widget.users.toList(),
                      "groupSelected": _groupSelected,
                      "batchSelected": _batchSelected,
                      "sectionSelected": _sectionSelected,
                    });
                  },
                  isOutLine: true,
                  title: "key_cancel".tr(),
                )),
                10.w,
                Expanded(
                    child: ButtonCustom(
                  onPressed: () {
                    var list = widget.users.toList().where((element) {
                      if (_groupSelected.isNotEmpty) {
                        return _groupSelected.contains(element.prCGroupName);
                      }
                      return true;
                    }).where((element) {
                      if (_batchSelected.isNotEmpty) {
                        return _batchSelected.contains(element.prCourseName);
                      }
                      return true;
                    }).where((element) {
                      if (_sectionSelected.isNotEmpty) {
                        return _sectionSelected.contains(element.prSectionName);
                      }
                      return true;
                    }).toList();
                    pop(context, result: {
                      "users": list,
                      "groupSelected": _groupSelected,
                      "batchSelected": _batchSelected,
                      "sectionSelected": _sectionSelected,
                    });
                  },
                  title: "key_apply".tr(),
                )),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
