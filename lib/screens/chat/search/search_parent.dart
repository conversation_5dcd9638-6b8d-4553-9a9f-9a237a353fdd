import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/screens/chat/search/show_phone_number_modal.dart';
import 'package:chat_app/screens/chat/search/widgets/filter_dialog.dart';
import 'package:chat_app/utils/app_colors.dart';

import 'package:chat_app/utils/firestore_service.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:string_similarity/string_similarity.dart';

class SearchParent extends StatefulWidget {
  final List<UserModel> users;
  final bool isSelectMulti;
  final bool showAppBar;
  final String? title;
  final bool? showCall;
  final String? type;
  final Function(List<UserModel> users)? onNext;
  final bool showOrderBy;
  final bool hideMe;
  const SearchParent({
    super.key,
    this.users = const [],
    this.isSelectMulti = true,
    this.showAppBar = true,
    this.title,
    this.showCall,
    this.type,
    this.onNext,
    this.showOrderBy = true,
    this.hideMe = true,
  });

  @override
  State<SearchParent> createState() => SearchParentState();
}

class SearchParentState extends State<SearchParent>
    with AutomaticKeepAliveClientMixin {
  List<UserModel> _originalUsers = [];
  List<UserModel> users = [];
  String? _orderBy = "Name";

  late List<UserModel> _usersSelected = widget.users;
  // String? _type;
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = false;
  List<String> _groupSelected = [];
  List<String> _batchSelected = [];
  List<String> _sectionSelected = [];
  bool _selectAll = false;

  @override
  void initState() {
    _searchUser(fetchAll: true);
    super.initState();
  }

  _searchUser(
      {String? query,
      bool isFromLocal = true,
      bool fetchAll = false,
      bool sort = true}) async {
    try {
      setState(() {
        _isLoading = true;
      });
      bool isPhone = false;
      if (query != null) {
        isPhone = Helper.checkStringIsNumberFormat(query);
      }
      var profile = BlocProvider.of<AuthBloc>(context).state.userModel;
      users = await FirestoreService.searchUser(
        name: isPhone ? null : query,
        phoneNumber: isPhone ? query : null,
        schoolCode: profile?.schoolCode,
        fromLocal: isFromLocal,
        type: widget.type,
        fetchAll: fetchAll,
      );
      if (widget.hideMe == false) {
        var profile = BlocProvider.of<AuthBloc>(context).state.userModel;
        users.insert(0, profile!);
      }
      if (_originalUsers.isEmpty) {
        _originalUsers = users.toList();
      }
      if (fetchAll) {
        _originalUsers = users.toList();
      }
      if (sort) {
        _sort();
      }
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  /// Build order by chip widget
  Widget _buildOrderByChip(String value, String label, IconData icon) {
    bool isSelected = _orderBy == value;
    return GestureDetector(
      onTap: () {
        setState(() {
          _orderBy = value;
        });
        _sort();
      },
      child: AnimatedContainer(
        duration: Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(horizontal: 6, vertical: 8),
        decoration: BoxDecoration(
          color:
              isSelected ? AppColors.primary : AppColors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected
                ? AppColors.primary
                : AppColors.white.withOpacity(0.3),
            width: 1.5,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.3),
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 12,
              color: isSelected
                  ? AppColors.white
                  : AppColors.white.withOpacity(0.7),
            ),
            SizedBox(width: 6),
            Text(
              label,
              style: AppStyles.textSize10(
                color: isSelected
                    ? AppColors.white
                    : AppColors.white.withOpacity(0.8),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  _sort() {
    if (_orderBy != null) {
      switch (_orderBy) {
        case "Group":
          users.sort(
            (a, b) {
              return (a.prCGroupName ?? "").compareTo(b.prCGroupName ?? "");
            },
          );
          break;
        case "Batch":
          users.sort(
            (a, b) {
              return (a.prCourseName ?? "").compareTo(b.prCourseName ?? "");
            },
          );
          break;
        case "Section":
          users.sort(
            (a, b) {
              return (a.prSectionName ?? "").compareTo(b.prSectionName ?? "");
            },
          );
          break;
        case "Name":
          _sortByName();

          break;
      }
      setState(() {});
    }
  }

  List<UserModel> _sortByName() {
    if (_searchController.text.isEmpty) {
      users.sort(
        (a, b) {
          return (a.name ?? "").compareTo(b.name ?? "");
        },
      );
    } else {
      var value = _searchController.text.trim().toLowerCase();
      var number = int.tryParse(value);
      if (number != null) {
        if (number.toString().length >= 9) {
          users = _originalUsers
              .where((e) => "${e.contactOne}".contains(number.toString()))
              .toList();
        } else {
          users = _originalUsers
              .where((e) => "${e.sid}".contains(number.toString()))
              .toList();
        }
      } else {
        users = _originalUsers.map((user) {
          return user.copyWith(
              similarity: StringSimilarity.compareTwoStrings(
                  value, user.name?.toLowerCase()));
        }).toList();
        users = users
            .where((e) => "${e.name}".toLowerCase().startsWith(value))
            .toList();
        // users.removeWhere((e) => (e.similarity ?? 0) < 0.3);
      }

      // // Sort matches based on similarity in descending order
      users.sort((a, b) => (b.similarity ?? 0.0).compareTo(a.similarity ?? 0));
    }
    return users;
  }

  List<UserModel> getSelectedUsers() {
    return _usersSelected;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GestureDetector(
      onTap: () {
        hideKeyboard(context);
      },
      child: OverlayLoading(
        isLoading: _isLoading,
        child: Scaffold(
          floatingActionButton: widget.isSelectMulti
              ? FloatingActionButton(
                  onPressed: () async {
                    var result = await showDialog(
                        context: context,
                        builder: (_) {
                          return FilterDialog(
                            users: _originalUsers,
                            batchSelected: _batchSelected,
                            groupSelected: _groupSelected,
                            sectionSelected: _sectionSelected,
                          );
                        });
                    if (result != null) {
                      setState(() {
                        users = result['users'];
                        _groupSelected = result['groupSelected'];
                        _batchSelected = result['batchSelected'];
                        _sectionSelected = result['sectionSelected'];
                      });
                    }
                  },
                  child: Icon(
                    Icons.filter_alt_rounded,
                    color: Colors.white,
                  ),
                )
              : null,
          backgroundColor: AppColors.backgroundScaffold,
          appBar: widget.showAppBar
              ? CustomAppbar(
                  title: widget.title ?? "key_add_people_to_group".tr(),
                  actions: [
                    IconButton(
                        onPressed: () async {
                          await _searchUser(isFromLocal: false, fetchAll: true);
                        },
                        icon: const Icon(
                          Icons.sync,
                          color: AppColors.primary,
                        )),
                    if (widget.isSelectMulti)
                      TextButton(
                          onPressed: _usersSelected.isNotEmpty
                              ? () {
                                  if (widget.onNext != null) {
                                    widget.onNext!.call(_usersSelected);
                                  } else {
                                    pop(context, result: _usersSelected);
                                  }
                                }
                              : null,
                          child: Text(
                            "key_next".tr() + "(${_usersSelected.length})",
                            style: AppStyles.textSize14(),
                          ))
                  ],
                )
              : null,
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Container(
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: _searchController,
                    onChanged: (value) {
                      // Use setState to wrap all list modifications
                      setState(() {
                        if (value.isEmpty) {
                          users = _originalUsers.toList();
                        } else {
                          if (int.tryParse(value) != null) {
                            var number = int.tryParse(value);
                            if ((number?.toString().length ?? 0) >= 9) {
                              users = _originalUsers.where((element) {
                                return (element.contactOne
                                            ?.toString()
                                            .toLowerCase()
                                            .contains(value.toLowerCase()) ??
                                        false) ||
                                    (element.contactTwo
                                            ?.toString()
                                            .toLowerCase()
                                            .contains(value.toLowerCase()) ??
                                        false) ||
                                    (element.whatsappNumber
                                            ?.toString()
                                            .toLowerCase()
                                            .contains(value.toLowerCase()) ??
                                        false);
                              }).toList();
                            } else {
                              users = _originalUsers.toList().where((element) {
                                return element.sid == value;
                              }).toList();
                            }
                          } else {
                            var list = _sortByName();
                            users = list.toList();
                          }
                        }
                      });
                    },
                    style: AppStyles.textSize14(color: AppColors.black),
                    decoration: InputDecoration(
                      prefixIcon: Container(
                        padding: EdgeInsets.all(12),
                        child: Icon(
                          Icons.search,
                          color: AppColors.primary,
                          size: 20,
                        ),
                      ),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              icon: Icon(
                                Icons.clear,
                                color: AppColors.grey,
                                size: 20,
                              ),
                              onPressed: () {
                                setState(() {
                                  _searchController.clear();
                                  users = _originalUsers.toList();
                                });
                              },
                            )
                          : null,
                      contentPadding: EdgeInsets.symmetric(
                        vertical: 16,
                        horizontal: 16,
                      ),
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      filled: true,
                      fillColor: Colors.transparent,
                      hintStyle: AppStyles.textSize14(
                        color: AppColors.grey.withOpacity(0.7),
                      ),
                      hintText: "Search by name, ID, or phone number...",
                    ),
                  ),
                ),
              ),
              if (widget.showOrderBy)
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppColors.white.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.sort,
                            color: AppColors.white,
                            size: 18,
                          ),
                          SizedBox(width: 8),
                          Text(
                            "key_order_by".tr(),
                            style: AppStyles.textSize14(
                              color: AppColors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 12),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          _buildOrderByChip(
                              "Name", "key_name".tr(), Icons.person_outline),
                          _buildOrderByChip(
                              "Group", "key_group".tr(), Icons.group_outlined),
                          _buildOrderByChip(
                              "Batch", "key_batch".tr(), Icons.school_outlined),
                          _buildOrderByChip("Section", "key_section".tr(),
                              Icons.class_outlined),
                        ],
                      ),
                    ],
                  ),
                ),
              if (widget.isSelectMulti)
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          _selectAll = !_selectAll;
                          if (_selectAll) {
                            _usersSelected = users.toList();
                          } else {
                            _usersSelected = [];
                          }
                        });
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        decoration: BoxDecoration(
                          color: AppColors.white.withOpacity(0.05),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: AppColors.white.withOpacity(0.2),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            Container(
                              width: 24,
                              height: 24,
                              decoration: BoxDecoration(
                                color: _selectAll
                                    ? AppColors.primary
                                    : Colors.transparent,
                                borderRadius: BorderRadius.circular(6),
                                border: Border.all(
                                  color: _selectAll
                                      ? AppColors.primary
                                      : AppColors.white.withOpacity(0.5),
                                  width: 2,
                                ),
                              ),
                              child: _selectAll
                                  ? Icon(
                                      Icons.check,
                                      color: AppColors.white,
                                      size: 16,
                                    )
                                  : _usersSelected.isNotEmpty
                                      ? Container(
                                          margin: EdgeInsets.all(4),
                                          decoration: BoxDecoration(
                                            color: AppColors.primary,
                                            borderRadius:
                                                BorderRadius.circular(2),
                                          ),
                                        )
                                      : null,
                            ),
                            SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "key_select_all".tr(),
                                    style: AppStyles.textSize14(
                                      color: AppColors.white,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  if (_usersSelected.isNotEmpty)
                                    Text(
                                      "${_usersSelected.length} of ${users.length} selected",
                                      style: AppStyles.textSize12(
                                        color: AppColors.white.withOpacity(0.7),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                            Icon(
                              _selectAll
                                  ? Icons.check_circle
                                  : _usersSelected.isNotEmpty
                                      ? Icons.indeterminate_check_box
                                      : Icons.radio_button_unchecked,
                              color: _selectAll
                                  ? AppColors.primary
                                  : _usersSelected.isNotEmpty
                                      ? AppColors.primary
                                      : AppColors.white.withOpacity(0.5),
                              size: 20,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              // Padding(
              //   padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              //   child: Row(
              //     children: [
              //       Text(
              //         "key_type".tr() + ":",
              //         style: AppStyles.textSize14(),
              //       ),
              //       Wrap(
              //         children: [null, "parent", "student"].map((e) {
              //           return GestureDetector(
              //             onTap: () {
              //               setState(() {
              //                 _type = e;
              //               });
              //             },
              //             child: Container(
              //               margin: EdgeInsets.symmetric(horizontal: 4),
              //               padding:
              //                   EdgeInsets.symmetric(horizontal: 10, vertical: 4),
              //               decoration: BoxDecoration(
              //                 borderRadius: BorderRadius.circular(1000),
              //                 color: _type == e
              //                     ? AppColors.primary
              //                     : AppColors.white.withOpacity(0.2),
              //               ),
              //               child: Text(e ?? "key_all".tr(),
              //                   style: AppStyles.textSize12(
              //                     color: AppColors.white,
              //                   )),
              //             ),
              //           );
              //         }).toList(),
              //       )
              //     ],
              //   ),
              // ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: FutureBuilder(
                  future: LocalStorage()
                      .getStringValue(AppConstants.updateDataUserDate),
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      return Text(
                        "Updated on: ${Helper.formatDateTimeToString(context, DateTime.parse(snapshot.data!), dateFormat: DateFormat("MMM dd, yyyy hh:mm a"))}",
                        style: AppStyles.textSize12(
                          color: AppColors.grey,
                        ),
                      );
                    }
                    return Container();
                  },
                ),
              ),
              Expanded(
                child: ListView.separated(
                    itemBuilder: (_, index) {
                      // Bounds checking to prevent index out of range
                      if (index >= users.length) {
                        return Container();
                      }

                      var user = users[index];
                      bool isSelected = _usersSelected.contains(user);
                      return UserItem(
                          showPhoneCall: widget.showCall ?? true,
                          onTap: () {
                            if (widget.isSelectMulti) {
                              if (isSelected) {
                                _usersSelected.remove(user);
                              } else {
                                _usersSelected.add(user);
                              }
                              setState(() {});
                            } else {
                              pop(context, result: [user]);
                            }
                          },
                          user: user,
                          isSelectMulti: widget.isSelectMulti,
                          isSelected: isSelected);
                    },
                    separatorBuilder: (_, index) {
                      // Bounds checking for separator
                      if (index >= users.length - 1) {
                        return Container();
                      }

                      // return Divider(
                      //   height: 1,
                      //   color: AppColors.white,
                      // );
                      return SizedBox(
                        height: 4,
                      );
                    },
                    itemCount: users.length),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class UserItem extends StatelessWidget {
  final Function() onTap;
  final UserModel user;
  final bool isSelectMulti;
  final bool isSelected;
  final bool showPhoneCall;
  final bool showBirthdayWishes;
  final bool showSid;
  final BoxDecoration? decoration;
  final EdgeInsets? padding;
  const UserItem(
      {super.key,
      required this.onTap,
      required this.user,
      required this.isSelectMulti,
      required this.isSelected,
      this.showPhoneCall = true,
      this.showBirthdayWishes = false,
      this.showSid = true,
      this.decoration,
      this.padding});

  /// Get initials from user name
  String _getInitials(String? name) {
    if (name == null || name.isEmpty) return "?";

    List<String> words =
        name.trim().split(' ').where((word) => word.isNotEmpty).toList();

    if (words.length >= 2) {
      // Take first character of first two non-empty words
      String first = words[0].isNotEmpty ? words[0][0] : "";
      String second = words[1].isNotEmpty ? words[1][0] : "";
      if (first.isNotEmpty && second.isNotEmpty) {
        return "$first$second".toUpperCase();
      }
    }

    if (words.isNotEmpty && words[0].isNotEmpty) {
      return words[0][0].toUpperCase();
    }

    return "?";
  }

  /// Get avatar background color based on name
  Color _getAvatarColor(String? name) {
    if (name == null || name.isEmpty) return AppColors.grey;

    final colors = [
      AppColors.primary,
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
    ];

    try {
      int hash = name.hashCode;
      int index = hash.abs() % colors.length;
      return colors[index];
    } catch (e) {
      // Fallback to grey if any error occurs
      return AppColors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: isSelected
            ? AppColors.primary.withOpacity(0.1)
            : AppColors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border:
            isSelected ? Border.all(color: AppColors.primary, width: 1) : null,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: decoration,
          padding:
              padding ?? const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
          child: Row(
            children: [
              if (isSelectMulti)
                Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: Checkbox(
                      value: isSelected,
                      onChanged: (value) {
                        onTap();
                      }),
                ),
              Container(
                width: 45,
                height: 45,
                margin: EdgeInsets.only(right: 12),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _getAvatarColor(user.name),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: user.avatar != null && user.avatar!.isNotEmpty
                    ? ClipOval(
                        child: Image.network(
                          user.avatar!,
                          width: 45,
                          height: 45,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Center(
                              child: Text(
                                _getInitials(user.name),
                                style: AppStyles.textSize16(
                                  color: AppColors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            );
                          },
                        ),
                      )
                    : Center(
                        child: Text(
                          _getInitials(user.name),
                          style: AppStyles.textSize16(
                            color: AppColors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "${user.name}",
                      style: AppStyles.textSize14(color: AppColors.white),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Builder(builder: (context) {
                        if (user.type == AppConstants.teacherType) {
                          return Container(
                            margin: EdgeInsets.only(top: 2),
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 3),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withOpacity(0.15),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: AppColors.primary.withOpacity(0.4),
                                  width: 0.8,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    padding: EdgeInsets.all(2),
                                    decoration: BoxDecoration(
                                      color: AppColors.primary.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: SvgPicture.asset(
                                      AppAssets.glassesIcon,
                                      color: AppColors.primary,
                                      width: 12,
                                      height: 12,
                                    ),
                                  ),
                                  SizedBox(width: 6),
                                  Text(
                                    "key_teacher".tr(),
                                    style: AppStyles.textSize11(
                                      color: AppColors.primary,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }
                        if ((user.prCGroupName?.isNotEmpty ?? false) &&
                            (user.prCourseName?.isNotEmpty ?? false) &&
                            (user.prSectionName?.isNotEmpty ?? false)) {
                          return Container(
                            margin: EdgeInsets.only(top: 6),
                            child: Wrap(
                              spacing: 8,
                              runSpacing: 4,
                              children: [
                                if (showSid)
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 2),
                                    decoration: BoxDecoration(
                                      color: AppColors.primary.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color:
                                            AppColors.primary.withOpacity(0.3),
                                        width: 0.5,
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.badge_outlined,
                                          size: 12,
                                          color: AppColors.primary,
                                        ),
                                        SizedBox(width: 4),
                                        Text(
                                          "${user.sid}",
                                          style: AppStyles.textSize10(
                                            color: AppColors.primary,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: Colors.blue.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: Colors.blue.withOpacity(0.3),
                                      width: 0.5,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.group_outlined,
                                        size: 12,
                                        color: Colors.blue,
                                      ),
                                      SizedBox(width: 4),
                                      Text(
                                        "${user.prCGroupName}",
                                        style: AppStyles.textSize10(
                                          color: Colors.blue,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: Colors.green.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: Colors.green.withOpacity(0.3),
                                      width: 0.5,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.school_outlined,
                                        size: 12,
                                        color: Colors.green,
                                      ),
                                      SizedBox(width: 4),
                                      Text(
                                        "${user.prCourseName}",
                                        style: AppStyles.textSize10(
                                          color: Colors.green,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: Colors.orange.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: Colors.orange.withOpacity(0.3),
                                      width: 0.5,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.class_outlined,
                                        size: 12,
                                        color: Colors.orange,
                                      ),
                                      SizedBox(width: 4),
                                      Text(
                                        "${user.prSectionName}",
                                        style: AppStyles.textSize10(
                                          color: Colors.orange,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        }
                        return Container();
                      }),
                    ),
                    if (showBirthdayWishes &&
                        (user.dateOfSentBirthdayWishes?.isNotEmpty ?? false))
                      Text(
                        "key_sent_birthday_wishes_on".tr().replaceAll(
                            "{date}", "${user.dateOfSentBirthdayWishes}"),
                        style: AppStyles.textSize12(
                          color: AppColors.primary,
                        ),
                      )
                  ],
                ),
              ),
              if (showPhoneCall)
                IconButton(
                  onPressed: () {
                    showModalBottomSheet(
                        context: context,
                        backgroundColor: Colors.transparent,
                        builder: (_) {
                          return ShowPhoneNumberModal(userModel: user);
                        });
                  },
                  icon: Icon(
                    Icons.call,
                    color: AppColors.primary,
                  ),
                )
            ],
          ),
        ),
      ),
    );
  }
}
