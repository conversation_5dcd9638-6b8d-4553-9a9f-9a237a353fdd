import 'package:chat_app/export.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/chat_attachment_modal.dart';
import 'package:flutter/material.dart';

import 'package:url_launcher/url_launcher_string.dart';

import '../../../utils/app_colors.dart';

class ShowPhoneNumberModal extends StatelessWidget {
  final UserModel userModel;
  const ShowPhoneNumberModal({super.key, required this.userModel});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(5),
          topRight: Radius.circular(5),
        ),
        color: AppColors.black,
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (userModel.contactOne != null)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: GestureDetector(
                  onTap: () async {
                    launchUrlString("tel://${userModel.contactOne}");
                  },
                  child: Item(
                    title: "${userModel.contactOne} - Main",
                    iconPath: AppAssets.callIcon,
                  ),
                ),
              ),
            if (userModel.contactTwo != null)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: GestureDetector(
                  onTap: () async {
                    launchUrlString("tel://${userModel.contactTwo}");
                  },
                  child: Item(
                    title: "${userModel.contactTwo} - Secondary",
                    iconPath: AppAssets.callIcon,
                  ),
                ),
              ),
            if (userModel.whatsappNumber != null)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: GestureDetector(
                  onTap: () async {
                    launchUrlString("tel://${userModel.whatsappNumber}");
                  },
                  child: Item(
                    title: "${userModel.whatsappNumber} - Whatsapp",
                    iconPath: AppAssets.callIcon,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
