import 'package:cached_network_image/cached_network_image.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/app_styles.dart';
import 'package:chat_app/utils/extensions.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class Search extends StatefulWidget {
  const Search({super.key});

  @override
  State<Search> createState() => _SearchState();
}

class _SearchState extends State<Search> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundScaffold,
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      style: AppStyles.textSize14(),
                      decoration: InputDecoration(
                        hintStyle: AppStyles.textSize14(
                          color: Colors.grey,
                        ),
                        constraints: BoxConstraints(maxHeight: 45),
                        contentPadding: EdgeInsets.symmetric(vertical: 0),
                        prefixIcon: Icon(
                          Icons.search,
                          color: Colors.grey,
                        ),
                        hintText: "key_search".tr(),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        fillColor: AppColors.white.withOpacity(0.3),
                        filled: true,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text(
                      "key_cancel".tr(),
                      style: AppStyles.textSize14(
                        color: AppColors.primary,
                      ),
                    ),
                  )
                ],
              ),
              Expanded(
                  child: ListView.separated(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      itemBuilder: (_, index) {
                        return Item();
                      },
                      separatorBuilder: (_, index) {
                        return Divider(
                          height: 10,
                          color: AppColors.white,
                        );
                      },
                      itemCount: 10))
            ],
          ),
        ),
      ),
    );
  }
}

class Item extends StatelessWidget {
  const Item({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: CachedNetworkImage(
              fit: BoxFit.cover,
              imageUrl:
                  "https://upload.wikimedia.org/wikipedia/commons/3/31/Room-education-classroom-children-library-students-1237486.jpg",
              width: 30,
              height: 30,
            ),
          ),
          10.w,
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "General chat",
                style: AppStyles.textSize14(),
              ),
              Text(
                "4/9/2023",
                style: AppStyles.textSize12(
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
