import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/screens/chat/search/search_parent.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/firestore_service.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SendBulkMessages extends StatefulWidget {
  final List<UserModel> students;
  const SendBulkMessages({super.key, required this.students});

  @override
  State<SendBulkMessages> createState() => _SendBulkMessagesState();
}

class _SendBulkMessagesState extends State<SendBulkMessages> {
  final TextEditingController _messageController = TextEditingController();
  late List<UserModel> _students = widget.students.toList();
  DateTime _dateTime = DateTime.now();
  bool _isLoading = false;
  @override
  void initState() {
    super.initState();
  }

  _submit() async {
    try {
      Helper.showDialogSuccessMessages(
          context: context,
          message: "messagesSubmittedSuccessfully".tr(),
          onPressPrimaryButton: () async {
            Navigator.popUntil(context, (route) => route.isFirst);
          });
      await FirestoreService.sendBulkMessages(
        message: _messageController.text,
        dateTime: _dateTime,
        students: _students,
        profile: BlocProvider.of<AuthBloc>(context).state.userModel!,
      );
    } catch (e) {
      showToast(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard(context);
      },
      child: OverlayLoading(
        isLoading: _isLoading,
        child: Scaffold(
          appBar: CustomAppbar(
            title: "key_send_bulk_messages".tr(),
            actions: [
              IconButton(
                onPressed: () async {
                  var result = await push(
                      context,
                      SearchParent(
                        isSelectMulti: true,
                        type: AppConstants.studentType,
                        title: "key_send_bulk_messages".tr(),
                        users: _students,
                      ));
                  if (result != null) {
                    setState(() {
                      _students = result;
                    });
                  }
                },
                icon: Icon(
                  Icons.add,
                  color: AppColors.white,
                ),
              ),
            ],
          ),
          bottomNavigationBar: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                child: ButtonCustom(
                  enabled: _messageController.text.isNotEmpty,
                  onPressed: _submit,
                  title: "key_send".tr() + " (${_students.length})",
                ),
              ),
            ],
          ),
          body: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Wrap(
                  //   spacing: 10,
                  //   runSpacing: 10,
                  //   children: List.generate(_students.length + 1, (index) {
                  //     if (index == _students.length) {
                  //       return GestureDetector(
                  //         onTap: () async {
                  //           var result = await push(
                  //               context,
                  //               SearchParent(
                  //                 isSelectMulti: true,
                  //                 type: AppConstants.studentType,
                  //                 title: "key_send_bulk_messages".tr(),
                  //                 users: _students,
                  //               ));
                  //           if (result != null) {
                  //             setState(() {
                  //               _students = result;
                  //             });
                  //           }
                  //         },
                  //         child: Container(
                  //           height: 46,
                  //           width: 80,
                  //           child: Icon(Icons.add),
                  //           decoration: BoxDecoration(
                  //             borderRadius: BorderRadius.circular(4),
                  //             color: AppColors.white.withOpacity(0.2),
                  //           ),
                  //         ),
                  //       );
                  //     }
                  //     var user = _students[index];
                  //     return Container(
                  //       height: 46,
                  //       padding:
                  //           EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                  //       decoration: BoxDecoration(
                  //         color: AppColors.white.withOpacity(0.2),
                  //         borderRadius: BorderRadius.circular(4),
                  //       ),
                  //       child: Row(
                  //         mainAxisSize: MainAxisSize.min,
                  //         children: [
                  //           GestureDetector(
                  //             onTap: () {
                  //               _students.removeAt(index);
                  //               setState(() {});
                  //             },
                  //             child: Padding(
                  //               padding: const EdgeInsets.only(right: 4),
                  //               child: Icon(
                  //                 Icons.remove_circle,
                  //                 color: AppColors.primary,
                  //               ),
                  //             ),
                  //           ),
                  //           Column(
                  //             crossAxisAlignment: CrossAxisAlignment.start,
                  //             children: [
                  //               Text(
                  //                 "${user.name}",
                  //                 style: AppStyles.textSize14(),
                  //               ),
                  //               Text(
                  //                 "${user.prCGroupName} - ${user.prCourseName} - ${user.prSectionName}",
                  //                 style: AppStyles.textSize12(),
                  //               )
                  //             ],
                  //           ),
                  //         ],
                  //       ),
                  //     );
                  //   }),
                  // ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: TextField(
                      minLines: 15,
                      maxLines: 20,
                      onChanged: (value) {
                        setState(() {});
                      },
                      style: AppStyles.textSize16(),
                      controller: _messageController,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: "key_type_something".tr(),
                      ),
                    ),
                  ),
                ],
              )),
        ),
      ),
    );
  }
}
