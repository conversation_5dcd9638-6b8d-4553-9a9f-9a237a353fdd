import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/repositories/education_repositories.dart';
import 'package:chat_app/screens/chat/create_user/create_user.dart';
import 'package:chat_app/screens/chat/search/search_parent.dart';
import 'package:chat_app/screens/chat/update_user/update_user.dart';
import 'package:chat_app/screens/menu/manage_class/manage_class.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/firestore_service.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ManageUsers extends StatefulWidget {
  const ManageUsers({super.key});

  @override
  State<ManageUsers> createState() => _ManageUsersState();
}

class _ManageUsersState extends State<ManageUsers> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(builder: (context, state) {
      return Scaffold(
          backgroundColor: AppColors.backgroundScaffold,
          appBar: CustomAppbar(
            title: "manage_users".tr(),
            isShowLeadingIcon: false,
          ),
          body: Padding(
            padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
            child: Column(
              spacing: 20,
              children: [
                GestureDetector(
                  onTap: () {
                    push(context, CreateUser());
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      color: AppColors.white.withOpacity(0.2),
                    ),
                    width: getWidth(context),
                    height: 100,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 10),
                          child: Icon(
                            Icons.add_circle_outline,
                            size: 50,
                          ),
                        ),
                        Text(
                          "add_user".tr(),
                          style: AppStyles.textSize17(
                            fontWeight: FontWeight.w500,
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    showDialog(
                        context: context,
                        builder: (context) => AddUserByIdDialog());
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      color: AppColors.white.withOpacity(0.2),
                    ),
                    width: getWidth(context),
                    height: 100,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 10),
                          child: Icon(
                            Icons.cloud_download_outlined,
                            size: 50,
                          ),
                        ),
                        Text(
                          "add_user_by_id".tr(),
                          style: AppStyles.textSize17(
                            fontWeight: FontWeight.w500,
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () async {
                    final result = await push(
                        context,
                        SearchParent(
                          users: [],
                          hideMe: false,
                          title: "update_user".tr(),
                          isSelectMulti: false,
                          showCall: false,
                        ));
                    if (result != null) {
                      push(
                          context,
                          UpdateUser(
                            userModel: result[0],
                          ));
                    }
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      color: AppColors.white.withOpacity(0.2),
                    ),
                    width: getWidth(context),
                    height: 100,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 10),
                          child: Icon(
                            Icons.update,
                            size: 50,
                          ),
                        ),
                        Text(
                          "update_user".tr(),
                          style: AppStyles.textSize17(
                            fontWeight: FontWeight.w500,
                          ),
                        )
                      ],
                    ),
                  ),
                )
              ],
            ),
          ));
    });
  }
}

class AddUserByIdDialog extends StatefulWidget {
  const AddUserByIdDialog({super.key});

  @override
  State<AddUserByIdDialog> createState() => _AddUserByIdDialogState();
}

class _AddUserByIdDialogState extends State<AddUserByIdDialog> {
  final TextEditingController _controller = TextEditingController();
  final EducationRepositories _educationRepository = EducationRepositories();
  bool _isLoading = false;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  UserModel? _userModel;
  int _currentYear = DateTime.now().year;
  late final List<String> _listYearRange = [-1, 0, 1].map((e) {
    var year = _currentYear + e;
    var nexYear = year + 1;
    return "$year-${nexYear.toString().substring(2)}";
  }).toList();
  late String _selectedYearRange = _listYearRange[1];

  @override
  void initState() {
    super.initState();
  }

  _addUserById() async {
    FocusScope.of(context).unfocus();
    try {
      if (_controller.text.isEmpty) {
        showToast("please_enter_student_id".tr());
        return;
      }
      setState(() {
        _isLoading = true;
      });
      final result = await _educationRepository.addUserById(
          schoolCode: BlocProvider.of<AuthBloc>(context).state.schoolName!,
          studentId: _controller.text,
          year: _selectedYearRange);
      if (result != null) {
        _userModel = result;
      } else {
        showToast("user_not_found".tr());
      }
    } catch (e) {
      showToast(e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  _saveUser() async {
    try {
      if (_userModel == null) {
        showToast("user_not_found".tr());
        return;
      }
      setState(() {
        _isLoading = true;
      });
      final result = await FirestoreService.addUser(_userModel!.toMap());
      if (result.isNotEmpty) {
        Navigator.pop(context);
      } else {
        showToast("error_adding_user".tr());
      }
    } catch (e) {
      showToast(e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return OverlayLoading(
      isLoading: _isLoading,
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        child: Form(
          key: _formKey,
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              spacing: 20,
              children: [
                Text("add_user_by_id".tr()),
                DropdownMenu<String>(
                  initialSelection: _selectedYearRange,
                  label: Text(
                    "Academic year",
                  ),
                  dropdownMenuEntries: List.generate(
                    3,
                    (index) {
                      return DropdownMenuEntry(
                          value: _listYearRange[index],
                          label: _listYearRange[index]);
                    },
                  ),
                  onSelected: (value) {
                    setState(() {
                      _selectedYearRange = value!;
                    });
                  },
                ),
                Row(
                  spacing: 10,
                  children: [
                    Expanded(
                      child: TextFormField(
                        validator: (value) {
                          if (value!.isEmpty) {
                            return "please_enter_student_id".tr();
                          }
                          return null;
                        },
                        controller: _controller,
                        decoration: InputDecoration(
                          hintText: "please_enter_student_id".tr(),
                        ),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: _addUserById,
                      child: Text(
                        "search".tr(),
                        style: AppStyles.textSize17(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    )
                  ],
                ),
                if (_userModel != null)
                  StudentPromoteItem(
                    showCheckbox: false,
                    selected: true,
                    student: _userModel!,
                    onChanged: (value) {},
                  ),
                Row(
                  spacing: 20,
                  children: [
                    Expanded(
                      child: ButtonCustom(
                        isOutLine: true,
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        title: "cancel".tr(),
                      ),
                    ),
                    Expanded(
                      child: ButtonCustom(
                        onPressed: _saveUser,
                        title: "save".tr(),
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
