import 'package:chat_app/widgets/custom_appbar.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebViewApp extends StatefulWidget {
  final String url;
  final String title;
  final bool hideAppBarWhenMainUrlChange;
  final String? mainUrl;
  const WebViewApp(
      {super.key,
      required this.url,
      required this.title,
      this.hideAppBarWhenMainUrlChange = false,
      this.mainUrl = "manage_exam.php"});

  @override
  State<WebViewApp> createState() => _WebViewAppState();
}

class _WebViewAppState extends State<WebViewApp> {
  bool hideAppBar = false;
  late WebViewController controller = WebViewController()
    ..setJavaScriptMode(JavaScriptMode.unrestricted)
    ..setNavigationDelegate(
      NavigationDelegate(
        onUrlChange: (change) {
          // print("Page started loading: $url");

          var url = change.url ?? "";
          if (url.contains(widget.mainUrl ?? "not_found") == false) {
            if (widget.hideAppBarWhenMainUrlChange) {
              setState(() {
                hideAppBar = true;
              });
            }
          } else {
            setState(() {
              hideAppBar = false;
            });
          }
        },
      ),
    )
    ..loadRequest(Uri.parse(widget.url));
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: hideAppBar
          ? null
          : CustomAppbar(
              title: widget.title,
            ),
      body: SafeArea(child: WebViewWidget(controller: controller)),
    );
  }
}
