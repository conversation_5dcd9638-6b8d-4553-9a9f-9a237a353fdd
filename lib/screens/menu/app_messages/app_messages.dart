import 'package:chat_app/export.dart';
import 'package:chat_app/screens/chat/absentees_messages/absentees_mesages.dart';
import 'package:chat_app/screens/chat/search/search_parent.dart';
import 'package:chat_app/screens/chat/send_birthday/send_birthday.dart';
import 'package:chat_app/screens/chat/send_bulk_messages/send_bulk_messages.dart';
import 'package:chat_app/screens/settings/settings.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/assets.dart';
import 'package:flutter/material.dart';

class AppMessages extends StatefulWidget {
  const AppMessages({super.key});

  @override
  State<AppMessages> createState() => _AppMessagesState();
}

class _AppMessagesState extends State<AppMessages> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundScaffold,
      appBar: CustomAppbar(
        title: "app_messages".tr(),
        isShowLeadingIcon: false,
      ),
      body: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        decoration: BoxDecoration(
          color: AppColors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(5),
        ),
        child: Column(mainAxisSize: MainAxisSize.min, children: [
          SettingItem(
              suffixIcon: const Icon(Icons.keyboard_arrow_right),
              padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
              onTap: () {
                push(
                    context,
                    SearchParent(
                      showOrderBy: false,
                      isSelectMulti: true,
                      type: AppConstants.studentType,
                      title: "key_send_bulk_messages".tr(),
                      showCall: false,
                      users: [],
                      onNext: (users) {
                        push(
                            context,
                            SendBulkMessages(
                              students: users,
                            ));
                      },
                    ));
              },
              title: "key_send_bulk_messages".tr(),
              iconPath: null),
          Divider(
            height: 1,
            color: AppColors.white,
          ),
          SettingItem(
              suffixIcon: const Icon(Icons.keyboard_arrow_right),
              padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
              onTap: () {
                push(context, SendBirthday());
              },
              title: "key_send_birthday_wishes".tr(),
              iconPath: null),
          Divider(
            height: 1,
            color: AppColors.white,
          ),
          SettingItem(
              suffixIcon: const Icon(Icons.keyboard_arrow_right),
              padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
              onTap: () {
                push(
                    context,
                    AbsenteesMessages(
                      students: [],
                    ));
              },
              title: "key_absenteees_messages".tr(),
              iconPath: null),
        ]),
      ),
    );
  }
}
