import 'package:chat_app/blocs/import_excel/import_excel_cubit.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/models/education/student_class_model.dart';
import 'package:chat_app/screens/menu/manage_class/manage_class.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/app_styles.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:chat_app/utils/helper.dart';
import 'package:chat_app/widgets/button_custom.dart';
import 'package:chat_app/widgets/overlay_loading.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:excel/excel.dart' as excel;
// import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:share_plus/share_plus.dart';

class ImportExcelPromoteClass extends StatefulWidget {
  const ImportExcelPromoteClass({super.key});

  @override
  State<ImportExcelPromoteClass> createState() =>
      _ImportExcelPromoteClassState();
}

class _ImportExcelPromoteClassState extends State<ImportExcelPromoteClass> {
  final ImportExcelCubit _importExcelCubit = ImportExcelCubit();
  List<StudentClassModel> _studentClasses = [];
  bool _isLoading = false;
  String _log = "";
  List<UserModel> _students = [];
  _downloadTemplateExcel() async {
    _importExcelCubit.downloadTemplateExcel(
      onSuccess: (file) async {
        if (file != null) {
          final params = ShareParams(
            text: "Template Excel",
            files: [XFile(file.path)],
          );
          await SharePlus.instance.share(params);
        } else {
          showToast("File download not supported on this platform");
        }
      },
      onError: (error) {
        showToast(error);
      },
    );
  }

  _getStudents() async {
    try {
      setState(() {
        _isLoading = true;
      });
      _students = await FirestoreService.searchUser(
          type: "student", fetchAll: true, fromLocal: false);

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  _importExcel() async {
    try {
      var result = await FilePicker.platform
          .pickFiles(type: FileType.custom, allowedExtensions: ['xlsx', 'xls']);
      if (result != null) {
        var file = result.files.first;
        var excelDecode =
            excel.Excel.decodeBytes(await file.xFile.readAsBytes());
        var sheet = excelDecode.sheets.values.first;

        // columns: 0: sid, 1: studentName, 2: prCourseName, 3: prSectionName, 4: prCGroupName  5: prAcYear,
        if (sheet.maxRows > 1) {
          for (var i = 1; i < sheet.maxRows; i++) {
            var data = sheet.row(i);
            var sid = data[0]!.value.toString();
            var studentName = data[1]?.value.toString();
            String? prCourseName = data[2]?.value.toString();
            String? prSectionName = data[3]?.value.toString();
            String? prCGroupName = data[4]?.value.toString();
            String? prAcYear = data[5]?.value.toString();
            String? schoolCode = data[6]?.value.toString();
            if (schoolCode != FirebaseFirestore.instance.app.name) {
              if (mounted) {
                Helper.showDialogSuccessMessages(
                  context: context,
                  title: "Error",
                  message: "School code not match",
                  onPressPrimaryButton: () {
                    Navigator.pop(context, true);
                  },
                );

                break;
              }
            }
            var studentClass = StudentClassModel(
              sid: sid,
              studentName: studentName,
              prCourseName: prCourseName,
              prSectionName: prSectionName,
              prCGroupName: prCGroupName,
              prAcYear: prAcYear,
              docId: '',
              updatedAt: DateTime.now().toUtc().toIso8601String(),
            );
            _studentClasses.add(studentClass);
          }
          setState(() {});
        } else {
          showToast("No data found");
        }
      } else {
        showToast("Please select a file");
      }
    } catch (e) {
      showToast(e.toString());
    }
  }

  @override
  void initState() {
    _getStudents();
    super.initState();
  }

  _startImport() async {
    if (_studentClasses.isNotEmpty) {
      try {
        setState(() {
          _isLoading = true;
        });
        for (int i = 0; i < _studentClasses.length; i++) {
          var studentClass = _studentClasses[i];
          var index = _students
              .indexWhere((element) => element.sid == studentClass.sid);
          if (index != -1) {
            _log = "Updated ${i + 1} of ${_studentClasses.length}";
            var student = _students[index];
            await FirestoreService.createOrUpdateStudentClass(studentClass);
            await FirestoreService.updateUser(docId: student.docId!, data: {
              "updated_at": DateTime.now().toUtc().toIso8601String(),
            });

            setState(() {});
          }
        }

        setState(() {
          _log = "";
          _isLoading = false;
        });
        if (mounted) {
          Helper.showDialogSuccessMessages(
            context: context,
            title: "Success",
            message: "Imported successfully",
            onPressPrimaryButton: () {
              Navigator.pop(context, true);
            },
          );
        }
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        showToast(e.toString());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    var students = _students.where((element) {
      return _studentClasses.any((e) => e.sid == element.sid);
    }).toList();
    return BlocBuilder<ImportExcelCubit, ImportExcelState>(
        bloc: _importExcelCubit,
        builder: (context, state) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('Import Data from Excel'),
            ),
            bottomNavigationBar: BottomAppBar(
              child: ButtonCustom(
                onPressed: () {
                  _startImport();
                },
                enabled: students.isNotEmpty,
                title: "Start Import",
              ),
            ),
            body: OverlayLoading(
              isLoading: _isLoading || state.isLoading,
              customLoading: Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Center(
                  child: Column(
                    spacing: 8,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const CircularProgressIndicator(
                        color: AppColors.primary,
                      ),
                      if (_log.isNotEmpty)
                        Text(
                          _log,
                          maxLines: 10,
                          style: AppStyles.textSize10(
                            color: AppColors.black,
                          ),
                        )
                    ],
                  ),
                ),
              ),
              child: Center(
                child: Builder(builder: (context) {
                  if (students.isNotEmpty) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 10,
                      children: [
                        Text(
                          "Total Students: ${students.length}",
                          style: AppStyles.textSize14(),
                        ),
                        Expanded(
                          child: ListView.builder(
                            itemCount: students.length,
                            itemBuilder: (context, index) {
                              var student = students[index];
                              return StudentPromoteItem(
                                showCheckbox: false,
                                selected: true,
                                student: student,
                                onChanged: (value) {},
                              );
                            },
                          ),
                        ),
                      ],
                    );
                  }
                  return Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      spacing: 10,
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        GestureDetector(
                          onTap: _importExcel,
                          child: Container(
                            height: 150,
                            decoration: BoxDecoration(
                              color: Colors.transparent,
                              border: Border.all(color: AppColors.primary),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            padding: const EdgeInsets.all(10),
                            child: Center(
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                spacing: 8,
                                children: [
                                  const Icon(Icons.upload_file),
                                  Text(
                                    "Import Data from Excel",
                                    style: AppStyles.textSize14(),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        ButtonCustom(
                          onPressed: () {
                            _downloadTemplateExcel();
                          },
                          title: "",
                          child: SizedBox(
                            width: 250,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              spacing: 10,
                              children: [
                                const Icon(Icons.download),
                                const Text("Download Template Excel"),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ),
            ),
          );
        });
  }
}
