import 'dart:math';

import 'package:chat_app/export.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/models/education/student_class_model.dart';
import 'package:chat_app/screens/menu/manage_class/import_excel_promote_class.dart';
import 'package:chat_app/screens/menu/manage_class/log_history_class.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/app_styles.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:chat_app/utils/helper.dart';
import 'package:chat_app/utils/merge_data_utils.dart';
import 'package:chat_app/widgets/overlay_loading.dart';
import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class ManageClass extends StatefulWidget {
  const ManageClass({super.key});

  @override
  State<ManageClass> createState() => _ManageClassState();
}

class _ManageClassState extends State<ManageClass> {
  bool _isLoading = false;
  String _logInfo = "";
  List<StudentClassModel> _studentClasses = [];
  List<UserModel> _students = [];
  List<String> _groupSelected = [];
  List<String> _batchSelected = [];
  List<String> _sectionSelected = [];
  List<String> _groups = [];
  List<String> _batchs = [];
  List<String> _sections = [];
  List<UserModel> _studentsFiltered = [];
  List<UserModel> _studentsSelected = [];

  int _currentYear = DateTime.now().year;
  late final List<String> _listYearRange = [-1, 0, 1].map((e) {
    var year = _currentYear + e;
    var nexYear = year + 1;
    return "$year-${nexYear.toString().substring(2)}";
  }).toList();
  late String _selectedYearRange = _listYearRange[1];
  bool _merge = false;
  _mergeData() async {
    try {
      setState(() {
        _isLoading = true;
      });
      await MergeDataUtils.mergeDataStudentClass(onLog: (log) {
        setState(() {
          _logInfo = log;
        });
      });
      setState(() {
        _isLoading = false;
      });
      _getData();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  @override
  void initState() {
    _getData();
    super.initState();
  }

  _getData() async {
    try {
      setState(() {
        _isLoading = true;
      });
      _studentClasses = await FirestoreService.getAllStudentClass();
      if (_studentClasses.isEmpty) {
        _merge = false;
      } else {
        _merge = true;
      }

      var groupData = groupBy(_studentClasses, (p0) => p0.sid);
      List<StudentClassModel> list = [];
      for (var key in groupData.keys) {
        var data = groupData[key]?.toList() ?? [];
        // get lasted data
        data.sort((a, b) => a.updatedAt!.compareTo(b.updatedAt!));
        list.add(data.last);
      }
      _studentClasses = _studentClasses.where((e) {
        return _selectedYearRange == e.prAcYear;
      }).toList();
      _studentClasses = list;

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
    await _getStudents();
    _filterStudent();
  }

  _getStudents() async {
    try {
      setState(() {
        _isLoading = true;
      });
      _students = await FirestoreService.searchUser(
          type: "student", fetchAll: true, fromLocal: false);

      {
        var x = groupBy(_studentClasses, (p0) => p0.prCGroupName);
        _groups = x.keys
            .whereType<String>()
            .toList()
            .where((e) => e != "" && e != "null")
            .toList();
        _groups.sort();
      }
      {
        var x = groupBy(_studentClasses, (p0) => p0.prCourseName);
        _batchs = x.keys
            .whereType<String>()
            .toList()
            .where((e) => e != "" && e != "null")
            .toList();

        _batchs.sort();
      }
      {
        var x = groupBy(_studentClasses, (p0) => p0.prSectionName);
        _sections = x.keys
            .whereType<String>()
            .toList()
            .where((e) => e != "" && e != "null")
            .toList();

        _sections.sort();
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  _filterBatchByGroup() {
    if (_groupSelected.isEmpty) {
      return _batchs;
    } else {
      var list = _studentClasses.toList().where((element) {
        if (_groupSelected.isNotEmpty) {
          return _groupSelected.contains(element.prCGroupName);
        }
        return true;
      }).toList();
      {
        var x = groupBy(list, (p0) => p0.prCourseName);
        var filterBatchs = x.keys.whereType<String>().toList();
        filterBatchs.sort();
        return filterBatchs;
      }
    }
  }

  _filterStudent() {
    var list = _studentClasses.where((e) {
      if (_batchSelected.isNotEmpty) {
        return _groupSelected.contains(e.prCGroupName);
      }
      return true;
    }).toList();
    list = list.where((e) {
      if (_batchSelected.isNotEmpty) {
        return _batchSelected.contains(e.prCourseName);
      }
      return true;
    }).toList();
    list = list.where((e) {
      if (_sectionSelected.isNotEmpty) {
        return _sectionSelected.contains(e.prSectionName);
      }
      return true;
    }).toList();

    list = list.where((e) {
      return _selectedYearRange == e.prAcYear;
    }).toList();
    _studentsFiltered = [];
    for (var studentClass in list) {
      var index = _students.indexWhere((e) => e.sid == studentClass.sid);
      if (index != -1) {
        _studentsFiltered.add(_students[index]);
      }
    }
    // _studentsFiltered.sort();
    _studentsSelected.clear();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    var filterBatchs = _filterBatchByGroup();

    return Scaffold(
      appBar: CustomAppbar(
        title: "Promote Student",
        // actions: [
        //   IconButton(
        //     onPressed: () async {
        //       var result = await showDialog(
        //         context: context,
        //         builder: (context) => ConfirmMergeDataDialog(),
        //       );
        //       if (result == true) {
        //         setState(() {
        //           _studentClasses = [];
        //         });
        //         await _mergeData();
        //       }
        //     },
        //     icon: const Icon(Icons.merge_type),
        //   ),
        //   IconButton(
        //     onPressed: () async {
        //       await _getData();
        //       _filterStudent();
        //     },
        //     icon: const Icon(Icons.refresh),
        //   ),
        //   IconButton(
        //     onPressed: () {
        //       push(context, const LogHistoryClass());
        //     },
        //     icon: const Icon(Icons.history),
        //   ),
        // ],

        actions: [
          PopupMenuButton(
              icon: const Icon(Icons.menu_sharp),
              itemBuilder: (context) {
                return [
                  PopupMenuItem(
                    onTap: () async {
                      var result = await showDialog(
                        context: context,
                        builder: (context) => ConfirmMergeDataDialog(),
                      );
                      if (result == true) {
                        setState(() {
                          _studentClasses = [];
                        });
                        await _mergeData();
                      }
                    },
                    child: Row(
                      spacing: 8,
                      children: [
                        Icon(Icons.merge_type),
                        const Text("Merge Data"),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    onTap: () async {
                      await _getData();
                      _filterStudent();
                    },
                    child: Row(
                      spacing: 8,
                      children: [
                        Icon(Icons.refresh),
                        const Text("Refresh"),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    onTap: () async {
                      push(context, const LogHistoryClass());
                    },
                    child: Row(
                      spacing: 8,
                      children: [
                        Icon(Icons.history),
                        const Text("History"),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    onTap: () async {
                      var result =
                          await push(context, const ImportExcelPromoteClass());
                      if (result == true) {
                        _getData();
                      }
                    },
                    child: Row(
                      spacing: 8,
                      children: [
                        Icon(Icons.file_present),
                        const Text("Import Data From Excel"),
                      ],
                    ),
                  ),
                ];
              })
        ],
      ),
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ButtonCustom(
                onPressed: () async {
                  if (_studentsSelected.isEmpty) {
                    showToast("Please select students");
                    return;
                  }
                  if (_groupSelected.isEmpty) {
                    showToast("Please select group");
                    return;
                  }
                  if (_batchSelected.isEmpty) {
                    showToast("Please select batch");
                    return;
                  }
                  if (_sectionSelected.isEmpty) {
                    showToast("Please select section");
                    return;
                  }

                  final result = await showDialog(
                    context: context,
                    builder: (context) => UpdateStudentClassDialog(
                      title: "Promote Students",
                      studentClasses: _studentClasses,
                      students: _studentsSelected,
                      groups: _groups,
                      batches: _batchs,
                      sections: _sections,
                      currentGroup: _groupSelected.first,
                      currentBatch: _batchSelected.first,
                      currentSection: _sectionSelected.first,
                      selectedYearRange: _selectedYearRange,
                    ),
                  );
                  if (result == true) {
                    await _getData();
                    _filterStudent();
                  }
                },
                title: "Promote Class",
              ),
            ],
          ),
        ),
      ),
      body: OverlayLoading(
        isLoading: _isLoading,
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Builder(builder: (_) {
            if (_merge == false) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    spacing: 10,
                    children: [
                      Icon(
                        Icons.warning_amber_rounded,
                        size: 40,
                      ),
                      Text(
                        "No student class found, press button below generate class data",
                        style: AppStyles.textSize14(),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 10),
                      ElevatedButton(
                        onPressed: _mergeData,
                        child: const Text('Generate Class Data'),
                      ),
                      Column(
                        spacing: 4,
                        children: [
                          if (_logInfo.isNotEmpty)
                            Text(
                              "Don't interrupt the process, it may take a while",
                              style: AppStyles.textSize12(
                                color: Colors.red,
                              ),
                            ),
                          Text(
                            _logInfo,
                            style: AppStyles.textSize12(),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            }
            return ListView(
              // spacing: 10,
              // crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child: Row(
                    spacing: 10,
                    children: [
                      Expanded(
                        child: DropdownMenu<String>(
                          initialSelection: _selectedYearRange,
                          label: Text(
                            "Academic year",
                          ),
                          dropdownMenuEntries: List.generate(
                            3,
                            (index) {
                              return DropdownMenuEntry(
                                  value: _listYearRange[index],
                                  label: _listYearRange[index]);
                            },
                          ),
                          onSelected: (value) {
                            setState(() {
                              _selectedYearRange = value!;
                            });
                            _getData();
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  "key_group".tr(),
                  style: AppStyles.textSize16(),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child: Wrap(
                    spacing: 10,
                    runSpacing: 10,
                    children: List.generate(_groups.length, (index) {
                      var value = _groups[index];
                      bool isSelected = _groupSelected.contains(value);
                      return GestureDetector(
                        onTap: () {
                          _groupSelected.clear();
                          if (isSelected) {
                            _groupSelected.remove(value);
                          } else {
                            _groupSelected.add(value);
                          }
                          setState(() {});
                          _filterStudent();
                        },
                        child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 10, vertical: 4),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: isSelected
                                  ? AppColors.primary
                                  : AppColors.white.withOpacity(0.1),
                            ),
                            child:
                                Text("$value", style: AppStyles.textSize12())),
                      );
                    }),
                  ),
                ),
                Row(
                  children: [
                    Text(
                      "key_batch".tr(),
                      style: AppStyles.textSize16(),
                    ),
                    // IconButton(
                    //   onPressed: () async {
                    //     var result = await showDialog(
                    //       context: context,
                    //       builder: (context) => CreateNewTypeDialog(
                    //         title: "Create new group",
                    //       ),
                    //     );
                    //     if (result != null) {
                    //       _groups.add(result);
                    //       setState(() {});
                    //     }
                    //   },
                    //   icon: const Icon(Icons.add_circle),
                    // ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child: Wrap(
                    spacing: 10,
                    runSpacing: 10,
                    children: List.generate(filterBatchs.length, (index) {
                      var value = filterBatchs[index];
                      bool isSelected = _batchSelected.contains(value);
                      return GestureDetector(
                        onTap: () {
                          _batchSelected.clear();
                          if (isSelected) {
                            _batchSelected.remove(value);
                          } else {
                            _batchSelected.add(value);
                          }
                          setState(() {});
                          _filterStudent();
                        },
                        child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 10, vertical: 4),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: isSelected
                                  ? AppColors.primary
                                  : AppColors.white.withOpacity(0.1),
                            ),
                            child:
                                Text("$value", style: AppStyles.textSize12())),
                      );
                    }),
                  ),
                ),
                Row(
                  children: [
                    Text(
                      "key_section".tr(),
                      style: AppStyles.textSize16(),
                    ),
                    // IconButton(
                    //   onPressed: () async {
                    //     var result = await showDialog(
                    //       context: context,
                    //       builder: (context) => CreateNewTypeDialog(
                    //         title: "Create new group",
                    //       ),
                    //     );
                    //     if (result != null) {
                    //       _groups.add(result);
                    //       setState(() {});
                    //     }
                    //   },
                    //   icon: const Icon(Icons.add_circle),
                    // ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child: Wrap(
                    spacing: 10,
                    runSpacing: 10,
                    children: List.generate(_sections.length, (index) {
                      var value = _sections[index];
                      bool isSelected = _sectionSelected.contains(value);
                      return GestureDetector(
                        onTap: () {
                          _sectionSelected.clear();
                          if (isSelected) {
                            _sectionSelected.remove(value);
                          } else {
                            _sectionSelected.add(value);
                          }
                          setState(() {});
                          _filterStudent();
                        },
                        child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 10, vertical: 4),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: isSelected
                                  ? AppColors.primary
                                  : AppColors.white.withOpacity(0.1),
                            ),
                            child:
                                Text("$value", style: AppStyles.textSize12())),
                      );
                    }),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "key_student".tr() + ": ${_studentsFiltered.length}",
                      style: AppStyles.textSize16(),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        if (_studentsSelected.length ==
                            _studentsFiltered.length) {
                          _studentsSelected = [];
                        } else {
                          _studentsSelected = _studentsFiltered.toList();
                        }
                        setState(() {});
                      },
                      child: Text(
                        _studentsSelected.length == _studentsFiltered.length
                            ? "Deselect all"
                            : "Select all",
                        style: AppStyles.textSize12(),
                      ),
                    ),
                  ],
                ),
                Column(
                  children: List.generate(_studentsFiltered.length, (index) {
                    var student = _studentsFiltered[index];
                    bool selected = _studentsSelected.contains(student);
                    return StudentPromoteItem(
                      selected: selected,
                      student: student,
                      onChanged: (value) {
                        if (value) {
                          _studentsSelected.add(student);
                        } else {
                          _studentsSelected.remove(student);
                        }
                        setState(() {});
                      },
                    );
                  }),
                )
              ],
            );
          }),
        ),
      ),
    );
  }
}

class StudentPromoteItem extends StatelessWidget {
  final bool selected;
  final UserModel student;
  final Function(bool) onChanged;
  final bool showCheckbox;
  const StudentPromoteItem({
    super.key,
    required this.selected,
    required this.student,
    required this.onChanged,
    this.showCheckbox = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            spacing: 10,
            children: [
              if (showCheckbox)
                Checkbox(
                  value: selected,
                  onChanged: (value) {
                    onChanged(value!);
                  },
                ),
              Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppColors.primary,
                  ),
                ),
                child: Center(
                  child: Icon(
                    Icons.person_outline,
                    size: 20,
                  ),
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 4,
                  children: [
                    Text(
                      "${student.name}",
                    ),
                    Text(
                      "SID: ${student.sid} Year: ${student.prAcYear}",
                      style: AppStyles.textSize12(
                        color: AppColors.grey,
                      ),
                    ),
                    Text(
                      "Updated on: ${student.updatedAt != null ? Helper.formatDateTimeToString(context, DateTime.parse(student.updatedAt!), dateFormat: DateFormat("MMM dd, yyyy hh:mm a")) : ""}",
                      style: AppStyles.textSize12(
                        color: AppColors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Divider(),
        ],
      ),
    );
  }
}

class CreateNewTypeDialog extends StatefulWidget {
  final String title;

  const CreateNewTypeDialog({super.key, required this.title});

  @override
  State<CreateNewTypeDialog> createState() => _CreateNewTypeDialogState();
}

class _CreateNewTypeDialogState extends State<CreateNewTypeDialog> {
  final TextEditingController _controller = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          spacing: 10,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              widget.title,
              style: AppStyles.textSize16(),
            ),
            TextField(
              controller: _controller,
              onChanged: (value) {
                setState(() {});
              },
            ),
            Row(
              spacing: 10,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      child: Text(
                        "Cancel",
                        style: AppStyles.textSize12(),
                      )),
                ),
                Expanded(
                  child: ElevatedButton(
                      onPressed: _controller.text.isEmpty
                          ? null
                          : () {
                              Navigator.pop(context, _controller.text);
                            },
                      child: Text(
                        "Save",
                        style: AppStyles.textSize12(),
                      )),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class UpdateStudentClassDialog extends StatefulWidget {
  final String title;
  final String selectedYearRange;

  const UpdateStudentClassDialog(
      {super.key,
      required this.title,
      required this.studentClasses,
      required this.students,
      required this.groups,
      required this.batches,
      required this.sections,
      this.currentGroup,
      this.currentBatch,
      this.currentSection,
      required this.selectedYearRange});
  final List<StudentClassModel> studentClasses;
  final List<UserModel> students;
  final List<String> groups;
  final List<String> batches;
  final List<String> sections;
  final String? currentGroup;
  final String? currentBatch;
  final String? currentSection;
  @override
  State<UpdateStudentClassDialog> createState() =>
      _UpdateStudentClassDialogState();
}

class _UpdateStudentClassDialogState extends State<UpdateStudentClassDialog> {
  late String? _selectedGroup = widget.currentGroup;
  late String? _selectedBatch = widget.currentBatch;
  late String? _selectedSection = widget.currentSection;

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  String _log = "";
  final int _currentYear = DateTime.now().year;
  late final List<String> _listYearRange = [-1, 0, 1].map((e) {
    var year = _currentYear + e;
    var nexYear = year + 1;
    return "$year-${nexYear.toString().substring(2)}";
  }).toList();
  late String _selectedYearRange = widget.selectedYearRange;

  _update() async {
    try {
      setState(() {
        _isLoading = true;
      });

      int total = widget.students.length;
      int count = 0;

      for (var student in widget.students) {
        var studentClass = StudentClassModel(
          docId: "",
          sid: student.sid ?? "",
          prCourseName: _selectedBatch ?? "",
          prSectionName: _selectedSection ?? "",
          prAcYear: _selectedYearRange,
          prCGroupName: _selectedGroup ?? "",
          studentName: student.name,
          createdAt: DateTime.now().toIso8601String(),
          updatedAt: DateTime.now().toIso8601String(),
        );
        await FirestoreService.createStudentClass(studentClass);
        await FirestoreService.updateUser(docId: student.docId!, data: {
          "updated_at": DateTime.now().toUtc().toIso8601String(),
        });
        count++;
        _log = "Updated $count of $total";
        setState(() {});
      }
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        Navigator.pop(context, true);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: OverlayLoading(
        isLoading: _isLoading,
        customLoading: Container(
          width: getWidth(context) * 0.5,
          height: 100,
          decoration: BoxDecoration(
            color: AppColors.grey.withOpacity(0.7),
            borderRadius: BorderRadius.circular(5),
          ),
          child: Center(
            child: Column(
              spacing: 8,
              mainAxisSize: MainAxisSize.min,
              children: [
                CupertinoActivityIndicator(),
                Text(
                  _log,
                  style: AppStyles.textSize12(),
                )
              ],
            ),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 10,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "Update student class",
                  style: AppStyles.textSize20(),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Row(
                    spacing: 10,
                    children: [
                      Expanded(
                        child: DropdownMenu<String>(
                          initialSelection: _selectedYearRange,
                          label: Text(
                            "Academic year",
                          ),
                          dropdownMenuEntries: List.generate(
                            3,
                            (index) {
                              return DropdownMenuEntry(
                                  value: _listYearRange[index],
                                  label: _listYearRange[index]);
                            },
                          ),
                          onSelected: (value) {
                            setState(() {
                              _selectedYearRange = value!;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                Text("Group: ${widget.currentGroup} -> ${_selectedGroup}"),
                Wrap(
                  spacing: 10,
                  runSpacing: 10,
                  children: [
                    for (var group in widget.groups)
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedGroup = group;
                          });
                        },
                        child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 10, vertical: 4),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: _selectedGroup == group
                                  ? AppColors.primary
                                  : AppColors.white.withOpacity(0.1),
                            ),
                            child: Text(group, style: AppStyles.textSize12())),
                      ),
                  ],
                ),
                Text("Batch: ${widget.currentBatch} -> ${_selectedBatch}"),
                Wrap(
                  spacing: 10,
                  runSpacing: 10,
                  children: [
                    for (var batch in widget.batches)
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedBatch = batch;
                          });
                        },
                        child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 10, vertical: 4),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: _selectedBatch == batch
                                  ? AppColors.primary
                                  : AppColors.white.withOpacity(0.1),
                            ),
                            child: Text(batch, style: AppStyles.textSize12())),
                      ),
                  ],
                ),
                Text(
                    "Section: ${widget.currentSection} -> ${_selectedSection}"),
                Wrap(
                  spacing: 10,
                  runSpacing: 10,
                  children: [
                    for (var section in widget.sections)
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedSection = section;
                          });
                        },
                        child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 10, vertical: 4),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: _selectedSection == section
                                  ? AppColors.primary
                                  : AppColors.white.withOpacity(0.1),
                            ),
                            child:
                                Text(section, style: AppStyles.textSize12())),
                      ),
                  ],
                ),
                Text(
                  "Effected students: ${widget.students.length}",
                  style: AppStyles.textSize12(),
                ),
                Row(
                  spacing: 10,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: Text(
                          "Cancel",
                          style: AppStyles.textSize12(),
                        ),
                      ),
                    ),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          _update();
                        },
                        child: Text(
                          "Promote",
                          style: AppStyles.textSize12(
                            color: AppColors.primary,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ConfirmMergeDataDialog extends StatefulWidget {
  const ConfirmMergeDataDialog({super.key});

  @override
  State<ConfirmMergeDataDialog> createState() => _ConfirmMergeDataDialogState();
}

class _ConfirmMergeDataDialogState extends State<ConfirmMergeDataDialog> {
  String _randomText = "";
  final TextEditingController _textController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  @override
  void initState() {
    _randomText = (1000 + Random().nextInt(9000)).toString();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        child: Form(
          key: _formKey,
          child: Column(
            spacing: 10,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "Confirm merge data",
                style: AppStyles.textSize20(),
              ),
              Text(
                "Are you sure you want to merge data again? This will delete all existing data and create new data",
                style: AppStyles.textSize12(),
              ),
              Text(_randomText, style: AppStyles.textSize16(color: Colors.red)),
              TextFormField(
                controller: _textController,
                decoration: InputDecoration(
                  hintText: "Enter text",
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return "Please enter text";
                  }
                  if (value != _randomText) {
                    return "Please enter the correct text";
                  }
                  return null;
                },
              ),
              Row(
                spacing: 10,
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context, false);
                      },
                      child: Text(
                        "Cancel",
                        style: AppStyles.textSize16(),
                      ),
                    ),
                  ),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        if (_formKey.currentState!.validate()) {
                          Navigator.pop(context, true);
                        }
                      },
                      child: Text(
                        "Merge",
                        style: AppStyles.textSize16(
                          color: Colors.red,
                        ),
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
