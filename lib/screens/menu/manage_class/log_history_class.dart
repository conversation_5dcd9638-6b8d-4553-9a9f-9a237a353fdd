import 'package:chat_app/export.dart';
import 'package:chat_app/models/education/student_class_model.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/app_styles.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:chat_app/utils/helper.dart';
import 'package:chat_app/widgets/custom_appbar.dart';
import 'package:chat_app/widgets/overlay_loading.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

class LogHistoryClass extends StatefulWidget {
  const LogHistoryClass({super.key});

  @override
  State<LogHistoryClass> createState() => _LogHistoryClassState();
}

class _LogHistoryClassState extends State<LogHistoryClass> {
  List<StudentClassModel> _studentClasses = [];
  Map<String, List<StudentClassModel>> _studentClassesMap = {};
  bool _isLoading = false;
  @override
  void initState() {
    super.initState();
    _getData();
  }

  _getData() async {
    try {
      setState(() {
        _isLoading = true;
      });
      _studentClasses = await FirestoreService.getAllStudentClass();
      _studentClasses.sort((a, b) => a.updatedAt!.compareTo(b.updatedAt!));
      _studentClassesMap = groupBy(_studentClasses, (p0) => p0.sid);
      _studentClassesMap.removeWhere((key, value) => value.length <= 1);
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    var keys = _studentClassesMap.keys.toList();
    return Scaffold(
      appBar: CustomAppbar(title: "Log History Class"),
      body: OverlayLoading(
        isLoading: _isLoading,
        child: ListView.separated(
          itemCount: keys.length,
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          separatorBuilder: (_, index) {
            return Divider();
          },
          itemBuilder: (_, index) {
            var key = keys[index];
            var sid = key;
            var studentName = "";
            var studentClass = _studentClassesMap[key];
            var list = studentClass?.toList() ?? [];
            list.sort(
                (a, b) => (a.updatedAt ?? "").compareTo(b.updatedAt ?? ""));
            try {
              studentName = _studentClassesMap[key]
                      ?.firstWhere((e) => e.studentName != null)
                      .studentName ??
                  "";
            } catch (e) {
              studentName = "N/A";
            }
            var updatedAt = list.last.updatedAt;
            return Row(
              spacing: 10,
              children: [
                Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: AppColors.primary,
                    ),
                  ),
                  child: Center(
                    child: Icon(
                      Icons.person_outline,
                      size: 20,
                    ),
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Student: $studentName - SID: $sid",
                        style: AppStyles.textSize16(),
                      ),
                      Text(
                        "Academic year: ${list.map((e) => e.prAcYear).join(" ->   ")}",
                        style: AppStyles.textSize12(),
                      ),
                      Text(
                        "Group: ${list.map((e) => e.prCGroupName).join(" -> ")}",
                        style: AppStyles.textSize12(),
                      ),
                      Text(
                        "Batch: ${list.map((e) => e.prCourseName).join(" -> ")}",
                        style: AppStyles.textSize12(),
                      ),
                      Text(
                        "Section: ${list.map((e) => e.prSectionName).join(" -> ")}",
                        style: AppStyles.textSize12(),
                      ),
                      Text(
                        "Updated on: ${updatedAt != null ? Helper.formatDateTimeToString(context, DateTime.parse(updatedAt), dateFormat: DateFormat("MMM dd, yyyy hh:mm a")) : ""}",
                        style: AppStyles.textSize10(
                          color: AppColors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
