import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/blocs/menu_bloc/menu_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/screens/menu/app_messages/app_messages.dart';
import 'package:chat_app/screens/menu/manage_class/manage_class.dart';
import 'package:chat_app/screens/menu/webview_app/webview_app.dart';
import 'package:chat_app/screens/menu/manage_users/manage_users.dart';
import 'package:chat_app/screens/settings/settings.dart';
import 'package:chat_app/screens/update_database/update_database.dart';

import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/assets.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:chat_app/utils/menu_constants.dart';
import 'package:chat_app/utils/platform_helper.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class Menu extends StatefulWidget {
  const Menu({super.key});

  @override
  State<Menu> createState() => _MenuState();
}

class _MenuState extends State<Menu> {
  late MenuBloc _menuBloc;
  bool _hasLoadedPermissions = false;

  @override
  void initState() {
    super.initState();
    _menuBloc = MenuBloc();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _loadMenuPermissionsIfNeeded();
  }

  void _loadMenuPermissionsIfNeeded() {
    if (_hasLoadedPermissions) return;

    final authBloc = BlocProvider.of<AuthBloc>(context);
    final authState = authBloc.state;

    if (authState.userModel != null &&
        authState.schoolName != null &&
        authState.userModel!.sid != null) {
      _menuBloc.add(LoadMenuPermissions(
        schoolCode: authState.schoolName!,
        sid: authState.userModel!.sid!,
      ));
      _hasLoadedPermissions = true;
    }
  }

  @override
  void dispose() {
    _menuBloc.close();
    super.dispose();
  }

  // Helper method to check if a menu should be visible
  bool _isMenuVisible(String menuId, MenuState menuState) {
    var phoneNumber =
        BlocProvider.of<AuthBloc>(context).state.userModel?.phoneNumber;
    if (phoneNumber?.toString() == AppConstants.testPhoneNumber) {
      return true;
    }
    // If permissions are not loaded yet, show default menus
    // if (menuState.permissions.isEmpty && !menuState.isLoading) {
    //   return MenuConstants.defaultMenus.contains(menuId);
    // }

    // // If permissions are loaded, check if menu is enabled
    // return _menuBloc.isMenuEnabled(menuId);
    var index = menuState.permissions.indexWhere((e) => e.menuId == menuId);
    if (index != -1) {
      return menuState.permissions[index].isEnabled;
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(builder: (context, authState) {
      return BlocBuilder<MenuBloc, MenuState>(
        bloc: _menuBloc,
        builder: (context, menuState) {
          return Scaffold(
            backgroundColor: AppColors.backgroundScaffold,
            // appBar: CustomAppbar(
            //   title: "menu".tr(),
            //   isShowLeadingIcon: false,
            // ),

            body: Container(
              margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
              decoration: BoxDecoration(
                color: AppColors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(5),
              ),
              child: menuState.isLoading
                  ? const Center(
                      child: Padding(
                        padding: EdgeInsets.all(20.0),
                        child: CircularProgressIndicator(),
                      ),
                    )
                  : Column(mainAxisSize: MainAxisSize.min, children: [
                      if (_isMenuVisible(MenuConstants.appMessages, menuState))
                        SettingItem(
                            suffixIcon: const Icon(Icons.keyboard_arrow_right),
                            onTap: () {
                              push(context, const AppMessages());
                            },
                            title: "app_messages".tr(),
                            iconPath: Assets.assetsImagesIconsAppMessages),
                      if (_isMenuVisible(MenuConstants.appMessages, menuState))
                        Divider(
                          height: 1,
                          color: AppColors.white,
                        ),
                      if (_isMenuVisible(
                          MenuConstants.whatsappMessages, menuState))
                        SettingItem(
                            suffixIcon: const Icon(Icons.keyboard_arrow_right),
                            onTap: () {},
                            title: "whatsapp_messages".tr(),
                            iconPath: Assets.assetsImagesIconsWhatsappIcon),
                      if (_isMenuVisible(
                          MenuConstants.whatsappMessages, menuState))
                        Divider(
                          height: 1,
                          color: AppColors.white,
                        ),
                      if (_isMenuVisible(MenuConstants.textMessages, menuState))
                        SettingItem(
                            suffixIcon: const Icon(Icons.keyboard_arrow_right),
                            onTap: () {},
                            title: "text_messages".tr(),
                            iconPath: Assets.assetsImagesIconsTextMessages),
                      if (_isMenuVisible(MenuConstants.textMessages, menuState))
                        Divider(
                          height: 1,
                          color: AppColors.white,
                        ),
                      if (_isMenuVisible(MenuConstants.manageExams, menuState))
                        SettingItem(
                            suffixIcon: const Icon(Icons.keyboard_arrow_right),
                            onTap: () async {
                              var sid = authState.userModel!.sid;
                              var url =
                                  'https://${FirestoreService.projectName.toLowerCase()}.feepayindia.in/Exams/manage_exam.php?empid=$sid';

                              if (kIsWeb) {
                                // On web, open URL in new tab
                                await PlatformHelper.openFile(url);
                              } else {
                                // On mobile, use WebView
                                push(
                                    context,
                                    WebViewApp(
                                      hideAppBarWhenMainUrlChange: true,
                                      url: url,
                                      title: "manage_exams".tr(),
                                    ));
                              }
                            },
                            title: "manage_exams".tr(),
                            iconPath: Assets.assetsImagesIconsManageExams),
                      if (_isMenuVisible(MenuConstants.manageExams, menuState))
                        Divider(
                          height: 1,
                          color: AppColors.white,
                        ),
                      // SettingItem(
                      //     suffixIcon: const Icon(Icons.keyboard_arrow_right),
                      //     onTap: () async {
                      //       var paymentLink =
                      //           "https://${FirestoreService.projectName.toLowerCase()}.feepayindia.in/Student/hostelInPass.php";
                      //       print(paymentLink);
                      //       push(
                      //           context,
                      //           WebViewApp(
                      //             title: "menu_inpass".tr(),
                      //             url: paymentLink,
                      //           ));
                      //     },
                      //     title: "menu_inpass".tr(),
                      //     iconPath: Assets.assetsImagesIconsMenu2Icon),
                      // Divider(
                      //   height: 1,
                      //   color: AppColors.white,
                      // ),
                      if (_isMenuVisible(
                          MenuConstants.promoteStudent, menuState))
                        SettingItem(
                            suffixIcon: const Icon(Icons.keyboard_arrow_right),
                            onTap: () {
                              push(context, const ManageClass());
                            },
                            title: "Promote Student",
                            iconPath: Assets.assetsImagesIconsClassIcon,
                            iconWidth: 22,
                            iconHeight: 22),
                      if (_isMenuVisible(
                          MenuConstants.promoteStudent, menuState))
                        Divider(
                          height: 1,
                          color: AppColors.white,
                        ),
                      if (_isMenuVisible(MenuConstants.manageUsers, menuState))
                        SettingItem(
                            suffixIcon: const Icon(Icons.keyboard_arrow_right),
                            onTap: () {
                              push(context, const ManageUsers());
                            },
                            title: "manager_users".tr(),
                            iconPath: Assets.assetsImagesIconsManageUsers),
                      if (_isMenuVisible(MenuConstants.manageUsers, menuState))
                        Divider(
                          height: 1,
                          color: AppColors.white,
                        ),
                      if (_isMenuVisible(
                          MenuConstants.studentAttendance, menuState))
                        SettingItem(
                            suffixIcon: const Icon(Icons.keyboard_arrow_right),
                            onTap: () async {
                              var employId = authState.userModel!.sid;
                              var url =
                                  'https://${FirestoreService.projectName.toLowerCase()}.feepayindia.in/Student/studentAttendance.php?empId=$employId';

                              if (kIsWeb) {
                                // On web, open URL in new tab
                                await PlatformHelper.openFile(url);
                              } else {
                                // On mobile, use WebView
                                push(
                                    context,
                                    WebViewApp(
                                      mainUrl: "studentAttendance.php",
                                      hideAppBarWhenMainUrlChange: true,
                                      url: url,
                                      title: "Student Attendance",
                                    ));
                              }
                            },
                            title: "Student Attendance",
                            iconPath: Assets.assetsImagesIconsAttendanceIcon),

                      if (_isMenuVisible(
                          MenuConstants.updateDatabase, menuState))
                        Column(children: [
                          Divider(
                            height: 1,
                            color: AppColors.white,
                          ),
                          SettingItem(
                              onTap: () {
                                push(context, const UpdateDatabase());
                              },
                              title: "Update Database",
                              iconPath: Assets.assetsImagesIconsClassIcon),
                        ]),
                    ]),
            ),
          );
        },
      );
    });
  }
}
