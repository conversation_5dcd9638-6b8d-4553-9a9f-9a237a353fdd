import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/models/chats/conversatoin_model.dart';
import 'package:chat_app/screens/chat/absentees_messages/absentees_mesages.dart';
import 'package:chat_app/screens/chat/chat_details/chat_details.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/change_room_dialog.dart';
import 'package:chat_app/screens/chat/chat_page.dart';
import 'package:chat_app/screens/chat/create_user/create_user.dart';
import 'package:chat_app/screens/chat/homework_and_notes/homework_and_notes.dart';
import 'package:chat_app/screens/chat/search/search_parent.dart';
import 'package:chat_app/screens/chat/send_birthday/send_birthday.dart';
import 'package:chat_app/screens/chat/update_user/update_user.dart';
import 'package:chat_app/screens/menu/menu.dart';
import 'package:chat_app/utils/homework_notes_helper.dart';

import 'package:chat_app/screens/settings/settings.dart';

import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/assets.dart';
import 'package:chat_app/utils/firestore_service.dart';

import 'package:chat_app/utils/notification_service.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:upgrader/upgrader.dart';

import 'package:url_launcher/url_launcher_string.dart';
// ignore: depend_on_referenced_packages
import 'package:async/async.dart';

class MainTab extends StatefulWidget {
  const MainTab({super.key});

  @override
  State<MainTab> createState() => _MainTabState();
}

class _MainTabState extends State<MainTab> {
  late final List<Widget> _pages = [
    const ChatPage(),
    HomeworkAndNotes(key: homeworkAndNotesGlobalKey),
    // const Results(),
    // const FeePay(),
    const Menu(),
  ];
  final NotificationService notificationService = NotificationService();
  int _index = 0;
  int _badge = 0;
  StreamGroup<List<ActivityModel>> _streamGroup =
      StreamGroup<List<ActivityModel>>();
  @override
  void initState() {
    _init();
    super.initState();
  }

  _init() async {
    var schoolCode = await LocalStorage().getSchoolCode();

    if (mounted) {
      BlocProvider.of<AuthBloc>(context)
          .add(GetSchoolDetails(schoolCode: schoolCode));
      try {
        await notificationService.settingNotifcation(context);
      } catch (e) {
        debugPrint(e.toString());
      }
    }
    _streamNotification();
    // _checkForAppUpdates();
  }

  _streamNotification() {
    var user = BlocProvider.of<AuthBloc>(context).state.userModel;
    if (user?.type == AppConstants.parentType) {
      var students = BlocProvider.of<AuthBloc>(context).state.students;

      for (var element in students!) {
        // var count =
        //     await FirestoreService.getUnReadActivityCount(userId: element.sid!);
        // total += count;

        var stream =
            FirestoreService.getActivityStreamStudent(receiverId: element.sid!);
        _streamGroup.add(stream);
      }
      _streamGroup.stream.listen((event) {
        var total =
            event.where((element) => element.readAt == null).toList().length;
        setState(() {
          _badge = total;
        });
      });
    }
  }

  @override
  void dispose() {
    notificationService.dispose();
    _streamGroup.close();
    super.dispose();
  }

  Future _onMenuTap(int value) async {
    switch (value) {
      case 0:
        final result = await push(
            context,
            SearchParent(
              showOrderBy: false,
              users: [],
              showCall: false,
            ));

        if (result != null) {
          var data = await push(context, ChangeRoomName(name: ""));
          if (data != null) {
            push(
                context,
                ChatDetails(
                  roomName: data['name'] ?? "",
                  image: data['image'] ?? "",
                  initUsers: result,
                ));
          }
        }
        break;
      case 1:
        var result = await push(
            context,
            SearchParent(
              users: [],
              title: "key_search".tr(),
              isSelectMulti: false,
            ));
        if (result != null) {
          push(
              context,
              ChatDetails(
                isNewMessage: true,
                initUsers: result,
              ));
        }
        break;
      case 2:
        push(context, SendBirthday());

        break;
      case 3:
        push(
            context,
            AbsenteesMessages(
              students: [],
            ));

        break;
      case 5:
        push(context, CreateUser());

        break;
      case 6:
        final result = await push(
            context,
            SearchParent(
              users: [],
              title: "update_user".tr(),
              isSelectMulti: false,
              showCall: false,
            ));
        if (result != null) {
          push(
              context,
              UpdateUser(
                userModel: result[0],
              ));
        }
        break;
      case 7:
        push(context, Settings());
        break;
      default:
        showToast("Update soon");
    }
  }

  PreferredSizeWidget _buildHeaderAppBar(
      AuthState state, String? title, String? logo) {
    return DynamicHeaderAppBar(
      title: title,
      logo: logo,
      badge: _badge,
      user: state.userModel!,
      onSelected: _onMenuTap,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(builder: (context, state) {
      String? title = state.schoolFullName;
      String? logo = state.logo;

      return UpgradeAlert(
        child: Scaffold(
          floatingActionButton: FloatingActionButton(
            onPressed: () async {
              var result = await push(
                  context,
                  SearchParent(
                    users: [],
                    title: "key_search".tr(),
                    isSelectMulti: false,
                  ));
              if (result != null) {
                push(
                    context,
                    ChatDetails(
                      isNewMessage: true,
                      initUsers: result,
                    ));
              }
            },
            child: const Icon(Icons.search),
          ),
          appBar: _buildHeaderAppBar(state, title, logo),
          backgroundColor: AppColors.black,
          body: IndexedStack(
            index: _index,
            children: _pages,
          ),
          bottomNavigationBar: BottomNavigationBar(
            onTap: (value) {
              if (value == 3) {
                var paymentLink =
                    "https://${FirestoreService.projectName.toLowerCase()}.feepayindia.in/opay/index.php?mobileNo=${state.userModel?.contactOne}";
                launchUrlString(paymentLink, mode: LaunchMode.inAppBrowserView);
              } else if (value == 4) {
                if (state.userModel?.type == AppConstants.parentType) {
                  showToast("available_soon".tr());
                } else {
                  setState(() {
                    _index = value;
                  });
                }
              } else {
                // Refresh HomeworkAndNotes when tab is clicked
                if (value == 1) {
                  HomeworkNotesHelper.safeRefreshHomeworkAndNotes();
                }

                setState(() {
                  _index = value;
                });
              }
            },
            selectedItemColor: AppColors.white,
            // selectedItemColor: AppColors.primary,
            selectedLabelStyle: AppStyles.textSize13(),
            unselectedLabelStyle: AppStyles.textSize13(),
            // selectedIconTheme: IconThemeData(color: Colors.red),
            unselectedItemColor: AppColors.white.withValues(alpha: 0.6),
            currentIndex: _index,
            backgroundColor: AppColors.isDark
                ? Color(0xff455A64)
                : Color.fromARGB(255, 255, 255, 255),
            // backgroundColor: AppColors.white,
            // unselectedItemColor: Colors.grey,
            // fixedColor: ,
            type: BottomNavigationBarType.fixed,
            showUnselectedLabels: true,
            showSelectedLabels: true,
            items: [
              BottomNavigationBarItem(
                backgroundColor: AppColors.backgroundScaffold,
                icon: SvgPicture.asset(
                  AppAssets.chatIcon,
                  colorFilter: ColorFilter.mode(
                    _index == 0
                        ? AppColors.primary
                        : AppColors.primary.withValues(alpha: 0.5),
                    BlendMode.srcIn,
                  ),
                ),
                label: "key_messages".tr(),
              ),
              BottomNavigationBarItem(
                  backgroundColor: AppColors.backgroundScaffold,
                  icon: SvgPicture.asset(
                    AppAssets.homeworkIcon,
                    colorFilter: ColorFilter.mode(
                      _index == 1
                          ? AppColors.primary
                          : AppColors.primary.withValues(alpha: 0.5),
                      BlendMode.srcIn,
                    ),
                  ),
                  label: "Homework & Notes"),
              BottomNavigationBarItem(
                  backgroundColor: AppColors.backgroundScaffold,
                  icon: SvgPicture.asset(
                    Assets.assetsImagesIconsMenuIcon,
                    colorFilter: ColorFilter.mode(
                      _index == 4
                          ? AppColors.primary
                          : AppColors.primary.withValues(alpha: 0.5),
                      BlendMode.srcIn,
                    ),
                  ),
                  label: "menu".tr()),
            ],
          ),
        ),
      );
    });
  }
}

class DynamicHeaderAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  final int badge;
  final UserModel user;
  final Function(int)? onSelected;
  final String? title;
  final String? logo;

  const DynamicHeaderAppBar({
    super.key,
    required this.badge,
    required this.user,
    this.onSelected,
    this.title,
    this.logo,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<ConversationModel>>(
      stream: FirestoreService.getConversationStream(
        userId: user.docId ?? "",
        isTeacher: user.type == AppConstants.teacherType,
      ),
      builder: (context, snapshot) {
        String? latestMessage;
        String? latestMessageType;
        bool isHomework = false;
        bool isNotes = false;

        if (snapshot.hasData && snapshot.data!.isNotEmpty) {
          var conversations = snapshot.data!;
          if (conversations.isNotEmpty) {
            var latestConversation = conversations.first;
            latestMessage = latestConversation.lastedMessage;
            latestMessageType = _detectMessageType(latestMessage);
          }
        }

        return HeaderAppBar(
          title: title,
          logo: logo,
          badge: badge,
          user: user,
          onSelected: onSelected,
          latestMessage: latestMessage,
          latestMessageType: latestMessageType,
          isHomework: isHomework,
          isNotes: isNotes,
        );
      },
    );
  }

  String _detectMessageType(String? message) {
    if (message != null && message.isNotEmpty) {
      if (message.startsWith('http') || message.startsWith('https')) {
        if (message.contains('.jpg') ||
            message.contains('.png') ||
            message.contains('.jpeg')) {
          return 'image';
        } else if (message.contains('.mp4') || message.contains('.mov')) {
          return 'video';
        } else if (message.contains('.pdf') || message.contains('.doc')) {
          return 'document';
        } else if (message.contains('.mp3') || message.contains('.wav')) {
          return 'audio';
        } else {
          return 'file';
        }
      } else {
        return 'text';
      }
    }
    return 'text';
  }

  @override
  Size get preferredSize => const Size.fromHeight(65);
}
