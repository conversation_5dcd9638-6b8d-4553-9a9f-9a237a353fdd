import 'package:chat_app/services/app_version_service.dart';
import 'package:chat_app/utils/app_version_admin.dart';
import 'package:chat_app/widgets/custom_appbar.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Test screen for app version functionality
/// This is for development/testing purposes only
class AppVersionTestScreen extends StatefulWidget {
  const AppVersionTestScreen({super.key});

  @override
  State<AppVersionTestScreen> createState() => _AppVersionTestScreenState();
}

class _AppVersionTestScreenState extends State<AppVersionTestScreen> {
  String _currentVersion = '';
  String _currentBuildNumber = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCurrentVersion();
  }

  Future<void> _loadCurrentVersion() async {
    final version = await AppVersionService.getCurrentVersion();
    final buildNumber = await AppVersionService.getCurrentBuildNumber();
    setState(() {
      _currentVersion = version;
      _currentBuildNumber = buildNumber;
    });
  }

  Future<void> _setupVersions() async {
    setState(() => _isLoading = true);
    try {
      await AppVersionAdmin.setupCurrentVersions();
      _showSnackBar('App versions setup complete!', Colors.green);
    } catch (e) {
      _showSnackBar('Error: $e', Colors.red);
    }
    setState(() => _isLoading = false);
  }

  Future<void> _checkForUpdates() async {
    setState(() => _isLoading = true);
    try {
      final updateInfo = await AppVersionService.checkForUpdate();
      if (updateInfo != null) {
        await AppVersionService.showUpdateDialog(context, updateInfo);
      } else {
        _showSnackBar('No updates available', Colors.blue);
      }
    } catch (e) {
      _showSnackBar('Error checking updates: $e', Colors.red);
    }
    setState(() => _isLoading = false);
  }

  Future<void> _simulateUpdate() async {
    setState(() => _isLoading = true);
    try {
      // Create a fake newer version for testing
      await AppVersionAdmin.setAndroidVersion(
        version: '1.0.21',
        buildNumber: '171',
        message: 'Test update with new features!',
        forceUpdate: false,
      );
      _showSnackBar('Test update version set! Now check for updates.', Colors.green);
    } catch (e) {
      _showSnackBar('Error: $e', Colors.red);
    }
    setState(() => _isLoading = false);
  }

  Future<void> _simulateForceUpdate() async {
    setState(() => _isLoading = true);
    try {
      // Create a fake force update for testing
      await AppVersionAdmin.setAndroidVersion(
        version: '1.0.22',
        buildNumber: '172',
        message: 'Critical security update required!',
        forceUpdate: true,
      );
      _showSnackBar('Force update set! Now check for updates.', Colors.orange);
    } catch (e) {
      _showSnackBar('Error: $e', Colors.red);
    }
    setState(() => _isLoading = false);
  }

  Future<void> _resetToCurrentVersion() async {
    setState(() => _isLoading = true);
    try {
      await AppVersionAdmin.setAndroidVersion(
        version: _currentVersion,
        buildNumber: _currentBuildNumber,
        message: 'You are up to date!',
        forceUpdate: false,
      );
      _showSnackBar('Reset to current version', Colors.blue);
    } catch (e) {
      _showSnackBar('Error: $e', Colors.red);
    }
    setState(() => _isLoading = false);
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppbar(title: 'App Version Test'),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Current Version Info
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current App Version',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text('Version: $_currentVersion'),
                    Text('Build: $_currentBuildNumber'),
                    Text('Platform: ${kIsWeb ? 'Web' : Theme.of(context).platform.name}'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Test Buttons
            Text(
              'Test Functions',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _setupVersions,
              child: const Text('1. Setup Initial Versions'),
            ),
            
            const SizedBox(height: 8),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _checkForUpdates,
              child: const Text('2. Check for Updates'),
            ),
            
            const SizedBox(height: 8),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _simulateUpdate,
              style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
              child: const Text('3. Simulate Optional Update'),
            ),
            
            const SizedBox(height: 8),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _simulateForceUpdate,
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('4. Simulate Force Update'),
            ),
            
            const SizedBox(height: 8),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _resetToCurrentVersion,
              style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
              child: const Text('5. Reset to Current Version'),
            ),
            
            const SizedBox(height: 20),
            
            // Instructions
            Card(
              color: Colors.blue.withOpacity(0.1),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Instructions:',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    const Text('1. First run "Setup Initial Versions"'),
                    const Text('2. Then "Check for Updates" (should show no updates)'),
                    const Text('3. Run "Simulate Optional Update" then check again'),
                    const Text('4. Run "Simulate Force Update" to test mandatory updates'),
                    const Text('5. Use "Reset" to return to current version'),
                  ],
                ),
              ),
            ),
            
            if (_isLoading)
              const Padding(
                padding: EdgeInsets.all(16),
                child: Center(child: CircularProgressIndicator()),
              ),
          ],
        ),
      ),
    );
  }
}
