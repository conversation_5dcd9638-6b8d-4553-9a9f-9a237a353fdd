import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../utils/app_asset.dart';
import '../../utils/app_colors.dart';
import '../../utils/app_styles.dart';

class Notifications extends StatefulWidget {
  const Notifications({super.key});

  @override
  State<Notifications> createState() => _NotificationsState();
}

class _NotificationsState extends State<Notifications> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundScaffold,
      body: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      "key_notifications".tr(),
                      style: AppStyles.textSize24(),
                    ),
                  ),
                  IconButton(
                      onPressed: () {},
                      icon: SvgPicture.asset(
                        AppAssets.markReadAllIcon,
                        color: AppColors.primary,
                      )),
                ],
              ),
            ),
            Expanded(
              child: ListView.separated(
                  itemBuilder: (_, index) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 14, vertical: 10),
                      child: Row(
                        children: [
                          Container(
                              margin: EdgeInsets.only(right: 10),
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: AppColors.white.withOpacity(0.1),
                              ),
                              child: Center(
                                child: SvgPicture.asset(
                                  AppAssets.bellIcon,
                                  width: 24,
                                  color: AppColors.primary,
                                ),
                              )),
                          Expanded(
                              child: Text(
                            "John just sent a new message to Group A",
                            style: AppStyles.textSize14(),
                          ))
                        ],
                      ),
                    );
                  },
                  separatorBuilder: (_, index) {
                    return Divider(
                      height: 1,
                      color: AppColors.white,
                    );
                  },
                  itemCount: 20),
            )
          ],
        ),
      ),
    );
  }
}
