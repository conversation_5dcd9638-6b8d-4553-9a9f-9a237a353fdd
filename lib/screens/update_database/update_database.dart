import 'dart:math';

import 'package:chat_app/export.dart';
import 'package:chat_app/screens/auth/splash_screen.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/app_styles.dart';
import 'package:chat_app/utils/helper.dart';
import 'package:chat_app/utils/local_storage.dart';
import 'package:chat_app/utils/platform_helper.dart';
import 'package:chat_app/utils/web_download_helper.dart'
    if (dart.library.io) 'package:chat_app/utils/web_download_helper_stub.dart';
import 'package:chat_app/widgets/button_custom.dart';
import 'package:chat_app/widgets/custom_appbar.dart';
import 'package:chat_app/widgets/overlay_loading.dart';
import 'package:excel/excel.dart' as excel;
import 'package:file_picker/file_picker.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:io';

class UpdateDatabase extends StatefulWidget {
  const UpdateDatabase({super.key});

  @override
  State<UpdateDatabase> createState() => _UpdateDatabaseState();
}

class _UpdateDatabaseState extends State<UpdateDatabase> {
  bool _isLoading = false;
  String _selectedSchoolCode = '';
  List<String> _availableSchoolCodes = [];
  Map<String, String> _schoolCodeToName = {};
  PlatformFile? _selectedFile;
  String _uploadStatus = '';
  List<Map<String, dynamic>> _importedData = [];

  @override
  void initState() {
    super.initState();
    _loadSchoolCodes();
  }

  Future<void> _loadSchoolCodes() async {
    try {
      setState(() {
        _isLoading = true;
      });

      await FirestoreService.getFirebaseSettings();

      // Get all Firebase apps (school codes)
      var apps = Firebase.apps;
      List<String> schoolCodes = [];
      Map<String, String> codeToName = {};

      for (var app in apps) {
        if (app.name != '[DEFAULT]') {
          schoolCodes.add(app.name);
          // Try to get school name from local storage
          var localStorage = await LocalStorage().getInstance();
          var schoolName = localStorage.getString(app.name);
          codeToName[app.name] = schoolName ?? app.name;
        }
      }

      setState(() {
        _availableSchoolCodes = schoolCodes;
        _schoolCodeToName = codeToName;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  Future<void> _pickFile() async {
    try {
      var result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls', 'csv'],
      );

      if (result != null) {
        setState(() {
          _selectedFile = result.files.first;
          _uploadStatus = 'File selected: ${_selectedFile!.name}';
        });
        await _processFile();
      }
    } catch (e) {
      showToast('Error picking file: ${e.toString()}');
    }
  }

  Future<void> _processFile() async {
    if (_selectedFile == null) return;

    try {
      setState(() {
        _isLoading = true;
        _uploadStatus = 'Processing file...';
      });

      // Web-compatible file processing
      excel.Excel excelDecode;

      if (kIsWeb) {
        // On web, use bytes directly from the file picker
        if (_selectedFile!.bytes != null) {
          excelDecode = excel.Excel.decodeBytes(_selectedFile!.bytes!);
        } else {
          throw Exception('File bytes not available on web');
        }
      } else {
        // On mobile/desktop, read from file path
        excelDecode =
            excel.Excel.decodeBytes(await _selectedFile!.xFile.readAsBytes());
      }

      var sheet = excelDecode.sheets.values.first;

      List<Map<String, dynamic>> data = [];

      if (sheet.maxRows > 1) {
        for (var i = 1; i < sheet.maxRows; i++) {
          var row = sheet.row(i);
          if (row.isNotEmpty && row[0]?.value != null) {
            var studentData = {
              "sid": row[0]?.value.toString() ?? '',
              "name": row[1]?.value.toString() ?? '',
              "whatsappNumber": int.tryParse(row[2]?.value?.toString() ?? ''),
              "contact_one": int.tryParse(row[3]?.value?.toString() ?? ''),
              "contact_two": int.tryParse(row[4]?.value?.toString() ?? ''),
              "pr_cgroup_name": row[5]?.value.toString() ?? '',
              "pr_course_name": row[6]?.value.toString() ?? '',
              "pr_section_name": row[7]?.value.toString() ?? '',
              "pr_ac_year": row[8]?.value.toString() ?? '',
              "activeStatus": int.tryParse(row[9]?.value?.toString() ?? "1"),
              "type": row[10]?.value.toString() ?? "student",
            };
            data.add(studentData);
          }
        }
      }
      data.add({
        "sid": "0",
        "name": "Test teacher",
        "whatsappNumber": 9916773896,
        "contact_one": 9916773896,
        "contact_two": 9916773896,
        "pr_cgroup_name": "",
        "pr_course_name": "",
        "pr_section_name": "",
        "pr_ac_year": "",
        "activeStatus": 1,
        "type": "teacher",
      });

      setState(() {
        _importedData = data;
        _uploadStatus =
            'File processed successfully. ${data.length} records found.';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _uploadStatus = 'Error processing file: ${e.toString()}';
      });
      showToast('Error processing file: ${e.toString()}');
    }
  }

  Future<void> _downloadTemplate() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Create Excel template
      var excelFile = excel.Excel.createExcel();
      var sheet = excelFile['Template'];

      // Add headers
      var headers = [
        'SID',
        'StudentName',
        'whatsapp_number',
        'contact_one',
        'contact_two',
        'pr_cgroup_name',
        'pr_course_name',
        'pr_section_name',
        'pr_ac_year',
        'active_status',
        'type'
      ];

      for (int i = 0; i < headers.length; i++) {
        sheet
            .cell(excel.CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
            .value = excel.TextCellValue(headers[i]);
      }

      // Add sample data
      var sampleData = [
        'STU001',
        'John Doe',
        '1234567890',
        '9876543210',
        '5555555555',
        'Group A',
        'Computer Science',
        'Section 1',
        '2024-25',
        '1',
        'student'
      ];

      for (int i = 0; i < sampleData.length; i++) {
        sheet
            .cell(excel.CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 1))
            .value = excel.TextCellValue(sampleData[i]);
      }

      // Save and download
      var bytes = excelFile.encode();
      if (bytes != null) {
        if (kIsWeb) {
          // Use web download helper for web
          WebDownloadHelper.downloadFile(
            bytes: Uint8List.fromList(bytes),
            fileName: 'student_import_template.xlsx',
            mimeType:
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          );
        } else {
          // For mobile/desktop, save to downloads folder
          var directory = await PlatformHelper.getDownloadsDirectoryCompat();
          if (directory != null) {
            var filePath = '${directory.path}/student_import_template.xlsx';
            var file = File(filePath);
            await file.writeAsBytes(bytes);
            showToast('Template saved to Downloads folder');
          }
        }
      }

      setState(() {
        _isLoading = false;
      });

      showToast('Template downloaded successfully');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast('Error downloading template: ${e.toString()}');
    }
  }

  Future<void> _importData() async {
    if (_importedData.isEmpty || _selectedSchoolCode.isEmpty) return;

    // Show confirmation dialog for import options
    var importOption = await showDialog<String>(
      context: context,
      builder: (context) => ImportOptionsDialog(),
    );

    if (importOption == null) return; // User cancelled

    try {
      setState(() {
        _isLoading = true;
        _uploadStatus = 'Preparing import...';
      });

      await FirestoreService.updateFirebaseProjectName(_selectedSchoolCode);

      // If user chose to delete old data, delete all users first
      if (importOption == 'delete') {
        setState(() {
          _uploadStatus = 'Deleting old user data...';
        });
        await _deleteCollection(FirestoreService.userCollection);
      }

      setState(() {
        _uploadStatus = 'Importing new data...';
      });

      for (int i = 0; i < _importedData.length; i++) {
        var studentData = _importedData[i];
        await FirestoreService.addUser(studentData);

        setState(() {
          _uploadStatus = 'Importing... ${i + 1}/${_importedData.length}';
        });
      }

      setState(() {
        _isLoading = false;
        _uploadStatus = 'Import completed successfully!';
        _importedData.clear();
        _selectedFile = null;
      });

      if (mounted) {
        Helper.showDialogSuccessMessages(
          context: context,
          title: "Success",
          message: importOption == 'delete'
              ? "Old data deleted and new data imported successfully"
              : "New data imported successfully (old data kept)",
          onPressPrimaryButton: () {
            // reload app
            Navigator.popUntil(context, (route) => route.isFirst);
            replace(context, SplashScreen());
          },
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _uploadStatus = 'Import failed: ${e.toString()}';
      });
      showToast('Import failed: ${e.toString()}');
    }
  }

  Future<void> _clearChatData() async {
    var result = await showDialog<bool>(
      context: context,
      builder: (context) => ConfirmActionDialog(
        title: "Clear Chat Data",
        description:
            "This will delete all conversations and chat messages for the selected school. This action cannot be undone.",
        actionType: "CLEAR_CHAT",
      ),
    );

    if (result == true) {
      await _performClearChatData();
    }
  }

  Future<void> _performClearChatData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      await FirestoreService.updateFirebaseProjectName(_selectedSchoolCode);

      // Delete all conversations and their chats
      var conversations = await FirestoreService.instance
          .collection(FirestoreService.conversationCollection)
          .get();

      for (var conversation in conversations.docs) {
        // Delete all chats in this conversation
        var chats = await conversation.reference
            .collection(FirestoreService.chatCollection)
            .get();

        for (var chat in chats.docs) {
          await chat.reference.delete();
        }

        // Delete the conversation
        await conversation.reference.delete();
      }

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        Helper.showDialogSuccessMessages(
          context: context,
          title: "Success",
          message: "Chat data cleared successfully",
          onPressPrimaryButton: () {
            Navigator.pop(context);
          },
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast('Error clearing chat data: ${e.toString()}');
    }
  }

  Future<void> _deleteAllUserData() async {
    var result = await showDialog<bool>(
      context: context,
      builder: (context) => ConfirmActionDialog(
        title: "Delete All User Data",
        description:
            "This will permanently delete ALL data including users, conversations, activities, and student classes for the selected school. This action cannot be undone.",
        actionType: "DELETE_ALL",
      ),
    );

    if (result == true) {
      await _performDeleteAllUserData();
    }
  }

  Future<void> _performDeleteAllUserData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      await FirestoreService.updateFirebaseProjectName(_selectedSchoolCode);

      // Delete all collections
      await _deleteCollection(FirestoreService.userCollection);
      await _deleteCollection(FirestoreService.conversationCollection);
      await _deleteCollection(FirestoreService.activityCollection);
      await _deleteCollection(FirestoreService.studentClassCollection);
      await _deleteCollection(FirestoreService.historyCollection);

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        Helper.showDialogSuccessMessages(
          context: context,
          title: "Success",
          message: "All user data deleted successfully",
          onPressPrimaryButton: () {
            Navigator.pop(context);
          },
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast('Error deleting user data: ${e.toString()}');
    }
  }

  Future<void> _deleteCollection(String collectionName) async {
    var collection = FirestoreService.instance.collection(collectionName);
    var snapshots = await collection.get();

    for (var doc in snapshots.docs) {
      // // If it's conversations, also delete subcollections
      // if (collectionName == FirestoreService.conversationCollection) {
      //   var chats = await doc.reference
      //       .collection(FirestoreService.chatCollection)
      //       .get();
      //   for (var chat in chats.docs) {
      //     await chat.reference.delete();
      //   }
      // }
      await doc.reference.delete();
    }
  }

  Widget _buildFixedHeaderCell(String text, double width) {
    return Container(
      width: width,
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
      child: Text(
        text,
        style: AppStyles.textSize12(
          fontWeight: FontWeight.w600,
          color: AppColors.primary,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildFixedDataCell(String text, double width) {
    return Container(
      width: width,
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
      child: Text(
        text,
        style: AppStyles.textSize11(),
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: "Update Database",
        actions: _selectedSchoolCode.isNotEmpty
            ? [
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert, color: AppColors.primary),
                  onSelected: (String value) {
                    if (value == 'clear_chat') {
                      _clearChatData();
                    } else if (value == 'delete_all') {
                      _deleteAllUserData();
                    }
                  },
                  itemBuilder: (BuildContext context) => [
                    PopupMenuItem<String>(
                      value: 'clear_chat',
                      child: Row(
                        children: [
                          Icon(Icons.chat_bubble_outline,
                              color: Colors.orange, size: 20),
                          const SizedBox(width: 12),
                          Text(
                            'Clear Only Chat Data',
                            style: AppStyles.textSize14(),
                          ),
                        ],
                      ),
                    ),
                    PopupMenuItem<String>(
                      value: 'delete_all',
                      child: Row(
                        children: [
                          Icon(Icons.delete_forever,
                              color: Colors.red, size: 20),
                          const SizedBox(width: 12),
                          Text(
                            'Delete All User Data',
                            style: AppStyles.textSize14(),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ]
            : null,
      ),
      body: OverlayLoading(
        isLoading: _isLoading,
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // School Code Dropdown Section
                Text(
                  "Select School Code",
                  style: AppStyles.textSize16(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 10),
                Container(
                  width: double.infinity,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.primary),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: _selectedSchoolCode.isEmpty
                          ? null
                          : _selectedSchoolCode,
                      hint: Text(
                        "Choose a school code",
                        style: AppStyles.textSize14(color: AppColors.grey),
                      ),
                      isExpanded: true,
                      items: _availableSchoolCodes.map((String code) {
                        return DropdownMenuItem<String>(
                          value: code,
                          child: Text(
                            "$code - ${_schoolCodeToName[code] ?? code}",
                            style: AppStyles.textSize14(),
                          ),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        setState(() {
                          _selectedSchoolCode = newValue ?? '';
                        });
                      },
                    ),
                  ),
                ),

                const SizedBox(height: 30),

                // File Upload Section
                Text(
                  "Import Data from File",
                  style: AppStyles.textSize16(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 10),

                GestureDetector(
                  onTap: _selectedSchoolCode.isEmpty ? null : _pickFile,
                  child: Container(
                    height: 120,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: _selectedSchoolCode.isEmpty
                          ? AppColors.grey.withValues(alpha: 0.3)
                          : Colors.transparent,
                      border: Border.all(
                        color: _selectedSchoolCode.isEmpty
                            ? AppColors.grey
                            : AppColors.primary,
                      ),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.upload_file,
                            size: 40,
                            color: _selectedSchoolCode.isEmpty
                                ? AppColors.grey
                                : AppColors.primary,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _selectedSchoolCode.isEmpty
                                ? "Select a school code first"
                                : kIsWeb
                                    ? "Upload Excel File"
                                    : "Upload CSV or Excel File",
                            style: AppStyles.textSize14(
                              color: _selectedSchoolCode.isEmpty
                                  ? AppColors.grey
                                  : AppColors.primary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                if (_uploadStatus.isNotEmpty) ...[
                  const SizedBox(height: 10),
                  Text(
                    _uploadStatus,
                    style: AppStyles.textSize12(color: AppColors.grey),
                  ),
                ],

                // const SizedBox(height: 15),

                // Download Template Button
                ButtonCustom(
                  onPressed: _downloadTemplate,
                  title: "Download Excel Template",
                  isOutLine: true,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.download, color: AppColors.primary),
                      const SizedBox(width: 8),
                      Text(
                        "Download Excel Template",
                        style: AppStyles.textSize14(color: AppColors.primary),
                      ),
                    ],
                  ),
                ),

                if (_importedData.isNotEmpty) ...[
                  const SizedBox(height: 20),

                  // Data Preview Section
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Data Preview",
                        style:
                            AppStyles.textSize16(fontWeight: FontWeight.w600),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(color: AppColors.primary),
                        ),
                        child: Text(
                          "${_importedData.length} records",
                          style: AppStyles.textSize12(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),

                  // Info text
                  Text(
                    "Please review the data below before importing. You can scroll horizontally to see all columns.",
                    style: AppStyles.textSize12(color: AppColors.grey),
                  ),
                  const SizedBox(height: 10),

                  Container(
                    height: 300,
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.grey),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        // Header row
                        Container(
                          height: 50,
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withValues(alpha: 0.1),
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(8),
                              topRight: Radius.circular(8),
                            ),
                          ),
                          child: Row(
                            children: [
                              _buildFixedHeaderCell("SID", 80),
                              _buildFixedHeaderCell("Name", 120),
                              _buildFixedHeaderCell("WhatsApp", 100),
                              _buildFixedHeaderCell("Contact 1", 100),
                              _buildFixedHeaderCell("Contact 2", 100),
                              _buildFixedHeaderCell("Group", 80),
                              _buildFixedHeaderCell("Course", 100),
                              _buildFixedHeaderCell("Section", 80),
                              _buildFixedHeaderCell("Year", 70),
                              _buildFixedHeaderCell("Status", 60),
                              _buildFixedHeaderCell("Type", 70),
                            ],
                          ),
                        ),

                        // Data rows
                        Expanded(
                          child: ListView.builder(
                            itemCount: _importedData.length,
                            itemBuilder: (context, index) {
                              var student = _importedData[index];
                              return Container(
                                height: 45,
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8),
                                decoration: BoxDecoration(
                                  color: index % 2 == 0
                                      ? Colors.transparent
                                      : AppColors.grey.withValues(alpha: 0.1),
                                  border: Border(
                                    bottom: BorderSide(
                                      color:
                                          AppColors.grey.withValues(alpha: 0.3),
                                      width: 0.5,
                                    ),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    _buildFixedDataCell(
                                        student["sid"] ?? "", 80),
                                    _buildFixedDataCell(
                                        student["name"] ?? "", 120),
                                    _buildFixedDataCell(
                                        student["whatsappNumber"]?.toString() ??
                                            "",
                                        100),
                                    _buildFixedDataCell(
                                        student["contact_one"]?.toString() ??
                                            "",
                                        100),
                                    _buildFixedDataCell(
                                        student["contact_two"]?.toString() ??
                                            "",
                                        100),
                                    _buildFixedDataCell(
                                        student["pr_cgroup_name"] ?? "", 80),
                                    _buildFixedDataCell(
                                        student["pr_course_name"] ?? "", 100),
                                    _buildFixedDataCell(
                                        student["pr_section_name"] ?? "", 80),
                                    _buildFixedDataCell(
                                        student["pr_ac_year"] ?? "", 70),
                                    _buildFixedDataCell(
                                        student["activeStatus"]?.toString() ??
                                            "",
                                        60),
                                    _buildFixedDataCell(
                                        student["type"] ?? "", 70),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 15),

                  Row(
                    children: [
                      Expanded(
                        child: ButtonCustom(
                          onPressed: () {
                            setState(() {
                              _importedData.clear();
                              _selectedFile = null;
                              _uploadStatus = '';
                            });
                          },
                          title: "Cancel",
                          isOutLine: true,
                          isSecondary: false,
                        ),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        flex: 2,
                        child: ButtonCustom(
                          onPressed: _importData,
                          title:
                              "Confirm Import ${_importedData.length} Records",
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ConfirmActionDialog extends StatefulWidget {
  final String title;
  final String description;
  final String actionType;

  const ConfirmActionDialog({
    super.key,
    required this.title,
    required this.description,
    required this.actionType,
  });

  @override
  State<ConfirmActionDialog> createState() => _ConfirmActionDialogState();
}

class _ConfirmActionDialogState extends State<ConfirmActionDialog> {
  String _randomText = "";
  final TextEditingController _textController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    _randomText = (1000 + Random().nextInt(9000)).toString();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        child: Form(
          key: _formKey,
          child: Column(
            spacing: 15,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.warning_amber_rounded,
                size: 50,
                color: Colors.red,
              ),
              Text(
                widget.title,
                style: AppStyles.textSize20(fontWeight: FontWeight.w600),
                textAlign: TextAlign.center,
              ),
              Text(
                widget.description,
                style: AppStyles.textSize14(color: AppColors.grey),
                textAlign: TextAlign.center,
              ),
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red),
                ),
                child: Column(
                  children: [
                    Text(
                      "To confirm this action, please type the following code:",
                      style: AppStyles.textSize12(color: Colors.red),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _randomText,
                      style: AppStyles.textSize18(
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              TextFormField(
                controller: _textController,
                decoration: InputDecoration(
                  hintText: "Enter the code above",
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: AppColors.primary),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return "Please enter the confirmation code";
                  }
                  if (value != _randomText) {
                    return "Incorrect code. Please try again.";
                  }
                  return null;
                },
              ),
              Row(
                spacing: 10,
                children: [
                  Expanded(
                    child: ButtonCustom(
                      onPressed: () {
                        Navigator.pop(context, false);
                      },
                      title: "Cancel",
                      isSecondary: false,
                      isOutLine: true,
                    ),
                  ),
                  Expanded(
                    child: ButtonCustom(
                      onPressed: () {
                        if (_formKey.currentState!.validate()) {
                          Navigator.pop(context, true);
                        }
                      },
                      title: "Confirm",
                      backgroundColor: Colors.red,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ImportOptionsDialog extends StatelessWidget {
  const ImportOptionsDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.help_outline,
              size: 50,
              color: AppColors.primary,
            ),
            const SizedBox(height: 15),
            Text(
              "Import Data Options",
              style: AppStyles.textSize20(fontWeight: FontWeight.w600),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),
            Text(
              "Do you want to keep or delete the existing user data before importing new data?",
              style: AppStyles.textSize14(color: AppColors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),

            // Keep existing data option
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 10),
              child: ButtonCustom(
                onPressed: () {
                  Navigator.pop(context, 'keep');
                },
                title: "Keep Existing Data",
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.add_circle_outline, color: Colors.white),
                    const SizedBox(width: 8),
                    Text(
                      "Keep Existing Data",
                      style: AppStyles.textSize14(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),

            // Delete existing data option
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 10),
              child: ButtonCustom(
                onPressed: () {
                  Navigator.pop(context, 'delete');
                },
                title: "Delete Existing Data",
                backgroundColor: Colors.orange,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.refresh, color: Colors.white),
                    const SizedBox(width: 8),
                    Text(
                      "Delete & Replace All Data",
                      style: AppStyles.textSize14(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),

            // Cancel option
            SizedBox(
              width: double.infinity,
              child: ButtonCustom(
                onPressed: () {
                  Navigator.pop(context, null);
                },
                title: "Cancel",
                isSecondary: false,
                isOutLine: true,
              ),
            ),

            const SizedBox(height: 10),

            // Info text
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: AppColors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, size: 16, color: AppColors.grey),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          "Keep: Add new users alongside existing ones",
                          style: AppStyles.textSize12(color: AppColors.grey),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 5),
                  Row(
                    children: [
                      Icon(Icons.warning_amber_outlined,
                          size: 16, color: Colors.orange),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          "Delete: Remove all existing users first, then import new ones",
                          style: AppStyles.textSize12(color: AppColors.grey),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
