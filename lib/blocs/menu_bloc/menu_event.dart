part of 'menu_bloc.dart';

abstract class MenuEvent extends Equatable {
  const MenuEvent();

  @override
  List<Object?> get props => [];
}

class LoadMenuPermissions extends MenuEvent {
  final String schoolCode;
  final String sid;

  const LoadMenuPermissions({
    required this.schoolCode,
    required this.sid,
  });

  @override
  List<Object?> get props => [schoolCode, sid];
}

class RefreshMenuPermissions extends MenuEvent {
  final String schoolCode;
  final String sid;

  const RefreshMenuPermissions({
    required this.schoolCode,
    required this.sid,
  });

  @override
  List<Object?> get props => [schoolCode, sid];
}
