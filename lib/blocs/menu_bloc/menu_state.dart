part of 'menu_bloc.dart';

class MenuState extends Equatable {
  final List<MenuPermissionModel> permissions;
  final bool isLoading;
  final String? error;

  const MenuState({
    this.permissions = const [],
    this.isLoading = false,
    this.error,
  });

  factory MenuState.initial() {
    return const MenuState(
      permissions: [],
      isLoading: false,
      error: null,
    );
  }

  MenuState copyWith({
    List<MenuPermissionModel>? permissions,
    bool? isLoading,
    String? error,
  }) {
    return MenuState(
      permissions: permissions ?? this.permissions,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  @override
  List<Object?> get props => [permissions, isLoading, error];
}
