import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:chat_app/models/menu/menu_permission_model.dart';
import 'package:chat_app/repositories/education_repositories.dart';
import 'package:chat_app/utils/helper.dart';
import 'package:equatable/equatable.dart';

part 'menu_event.dart';
part 'menu_state.dart';

class MenuBloc extends Bloc<MenuEvent, MenuState> {
  final EducationRepositories _educationRepositories;

  MenuBloc({EducationRepositories? educationRepositories})
      : _educationRepositories =
            educationRepositories ?? EducationRepositories(),
        super(MenuState.initial()) {
    on<LoadMenuPermissions>(_onLoadMenuPermissions);
    on<RefreshMenuPermissions>(_onRefreshMenuPermissions);
  }

  Future<void> _onLoadMenuPermissions(
    LoadMenuPermissions event,
    Emitter<MenuState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, error: null));

    try {
      final permissions = await _educationRepositories.getMobileMenuAccess(
        schoolCode: event.schoolCode,
        userId: event.sid,
      );

      emit(state.copyWith(
        permissions: permissions,
        isLoading: false,
        error: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
      showToast('Failed to load menu permissions: ${e.toString()}');
    }
  }

  Future<void> _onRefreshMenuPermissions(
    RefreshMenuPermissions event,
    Emitter<MenuState> emit,
  ) async {
    try {
      final permissions = await _educationRepositories.getMobileMenuAccess(
        schoolCode: event.schoolCode,
        userId: event.sid,
      );

      emit(state.copyWith(
        permissions: permissions,
        error: null,
      ));
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
      showToast('Failed to refresh menu permissions: ${e.toString()}');
    }
  }

  // Helper method to check if a specific menu is enabled
  bool isMenuEnabled(String menuId) {
    return state.permissions.any(
        (permission) => permission.menuId == menuId && permission.isEnabled);
  }

  // Helper method to get menu permission by ID
  MenuPermissionModel? getMenuPermission(String menuId) {
    try {
      return state.permissions
          .firstWhere((permission) => permission.menuId == menuId);
    } catch (e) {
      return null;
    }
  }
}
