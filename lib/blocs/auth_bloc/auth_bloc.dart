import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/repositories/education_repositories.dart';
import 'package:chat_app/utils/app_constants.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:chat_app/utils/helper.dart';
import 'package:chat_app/utils/local_storage.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  AuthBloc() : super(AuthState.empty()) {
    on<GetProfile>(_getProfile);
    on<Logout>(_logout);
    on<GetStarted>(_getStarted);
    on<GetSchoolDetails>(_getSchoolDetails);
  }
  final LocalStorage _localStorage = LocalStorage();
  final EducationRepositories educationRepositories = EducationRepositories();
  FutureOr<void> _getProfile(GetProfile event, Emitter<AuthState> emit) async {
    try {
      List<UserModel> list = [];
      emit(state.copyWith(getProfileLoading: true));

      var userModel = await FirestoreService.getUserDetails(docId: event.docId);

      if (userModel != null) {
        var schoolName =
            await _localStorage.getStringValue(FirestoreService.projectName);
        emit(state.copyWith(schoolName: schoolName));

        await _localStorage.setUserDocId(event.docId);
        if (userModel.type == AppConstants.parentType) {
          for (var user in userModel.students ?? []) {
            var model = await FirestoreService.getUserDetails(docId: user);
            if (model != null) {
              list.add(model);
            }
          }

          emit(state.copyWith(students: list));
        }
        emit(state.copyWith(
          getProfileLoading: false,
          userModel: userModel,
        ));
        try {
          var user = list.first;
          var data = await educationRepositories.getStudentDetails(body: {
            "sid": user.sid,
          }, schoolCode: state.schoolName!);
          emit(state.copyWith(
            children: data,
          ));
        } catch (e) {
          print(e);
        }
        event.onSuccess();
      } else {
        throw Exception("User not found");
      }
    } catch (e) {
      event.onError?.call();
      emit(state.copyWith(getProfileLoading: false));
      showToast(e.toString());
    }
  }

  FutureOr<void> _logout(Logout event, Emitter<AuthState> emit) async {
    await _localStorage.storage.deleteAll();
    emit(AuthState.empty());
  }

  FutureOr<void> _getStarted(GetStarted event, Emitter<AuthState> emit) async {
    try {
      String? docId = await _localStorage.getUserId();
      if (docId != null) {
        add(GetProfile(
            docId: docId,
            onError: () {
              event.onSuccess(false);
            },
            onSuccess: () {
              event.onSuccess(true);
            }));
      } else {
        event.onSuccess(false);
      }
    } catch (e) {
      event.onSuccess(false);
    }
  }

  FutureOr<void> _getSchoolDetails(
      GetSchoolDetails event, Emitter<AuthState> emit) async {
    if (event.schoolCode != defaultFirebaseAppName) {
      var instance = FirebaseFirestore.instanceFor(
          app: Firebase.app(defaultFirebaseAppName));
      var result = await instance
          .collection(FirestoreService.settingsCollections)
          .doc(event.schoolCode.toLowerCase())
          .get();
      var data = result.data();
      emit(state.copyWith(
          schoolFullName: data?["full_name"] ?? "", logo: data?["logo"] ?? ""));
    }
  }
}
