part of 'auth_bloc.dart';

class AuthState extends Equatable {
  final UserModel? userModel;
  final List<UserModel>? students;
  final List<UserModel>? children;
  final String? schoolName;
  final bool? getProfileLoading;
  final String? schoolFullName;
  final String? logo;
  const AuthState(
      {this.schoolName,
      this.students,
      this.userModel,
      this.getProfileLoading,
      this.schoolFullName,
      this.logo,
      this.children});

  factory AuthState.empty() {
    return const AuthState(
      userModel: null,
      getProfileLoading: false,
      students: [],
      schoolName: '',
      children: [],
      schoolFullName: '',
      logo: '',
    );
  }

  AuthState copyWith({
    final UserModel? userModel,
    final bool? getProfileLoading,
    final List<UserModel>? students,
    final String? schoolName,
    final List<UserModel>? children,
    final String? schoolFullName,
    final String? logo,
  }) {
    return AuthState(
      schoolName: schoolName ?? this.schoolName,
      students: students ?? this.students,
      userModel: userModel ?? this.userModel,
      getProfileLoading: getProfileLoading ?? this.getProfileLoading,
      children: children ?? this.children,
      schoolFullName: schoolFullName ?? this.schoolFullName,
      logo: logo ?? this.logo,
    );
  }

  @override
  List<Object?> get props => [
        userModel,
        getProfileLoading,
        students,
        schoolName,
        children,
        schoolFullName,
        logo
      ];
}
