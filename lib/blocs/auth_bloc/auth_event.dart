part of 'auth_bloc.dart';

abstract class AuthEvent {}

class GetProfile extends AuthEvent {
  final String docId;
  final Function() onSuccess;
  final Function()? onError;
  GetProfile({required this.docId, required this.onSuccess, this.onError});
}

class Logout extends AuthEvent {}

class GetStarted extends AuthEvent {
  final Function(bool) onSuccess;

  GetStarted(this.onSuccess);
}

class GetSchoolDetails extends AuthEvent {
  final String schoolCode;

  GetSchoolDetails({required this.schoolCode});
}
