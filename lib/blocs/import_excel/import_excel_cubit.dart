import 'dart:io';

import 'package:chat_app/repositories/education_repositories.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'import_excel_state.dart';

class ImportExcelCubit extends Cubit<ImportExcelState> {
  ImportExcelCubit() : super(ImportExcelState.empty());

  final EducationRepositories educationRepositories = EducationRepositories();

  Future<void> downloadTemplateExcel({
    required Function(File?) onSuccess,
    required Function(String) onError,
  }) async {
    try {
      emit(state.copyWith(isLoading: true));
      final file = await educationRepositories.downloadTemplateExcel();
      if (file != null) {
        onSuccess(file);
      } else {
        onError("File download not supported on this platform");
      }
      emit(state.copyWith(isLoading: false));
    } catch (e) {
      emit(state.copyWith(isLoading: false));
      onError(e.toString());
    }
  }

  Future<void> importExcel({
    required Function(String) onSuccess,
    required Function(String) onError,
  }) async {
    try {
      emit(state.copyWith(isLoading: true));
    } catch (e) {
      emit(state.copyWith(isLoading: false));
      onError(e.toString());
    }
  }
}
