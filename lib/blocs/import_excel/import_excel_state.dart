part of 'import_excel_cubit.dart';

class ImportExcelState extends Equatable {
  final bool isLoading;

  const ImportExcelState({this.isLoading = false});

  factory ImportExcelState.empty() {
    return const ImportExcelState(
      isLoading: false,
    );
  }

  ImportExcelState copyWith({bool? isLoading}) {
    return ImportExcelState(
      isLoading: isLoading ?? this.isLoading,
    );
  }

  @override
  List<Object?> get props => [isLoading];
}
