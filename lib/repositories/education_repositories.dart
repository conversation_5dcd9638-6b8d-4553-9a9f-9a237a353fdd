import 'dart:convert';
import 'dart:io';

import 'package:chat_app/api/api.dart';
import 'package:chat_app/api/api_url.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/models/education/attendance_model.dart';
import 'package:chat_app/models/education/exam_model.dart';
import 'package:chat_app/models/menu/menu_permission_model.dart';
import 'package:chat_app/utils/platform_helper.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

class EducationRepositories extends Api {
  Future<List<ExamModel>> getListExamResults(
      {required String schoolCode, required Map<String, dynamic> body}) async {
    final url =
        ApiUrl.getListExamResults.replaceAll("{school_code}", schoolCode);
    final response =
        await request(url, Method.post, body: FormData.fromMap(body));
    var data = jsonDecode(response.data);
    return (data['data'] as List? ?? [])
        .map((e) => ExamModel.fromJson(e))
        .toList();
  }

  Future<AttendanceModel> getAttendanceDetails(
      {required String schoolCode, required Map<String, dynamic> body}) async {
    final url =
        ApiUrl.getAttendanceDetails.replaceAll("{school_code}", schoolCode);
    final response =
        await request(url, Method.post, body: FormData.fromMap(body));
    var data = jsonDecode(response.data);
    return AttendanceModel.fromJson(data['data']);
  }

  Future<List<UserModel>> getStudentDetails(
      {required Map<String, dynamic> body, required String schoolCode}) async {
    var url = ApiUrl.getStudentDetails.replaceAll("{school_code}", schoolCode);
    final response =
        await request(url, Method.post, body: FormData.fromMap(body));
    var data = jsonDecode(response.data);
    return ((data['data'] as List)[0] as Map<String, dynamic>).keys.map((e) {
      return UserModel.fromJson(data['data'][0][e]);
    }).toList();
  }

  Future<File?> downloadTemplateExcel() async {
    if (kIsWeb) {
      // On web, we can't save files to local file system
      return null;
    }

    var url =
        "https://docs.google.com/spreadsheets/d/15hUmaIQRe8LUvkap7YgwTT5n2HHxoScuY1fmcPEELCo/export?format=xlsx";
    var dio = Dio();
    var tempDir = await PlatformHelper.getTemporaryDirectoryCompat();
    if (tempDir == null) {
      return null;
    }
    var filePath = "${tempDir.path}/template.xlsx";
    await dio.download(url, filePath);
    var file = File(filePath);
    return file;
  }

  Future<UserModel?> addUserById(
      {required String studentId,
      required String year,
      required String schoolCode}) async {
    final url = ApiUrl.addUserById.replaceAll("{school_code}", schoolCode);
    final response = await request(url, Method.get, params: {
      "sid": studentId,
      "year": year,
    });
    var data = jsonDecode(response.data);
    if (data.length > 0) {
      // ignore: prefer_interpolation_to_compose_strings
      var name =
          (data[0]['first_name'] ?? "") + " " + (data[0]['last_name'] ?? "");
      data[0]['name'] = name;
      return UserModel.fromJson(data[0]);
    }
    return null;
  }

  Future<List<MenuPermissionModel>> getMobileMenuAccess({
    required String schoolCode,
    required String userId,
  }) async {
    final url =
        ApiUrl.getMobileMenuAccess.replaceAll("{school_code}", schoolCode);
    final response = await request(url, Method.post,
        body: FormData.fromMap({
          "user_id": userId,
        }));

    var responseData = response.data;

    // Handle new response format: {resultCode: 200, data: [...]}
    List<dynamic> menuList = [];
    if (responseData is Map<String, dynamic>) {
      if (responseData.containsKey('resultCode') &&
          responseData['resultCode'] == 200 &&
          responseData.containsKey('data') &&
          responseData['data'] is List) {
        menuList = responseData['data'] as List;
      } else if (responseData.containsKey('data') &&
          responseData['data'] is List) {
        menuList = responseData['data'] as List;
      } else if (responseData.containsKey('menu') &&
          responseData['menu'] is List) {
        menuList = responseData['menu'] as List;
      } else if (responseData.containsKey('permissions') &&
          responseData['permissions'] is List) {
        menuList = responseData['permissions'] as List;
      }
    } else if (responseData is List) {
      menuList = responseData;
    }

    return menuList
        .map((e) => MenuPermissionModel.fromJson(e as Map<String, dynamic>))
        .toList();
  }
}
