import 'package:chat_app/export.dart';
import 'package:flutter/material.dart';

import '../utils/app_colors.dart';

class CustomDialog extends StatefulWidget {
  final String title, descriptions;
  final Function? onClose;
  final Function? onPressPrimaryButton;
  final bool? isShowSecondButton;
  final Function? onPressSecondButton;
  final String? labelPrimary, labelSecondary;
  final Widget? image;
  final bool barrierDismissible;
  final bool showTitle;
  const CustomDialog({
    super.key,
    required this.title,
    required this.descriptions,
    this.onClose,
    this.barrierDismissible = true,
    this.isShowSecondButton = false,
    this.onPressSecondButton,
    this.onPressPrimaryButton,
    this.labelPrimary,
    this.labelSecondary,
    this.image,
    this.showTitle = true,
  });

  @override
  State<StatefulWidget> createState() => _CustomDialogState();
}

class _CustomDialogState extends State<CustomDialog> {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: contentBox(context),
    );
  }

  contentBox(context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      margin: const EdgeInsets.only(top: 10),
      decoration: BoxDecoration(
        shape: BoxShape.rectangle,
        color: AppColors.black,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          if (widget.showTitle)
            Padding(
              padding: const EdgeInsets.only(bottom: 15),
              child: Stack(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(right: 24, left: 24),
                    child: Center(
                      child: Text(
                        widget.title,
                        style:
                            AppStyles.textSize16(fontWeight: FontWeight.w600),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  if (widget.barrierDismissible == true)
                    Positioned(
                        right: 0,
                        child: GestureDetector(
                            onTap: () {
                              Navigator.pop(context);
                              if (widget.onClose != null) {
                                widget.onClose!();
                              }
                            },
                            child: const Icon(
                              Icons.close,
                              size: 25,
                            ))),
                ],
              ),
            ),
          if (widget.image != null)
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: widget.image,
            ),
          Text(
            widget.descriptions,
            style: AppStyles.textSize14(),
            textAlign: TextAlign.center,
          ),
          const SizedBox(
            height: 10,
          ),
          ButtonCustom(
            title: widget.labelPrimary ?? "key_ok".tr(),
            borderRadius: 5,
            height: 36,
            padding: const EdgeInsets.symmetric(vertical: 0),
            onPressed: () {
              Navigator.pop(context);
              if (widget.onPressPrimaryButton != null) {
                widget.onPressPrimaryButton!();
              } else {
                if (widget.onClose != null) {
                  widget.onClose!();
                }
              }
            },
          ),
          if (widget.isShowSecondButton!)
            Padding(
              padding: const EdgeInsets.only(top: 0),
              child: ButtonCustom(
                isOutLine: true,
                borderRadius: 5,
                height: 36,
                padding: const EdgeInsets.symmetric(vertical: 0),
                textColor: AppColors.primary,
                title: widget.labelSecondary ?? "key_cancel".tr(),
                onPressed: () {
                  Navigator.pop(context);
                  if (widget.onPressSecondButton != null) {
                    widget.onPressSecondButton!();
                  }
                },
              ),
            )
        ],
      ),
    );
  }
}
