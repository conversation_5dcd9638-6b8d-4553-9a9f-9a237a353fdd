import 'package:chat_app/utils/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../export.dart';

class CustomAppbar extends StatelessWidget implements PreferredSizeWidget {
  final double height;
  final String title;
  final bool isCenterTitle;
  final List<Widget>? actions;
  final Widget? leadingIcon;
  final bool isShowLeadingIcon;
  final TextStyle? textStyle;
  final Widget? widgetTitle;
  final bool showDivider;
  final Function? onPopBack;
  final PreferredSizeWidget? bottom;
  final Color? backgroundColor;
  const CustomAppbar(
      {super.key,
      this.height = kToolbarHeight,
      required this.title,
      this.isCenterTitle = true,
      this.actions,
      this.leadingIcon,
      this.isShowLeadingIcon = true,
      this.textStyle,
      this.widgetTitle,
      this.showDivider = false,
      this.onPopBack,
      this.backgroundColor,
      this.bottom});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: AppBar(
            systemOverlayStyle: SystemUiOverlayStyle(
              statusBarBrightness: Theme.of(context).brightness,
            ),
            elevation: 0,
            iconTheme: const IconThemeData(
              color: AppColors.primary,
            ),
            backgroundColor: backgroundColor ?? AppColors.black,
            actions: actions,
            centerTitle: isCenterTitle,
            title: widgetTitle ??
                Text(
                  title,
                  style: AppStyles.textSize16(
                    fontWeight: FontWeight.w600,
                    color: AppColors.white,
                  ),
                ),
            leading: isShowLeadingIcon
                ? leadingIcon ??
                    IconButton(
                      onPressed: () {
                        if (onPopBack != null) {
                          onPopBack!.call();
                        } else {
                          Navigator.pop(context);
                        }
                      },
                      icon: const Icon(
                        Icons.arrow_back,
                        color: AppColors.primary,
                      ),
                    )
                : null,
            bottom: bottom,
          ),
        ),
        if (showDivider)
          const Divider(
            color: AppColors.grey,
            height: 1,
          )
      ],
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height + 1);
}
