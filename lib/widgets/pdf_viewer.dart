import 'package:chat_app/widgets/custom_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:share_plus/share_plus.dart';

class PDFViewer extends StatefulWidget {
  final String url;
  final String title;
  const PDFViewer({super.key, required this.url, required this.title});

  @override
  State<PDFViewer> createState() => _PDFViewerState();
}

class _PDFViewerState extends State<PDFViewer> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: widget.title,
        actions: [
          IconButton(
            onPressed: () async {
              await Share.shareUri(Uri.parse(widget.url));
            },
            icon: const Icon(Icons.share),
          )
        ],
      ),
      body: const PDF().cachedFromUrl(
        widget.url,
        placeholder: (progress) => Center(child: Text('$progress %')),
        errorWidget: (error) => Center(child: Text(error.toString())),
      ),
    );
  }
}
