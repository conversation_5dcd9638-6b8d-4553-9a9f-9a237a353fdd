import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../utils/image_helper.dart';

class WebCompatibleImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BorderRadius? borderRadius;

  const WebCompatibleImage({
    Key? key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit,
    this.placeholder,
    this.errorWidget,
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget imageWidget;
    final webCompatibleUrl = ImageHelper.getWebCompatibleImageUrl(imageUrl);

    if (kIsWeb) {
      // On web, use Image.network without caching to avoid CORS issues
      imageWidget = Image.network(
        webCompatibleUrl,
        width: width,
        height: height,
        fit: fit ?? BoxFit.cover,
        // Remove headers that might cause CORS issues
        // headers: {
        //   'Access-Control-Allow-Origin': '*',
        //   'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        //   'Access-Control-Allow-Headers': 'Origin, Content-Type, Accept',
        // },
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return placeholder ??
              Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
                ),
              );
        },
        errorBuilder: (context, error, stackTrace) {
          return errorWidget ??
              Container(
                width: width,
                height: height,
                color: Colors.grey[300],
                child: const Icon(
                  Icons.error,
                  color: Colors.red,
                ),
              );
        },
      );
    } else {
      // On mobile, use CachedNetworkImage
      imageWidget = CachedNetworkImage(
        imageUrl: imageUrl,
        width: width,
        height: height,
        fit: fit ?? BoxFit.cover,
        placeholder: (context, url) =>
            placeholder ?? const Center(child: CircularProgressIndicator()),
        errorWidget: (context, url, error) =>
            errorWidget ??
            Container(
              width: width,
              height: height,
              color: Colors.grey[300],
              child: const Icon(
                Icons.error,
                color: Colors.red,
              ),
            ),
      );
    }

    if (borderRadius != null) {
      return ClipRRect(
        borderRadius: borderRadius!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }
}

class WebCompatibleCircleAvatar extends StatelessWidget {
  final String imageUrl;
  final double radius;
  final Widget? placeholder;
  final Widget? errorWidget;

  const WebCompatibleCircleAvatar({
    Key? key,
    required this.imageUrl,
    required this.radius,
    this.placeholder,
    this.errorWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final webCompatibleUrl = ImageHelper.getWebCompatibleImageUrl(imageUrl);

    if (kIsWeb) {
      return CircleAvatar(
        radius: radius,
        backgroundColor: Colors.grey[300],
        child: ClipOval(
          child: Image.network(
            webCompatibleUrl,
            width: radius * 2,
            height: radius * 2,
            fit: BoxFit.cover,
            // Remove headers that might cause CORS issues
            // headers: {
            //   'Access-Control-Allow-Origin': '*',
            //   'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            //   'Access-Control-Allow-Headers': 'Origin, Content-Type, Accept',
            // },
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return placeholder ??
                  Center(
                    child: CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                    ),
                  );
            },
            errorBuilder: (context, error, stackTrace) {
              return errorWidget ??
                  Icon(
                    Icons.person,
                    size: radius,
                    color: Colors.grey[600],
                  );
            },
          ),
        ),
      );
    } else {
      return CachedNetworkImage(
        imageUrl: imageUrl,
        imageBuilder: (context, imageProvider) => CircleAvatar(
          radius: radius,
          backgroundImage: imageProvider,
        ),
        placeholder: (context, url) =>
            placeholder ??
            CircleAvatar(
              radius: radius,
              backgroundColor: Colors.grey[300],
              child: const CircularProgressIndicator(),
            ),
        errorWidget: (context, url, error) =>
            errorWidget ??
            CircleAvatar(
              radius: radius,
              backgroundColor: Colors.grey[300],
              child: Icon(
                Icons.person,
                size: radius,
                color: Colors.grey[600],
              ),
            ),
      );
    }
  }
}
