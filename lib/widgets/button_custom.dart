import 'package:flutter/material.dart';

import '../utils/app_colors.dart';
import '../utils/app_styles.dart';

class ButtonCustom extends StatelessWidget {
  final Function() onPressed;
  final String title;
  final bool isSecondary, isOutLine;
  final double height;
  final double width;
  final Color? textColor, backgroundColor, borderColor;
  final TextStyle? textStyle;
  final double borderRadius;
  final EdgeInsetsGeometry? padding;
  final bool enabled;
  final Widget? child;
  const ButtonCustom({
    super.key,
    required this.onPressed,
    required this.title,
    this.isSecondary = false,
    this.height = 0,
    this.width = double.infinity,
    this.textColor,
    this.backgroundColor,
    this.borderColor,
    this.isOutLine = false,
    this.textStyle,
    this.borderRadius = 5,
    this.padding,
    this.enabled = true,
    this.child,
  });
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: enabled
          ? () {
              onPressed();
            }
          : null,
      style: OutlinedButton.styleFrom(
        elevation: 0,

        padding: padding ?? const EdgeInsets.symmetric(vertical: 12),
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius)),
        backgroundColor: enabled == false
            ? const Color(0xfff1f1f2)
            : checkBackgroundColor(), // background
        foregroundColor: checkBackgroundColor() == Colors.white
            ? Colors.grey
            : Colors.white, // foreground text
        side: enabled == false
            ? const BorderSide(color: Color(0xfff1f1f2))
            : BorderSide(
                color: checkBorderColor(),
              ), // foreground border
      ),
      child: child ??
          Center(
              child: Text(
            title,
            style: checkTextStyle(context),
          )),
    );
  }

  Color checkBackgroundColor() {
    if (backgroundColor != null) {
      return backgroundColor!;
    } else {
      if (isSecondary) {
        return AppColors.grey;
      } else if (isOutLine) {
        return Colors.transparent;
      } else {
        return AppColors.primary;
      }
    }
  }

  Color checkBorderColor() {
    if (borderColor != null) {
      return borderColor!;
    } else {
      if (isSecondary) {
        return const Color(0xffdbdbdb);
      } else if (isOutLine) {
        return AppColors.primary;
      } else {
        return AppColors.primary;
      }
    }
  }

  TextStyle checkTextStyle(BuildContext context) {
    if (textStyle != null) {
      return textStyle!;
    }
    return AppStyles.textSize16(
        fontWeight: FontWeight.w600,
        color: enabled == false
            ? const Color(0xff9a9a9a)
            : textColor == null
                ? isSecondary
                    ? AppColors.grey
                    : isOutLine
                        ? AppColors.primary
                        : Colors.white
                : textColor!);
  }
}
