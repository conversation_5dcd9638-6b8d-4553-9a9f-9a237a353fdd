import 'package:chat_app/models/auths/user_model.dart';

class ConversationModel {
  final String? createdAt;
  final String? updatedAt;
  final String? teacherId;
  final List<UserModel>? users;
  final String? lastedMessage;
  final String? title;
  final String? category;
  final String? image;
  final String? id;
  final List<String>? userIds;
  final List<String>? mutedByUsers;
  final String? teacherName;
  ConversationModel({
    this.userIds,
    this.id,
    this.category,
    this.title,
    this.createdAt,
    this.updatedAt,
    this.teacherId,
    this.users,
    this.image,
    this.lastedMessage,
    this.mutedByUsers,
    this.teacherName,
  });

  factory ConversationModel.fromJson(Map<String, dynamic> json, String id) {
    return ConversationModel(
      teacherName: json['teacher_name'],
      userIds: (json['userIds'] as List).map((e) => e.toString()).toList(),
      id: id,
      category: json['category'],
      createdAt: json['created_at'],
      lastedMessage: json['lasted_message'],
      teacherId: json['teacher_id'],
      image: json['image'],
      updatedAt: json['updated_at'],
      users: (json['users'] as List).map((e) => UserModel.fromJson(e)).toList(),
      title: json['title'],
      mutedByUsers: (json['mutedByUsers'] as List? ?? [])
          .map((e) => e.toString())
          .toList(),
    );
  }
  Map<String, dynamic> toMap() {
    return {
      'teacher_name': teacherName,
      "userIds": userIds,
      "id": id,
      "category": category,
      "created_at": createdAt,
      "updated_at": updatedAt,
      "lasted_message": lastedMessage,
      "teacher_id": teacherId,
      "image": image,
      "title": title,
      "users": users?.map((e) => e.toMap()).toList(),
      "mutedByUsers": mutedByUsers ?? [],
    };
  }
}

class ChatModel {
  final String? createdAt;
  final String? updatedAt;
  final String? message;
  final String? senderId;
  final String? type;
  final String? fileThumbnail;
  final int? fileSize;
  final String? fileName;
  final Map<String, dynamic>? linkButton;
  final String? id;
  final String? localPath;
  final String? deletedAt;
  final String? readAt;
  final List<String>? readBy;
  final String? fileContent;
  final String? senderName;
  final ChatModel? replyChat;
  final bool? isHomework;
  final bool? isNotes;
  final String? homeworkSetAt;
  final String? notesSetAt;
  // Optimistic UI fields
  final bool? isPending;
  final bool? isFailed;
  final String? tempId;
  ChatModel({
    this.readBy,
    this.createdAt,
    this.type,
    this.updatedAt,
    this.message,
    this.senderId,
    this.fileThumbnail,
    this.fileName,
    this.fileSize,
    this.linkButton,
    this.id,
    this.localPath,
    this.deletedAt,
    this.readAt,
    this.fileContent,
    this.senderName,
    this.replyChat,
    this.isHomework,
    this.isNotes,
    this.homeworkSetAt,
    this.notesSetAt,
    this.isPending,
    this.isFailed,
    this.tempId,
  });

  factory ChatModel.fromJson(Map<String, dynamic> json,
      {String? id, String? localPath}) {
    return ChatModel(
      fileContent: json['file_content'],
      readBy:
          (json['read_by'] as List? ?? []).map((e) => e.toString()).toList(),
      createdAt: json['created_at'],
      message: json['message'],
      senderId: json['sender_id'],
      updatedAt: json['updated_at'],
      type: json['type'],
      fileThumbnail: json['file_thumbnail'],
      fileName: json['file_name'],
      fileSize: json['file_size'],
      linkButton: json['linkButton'],
      id: id ?? json['id'],
      localPath: localPath,
      deletedAt: json['deleted_at'],
      readAt: json['read_at'],
      senderName: json['sender_name'],
      replyChat:
          json['reply'] != null ? ChatModel.fromJson(json['reply']) : null,
      isHomework: json['is_homework'],
      isNotes: json['is_notes'],
      homeworkSetAt: json['homework_set_at'],
      notesSetAt: json['notes_set_at'],
      // Optimistic UI fields are not stored in Firestore
      isPending: false,
      isFailed: false,
      tempId: null,
    );
  }
  Map<String, dynamic> toMap() {
    return {
      "created_at": createdAt,
      "message": message,
      "sender_id": senderId,
      "updated_at": updatedAt,
      "type": type,
      "file_thumbnail": fileThumbnail,
      "file_size": fileSize,
      "linkButton": linkButton,
      "deleted_at": deletedAt,
      "read_at": readAt,
      "read_by": readBy,
      'file_content': fileContent,
      'sender_name': senderName,
      'reply': replyChat?.toMap(),
      'id': id,
      'is_homework': isHomework,
      'is_notes': isNotes,
      'homework_set_at': homeworkSetAt,
      'notes_set_at': notesSetAt,
    };
  }

  ChatModel copyWith({
    final String? createdAt,
    final String? updatedAt,
    final String? message,
    final String? senderId,
    final String? type,
    final String? fileThumbnail,
    final int? fileSize,
    final String? fileName,
    final Map<String, dynamic>? linkButton,
    final String? id,
    final String? localPath,
    final String? deletedAt,
    final String? readAt,
    final List<String>? readBy,
    final String? senderName,
    final ChatModel? replyChat,
    final bool? isHomework,
    final bool? isNotes,
    final String? homeworkSetAt,
    final String? notesSetAt,
    final bool? isPending,
    final bool? isFailed,
    final String? tempId,
  }) {
    return ChatModel(
      senderName: senderName ?? this.senderName,
      fileContent: fileContent,
      readBy: readBy ?? this.readBy,
      readAt: readAt ?? this.readAt,
      createdAt: createdAt ?? this.createdAt,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      fileThumbnail: fileThumbnail ?? this.fileThumbnail,
      id: id ?? this.id,
      linkButton: linkButton ?? this.linkButton,
      localPath: localPath ?? this.localPath,
      message: message ?? this.message,
      senderId: senderId ?? this.senderId,
      type: type ?? this.type,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      replyChat: replyChat ?? this.replyChat,
      isHomework: isHomework ?? this.isHomework,
      isNotes: isNotes ?? this.isNotes,
      homeworkSetAt: homeworkSetAt ?? this.homeworkSetAt,
      notesSetAt: notesSetAt ?? this.notesSetAt,
      isPending: isPending ?? this.isPending,
      isFailed: isFailed ?? this.isFailed,
      tempId: tempId ?? this.tempId,
    );
  }

  /// Create an optimistic message for immediate UI display
  factory ChatModel.createOptimistic({
    required String message,
    required String senderId,
    required String senderName,
    required String type,
    Map<String, dynamic>? linkButton,
    ChatModel? replyChat,
    String? fileName,
    int? fileSize,
    String? fileContent,
  }) {
    final now = DateTime.now().toUtc().toIso8601String();
    final tempId = 'temp_${DateTime.now().millisecondsSinceEpoch}';

    return ChatModel(
      tempId: tempId,
      id: tempId,
      message: message,
      senderId: senderId,
      senderName: senderName,
      type: type,
      createdAt: now,
      updatedAt: now,
      isPending: true,
      isFailed: false,
      linkButton: linkButton,
      replyChat: replyChat,
      fileName: fileName,
      fileSize: fileSize,
      fileContent: fileContent,
      readBy: [],
    );
  }
}

class ActivityModel {
  final String? senderId;
  final String? receiverId;
  final String? createdAt;
  final String? readAt;
  final String? message;
  final String? id;
  final String? senderName;
  final String? receiverName;
  final String? linkContent;
  final String? type;
  final Map<String, dynamic>? metadata;

  ActivityModel({
    this.senderId,
    this.receiverId,
    this.createdAt,
    this.id,
    this.readAt,
    this.message,
    this.senderName,
    this.receiverName,
    this.linkContent,
    this.metadata,
    this.type,
  });

  factory ActivityModel.fromJson(Map<String, dynamic> json, String id) {
    return ActivityModel(
      type: json['type'],
      metadata: json['metadata'],
      createdAt: json['created_at'],
      message: json['message'],
      readAt: json['read_at'],
      receiverId: json['receiver_id'],
      senderId: json['sender_id'],
      id: id,
      receiverName: json['receiver_name'],
      senderName: json['sender_name'],
      linkContent: json['link_content'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      "metadata": metadata,
      "type": type,
      "created_at": createdAt,
      "message": message,
      "read_at": readAt,
      "receiver_id": receiverId,
      "sender_id": senderId,
      "id": id,
      "receiver_name": receiverName,
      "sender_name": senderName,
      "link_content": linkContent,
    };
  }
}
