import 'package:equatable/equatable.dart';

class UserModel extends Equatable {
  final String? name;
  final String? type;
  final dynamic phoneNumber;
  final String? avatar;
  final String? docId;
  final String? parentId;
  final String? prCourseName;
  final String? prSectionName;
  final String? prAcYear;
  final String? prCGroupName;
  final dynamic whatsappNumber;
  final dynamic contactOne;
  final dynamic contactTwo;
  final String? fcmToken;
  final String? currentConversationId;
  final List<String>? muteNotificationRooms;
  final List<String>? students;
  final String? sid;
  final String? schoolCode;
  final String? dob;
  final String? dateOfSentBirthdayWishes;
  final int? activeStatus;
  final String? dateOfSentAbsentesMessages;
  final int? role;
  final double? similarity;
  final String? updatedAt;
  const UserModel({
    this.docId,
    this.parentId,
    this.name,
    this.type,
    this.phoneNumber,
    this.avatar,
    this.prAcYear,
    this.prCGroupName,
    this.prCourseName,
    this.prSectionName,
    this.whatsappNumber,
    this.contactOne,
    this.contactTwo,
    this.fcmToken,
    this.currentConversationId,
    this.muteNotificationRooms,
    this.students,
    this.sid,
    this.schoolCode,
    this.dob,
    this.dateOfSentBirthdayWishes,
    this.activeStatus,
    this.dateOfSentAbsentesMessages,
    this.role,
    this.similarity,
    this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json, {String? docId}) {
    return UserModel(
      role: json['role'] ?? 0,
      dateOfSentAbsentesMessages: json['date_of_sent_absentes_messages'],
      activeStatus: int.tryParse("${json['activeStatus']}") ?? 1,
      dateOfSentBirthdayWishes: json['date_of_sent_birthday_wishes'],
      dob: json['dob'],
      schoolCode: json['school_code'],
      sid: "${json['sid']}",
      fcmToken: json['fcmToken'],
      avatar: json['avatar'],
      name: json['name'],
      phoneNumber: json['contact_one'],
      type: json['type'],
      docId: docId ?? json['docId'],
      parentId: json['parent_id'],
      prAcYear: json['pr_ac_year'],
      prCGroupName: json['pr_cgroup_name'],
      prCourseName: "${json['pr_course_name']}",
      prSectionName: json['pr_section_name'],
      contactOne: json['contact_one'],
      contactTwo: json['contact_two'],
      whatsappNumber: json['whatsappNumber'],
      currentConversationId: json['current_conversation_id'],
      muteNotificationRooms: (json['muteNotificationRooms'] as List? ?? [])
          .map((e) => e.toString())
          .toList(),
      similarity: 0,
      students:
          (json['students'] as List? ?? []).map((e) => e.toString()).toList(),
      updatedAt: json['updated_at'],
    );
  }
  UserModel copyWith({
    String? docId,
    String? dateOfSentBirthdayWishes,
    double? similarity,
    String? prCGroupName,
    String? prCourseName,
    String? prSectionName,
    String? prAcYear,
  }) {
    return UserModel(
      similarity: similarity ?? this.similarity,
      role: role,
      dateOfSentAbsentesMessages: dateOfSentAbsentesMessages,
      avatar: avatar ?? this.avatar,
      docId: docId ?? this.docId,
      name: name ?? this.name,
      parentId: parentId ?? this.parentId,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      type: type ?? this.type,
      prAcYear: prAcYear ?? this.prAcYear,
      prCGroupName: prCGroupName ?? this.prCGroupName,
      prCourseName: prCourseName ?? this.prCourseName,
      prSectionName: prSectionName ?? this.prSectionName,
      contactOne: contactOne,
      contactTwo: contactTwo,
      whatsappNumber: whatsappNumber,
      fcmToken: fcmToken,
      currentConversationId: currentConversationId,
      muteNotificationRooms: muteNotificationRooms,
      students: students,
      sid: sid,
      schoolCode: schoolCode,
      dob: dob,
      dateOfSentBirthdayWishes:
          dateOfSentBirthdayWishes ?? this.dateOfSentBirthdayWishes,
      activeStatus: activeStatus,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      "role": role,
      "date_of_sent_absentes_messages": dateOfSentAbsentesMessages,
      'avatar': avatar,
      'name': name,
      'contact_one': phoneNumber,
      'type': type,
      'docId': docId,
      'parent_id': parentId,
      'pr_ac_year': prAcYear,
      'pr_cgroup_name': prCGroupName,
      'pr_course_name': prCourseName,
      'pr_section_name': prSectionName,
      'contact_two': contactTwo,
      'whatsappNumber': whatsappNumber,
      "fcmToken": fcmToken,
      'current_conversation_id': currentConversationId,
      "muteNotificationRooms": muteNotificationRooms ?? [],
      "students": students,
      "sid": sid,
      "school_code": schoolCode,
      "dob": dob,
      "date_of_sent_birthday_wishes": dateOfSentBirthdayWishes,
      "activeStatus": activeStatus,
      "updated_at": updatedAt,
    };
  }

  @override
  List<Object?> get props => [
        this.docId,
        this.parentId,
        this.name,
        this.type,
        this.phoneNumber,
        this.avatar,
        this.prAcYear,
        this.prCGroupName,
        this.prCourseName,
        this.prSectionName,
        this.whatsappNumber,
        this.contactOne,
        this.contactTwo,
        this.fcmToken,
        this.currentConversationId,
        this.muteNotificationRooms,
        this.students,
        this.sid,
        this.schoolCode,
        this.dob,
        this.dateOfSentBirthdayWishes,
        this.activeStatus,
        this.dateOfSentAbsentesMessages,
        this.role,
        this.similarity,
        this.updatedAt,
      ];
}
