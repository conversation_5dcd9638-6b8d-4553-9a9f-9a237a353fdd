import 'package:equatable/equatable.dart';

class ExamModel extends Equatable {
  final dynamic rId;
  final String? mainActivity;
  final String? examDate;
  final String? pdfLink;
  final String? remark;
  final dynamic paMactyId;

  ExamModel({
    this.rId,
    this.mainActivity,
    this.examDate,
    this.pdfLink,
    this.remark,
    this.paMactyId,
  });

  // Factory constructor for creating an Activity instance from a JSON map
  factory ExamModel.fromJson(Map<String, dynamic> json) {
    return ExamModel(
      rId: json['r_id'],
      mainActivity: json['mainactivity'],
      examDate: json['exam_date'],
      pdfLink: json['pdf_link'],
      remark: json['remark'],
      paMactyId: json['pa_macty_id'],
    );
  }

  // Method for converting an Activity instance to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'r_id': rId,
      'mainactivity': mainActivity,
      'exam_date': examDate,
      'pdf_link': pdfLink,
      'remark': remark,
      'pa_macty_id': paMactyId,
    };
  }

  // Equatable override to enable value comparison
  @override
  List<Object?> get props =>
      [rId, mainActivity, examDate, pdfLink, remark, paMactyId];
}
