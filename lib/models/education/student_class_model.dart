class StudentClassModel {
  final String docId;
  final String sid;
  final String? prCourseName;
  final String? prSectionName;
  final String? prAcYear;
  final String? prCGroupName;
  final String? createdAt;
  final String? updatedAt;
  final String? studentName;

  StudentClassModel(
      {required this.docId,
      required this.sid,
      required this.prCourseName,
      required this.prSectionName,
      required this.prAcYear,
      required this.prCGroupName,
      this.createdAt,
      this.updatedAt,
      this.studentName});

  factory StudentClassModel.fromJson(Map<String, dynamic> json) {
    return StudentClassModel(
      docId: json['docId'],
      sid: json['sid'],
      prCourseName: json['prCourseName'],
      prSectionName: json['prSectionName'],
      prAcYear: json['prAcYear'],
      prCGroupName: json['prCGroupName'],
      createdAt: json['createdAt'],
      updatedAt: json['updatedAt'],
      studentName: json['studentName'],
    );
  }

  StudentClassModel copyWith({
    String? prCGroupName,
    String? prCourseName,
    String? prSectionName,
    String? prAcYear,
    String? createdAt,
    String? updatedAt,
    String? docId,
    String? studentName,
  }) {
    return StudentClassModel(
      docId: docId ?? this.docId,
      sid: sid,
      prCourseName: prCourseName ?? this.prCourseName,
      prSectionName: prSectionName ?? this.prSectionName,
      prAcYear: prAcYear ?? this.prAcYear,
      prCGroupName: prCGroupName ?? this.prCGroupName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      studentName: studentName ?? this.studentName,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'sid': sid,
      'prCourseName': prCourseName,
      'prSectionName': prSectionName,
      'prAcYear': prAcYear,
      'prCGroupName': prCGroupName,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'studentName': studentName,
    };
  }
}
