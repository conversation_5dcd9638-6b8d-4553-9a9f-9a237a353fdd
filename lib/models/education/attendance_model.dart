import 'package:equatable/equatable.dart';

class AttendanceModel extends Equatable {
  final int? presentCount;
  final int? absentCount;
  final int? partialCount;
  final int? ndCount;
  final int? holidayCount;
  final List<dynamic>? sessionList;
  final List<Attendance>? record;

  const AttendanceModel(
      {required this.presentCount,
      required this.absentCount,
      required this.partialCount,
      required this.ndCount,
      required this.holidayCount,
      required this.sessionList,
      required this.record});

  factory AttendanceModel.fromJson(Map<String, dynamic> json) {
    return AttendanceModel(
      presentCount: json['presentCount'],
      absentCount: json['absentCount'],
      partialCount: json['partialCount'],
      ndCount: json['ndCount'],
      holidayCount: json['holidayCount'],
      sessionList: json['sessionList'],
      record: (json['record'] as List? ?? [])
          .map((e) => Attendance.fromJson(e))
          .toList(),
    );
  }
  @override
  List<Object?> get props => [
        presentCount,
        absentCount,
        partialCount,
        ndCount,
        holidayCount,
        sessionList,
        record
      ];
}

// SessionDetail class
class SessionDetail extends Equatable {
  final int? attendSesnId;
  final String? sessionType;
  final String? attShortName;
  final String? weekDay;
  final String? attendValue;
  final int? batchId;
  final int? deleteStatus;
  final int? verifySessionId;
  final int? absesntSessionId;
  final int? presentSession;
  final int? absentSession;
  final String? status;

  const SessionDetail({
    required this.attendSesnId,
    required this.sessionType,
    required this.attShortName,
    required this.weekDay,
    required this.attendValue,
    required this.batchId,
    required this.deleteStatus,
    this.verifySessionId,
    this.absesntSessionId,
    required this.presentSession,
    required this.absentSession,
    required this.status,
  });

  factory SessionDetail.fromJson(Map<String, dynamic> json) {
    return SessionDetail(
      attendSesnId: json['attend_sesn_id'],
      sessionType: json['session_type'],
      attShortName: json['att_short_name'],
      weekDay: json['week_day'],
      attendValue: json['attend_value'],
      batchId: json['batch_id'],
      deleteStatus: json['delete_status'],
      verifySessionId: json['verifySessionId'],
      absesntSessionId: json['absesntSessionId'],
      presentSession: json['presentSession'],
      absentSession: json['absentSession'],
      status: json['status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'attend_sesn_id': attendSesnId,
      'session_type': sessionType,
      'att_short_name': attShortName,
      'week_day': weekDay,
      'attend_value': attendValue,
      'batch_id': batchId,
      'delete_status': deleteStatus,
      'verifySessionId': verifySessionId,
      'absesntSessionId': absesntSessionId,
      'presentSession': presentSession,
      'absentSession': absentSession,
      'status': status,
    };
  }

  @override
  List<Object?> get props => [
        attendSesnId,
        sessionType,
        attShortName,
        weekDay,
        attendValue,
        batchId,
        deleteStatus,
        verifySessionId,
        absesntSessionId,
        presentSession,
        absentSession,
        status
      ];
}

// Attendance class
class Attendance extends Equatable {
  final String? attendanceDate;
  final String? type;
  final String? overAllStatus;
  final List<SessionDetail>? sessionDetails;

  const Attendance({
    required this.attendanceDate,
    required this.type,
    required this.overAllStatus,
    required this.sessionDetails,
  });

  factory Attendance.fromJson(Map<String, dynamic> json) {
    var sessionDetailsJson = json['sessionDetails'] as List? ?? [];
    List<SessionDetail> sessionDetailsList =
        sessionDetailsJson.map((e) => SessionDetail.fromJson(e)).toList();

    return Attendance(
      attendanceDate: json['attendanceDate'],
      type: json['type'],
      overAllStatus: json['overAllStatus'],
      sessionDetails: sessionDetailsList,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'attendanceDate': attendanceDate,
      'type': type,
      'overAllStatus': overAllStatus,
      'sessionDetails': sessionDetails?.map((e) => e.toJson()).toList() ?? [],
    };
  }

  @override
  List<Object?> get props =>
      [attendanceDate, type, overAllStatus, sessionDetails];
}
