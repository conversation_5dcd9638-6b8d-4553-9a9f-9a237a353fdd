import 'package:equatable/equatable.dart';

class MenuPermissionModel extends Equatable {
  final String? appType;
  final String? menuTitle;
  final String? menuId;
  final int? permission;
  final int? userId;

  const MenuPermissionModel({
    this.appType,
    this.menuTitle,
    this.menuId,
    this.permission,
    this.userId,
  });

  factory MenuPermissionModel.fromJson(Map<String, dynamic> json) {
    return MenuPermissionModel(
      appType: json['app_type']?.toString(),
      menuTitle: json['menu_title']?.toString(),
      menuId: json['menu_id']?.toString(),
      permission: int.tryParse(json['permission']?.toString() ?? '0') ?? 0,
      userId: int.tryParse(json['user_id']?.toString() ?? '0') ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'app_type': appType,
      'menu_title': menuTitle,
      'menu_id': menuId,
      'permission': permission,
      'user_id': userId,
    };
  }

  MenuPermissionModel copyWith({
    String? appType,
    String? menuTitle,
    String? menuId,
    int? permission,
    int? userId,
  }) {
    return MenuPermissionModel(
      appType: appType ?? this.appType,
      menuTitle: menuTitle ?? this.menuTitle,
      menuId: menuId ?? this.menuId,
      permission: permission ?? this.permission,
      userId: userId ?? this.userId,
    );
  }

  // Helper method to check if menu is enabled
  bool get isEnabled => permission == 1;

  // Helper method to get display name
  String get displayName => menuTitle ?? menuId ?? 'Unknown Menu';

  @override
  List<Object?> get props => [
        appType,
        menuTitle,
        menuId,
        permission,
        userId,
      ];
}
