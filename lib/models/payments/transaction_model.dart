class TransactionModel {
  final String? orderId;
  final String? transactionId;
  final dynamic platformBillId;
  final String? studentId;
  final dynamic paidAmount;
  final dynamic feeAmount;
  final dynamic conveyanceFee;
  final String? status;
  final String? transactionAt;
  final String? paymentType;

  TransactionModel(
      {this.orderId,
      this.transactionId,
      this.platformBillId,
      this.studentId,
      this.paidAmount,
      this.feeAmount,
      this.conveyanceFee,
      this.status,
      this.transactionAt,
      this.paymentType});

  factory TransactionModel.fromJson(Map<String, dynamic> json) {
    return TransactionModel(
      conveyanceFee: json['conveyance_fee'],
      feeAmount: json['feeAmount'],
      orderId: json['order_id'],
      paidAmount: json['paidamount'],
      paymentType: json['payment_type'],
      platformBillId: json['platformBillID'],
      status: json['status'],
      studentId: json['student_id'],
      transactionAt: json['transaction_at'],
      transactionId: json['transaction_id'],
    );
  }
}
