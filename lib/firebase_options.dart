// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCbW-_hAd9m6reTHBEHbXstWUzymIu2irw',
    appId: '1:1047871199588:web:4e2bab7e0d25e964e2c271',
    messagingSenderId: '1047871199588',
    projectId: 'messagebox-ce8a4',
    authDomain: 'messagebox-ce8a4.firebaseapp.com',
    storageBucket: 'messagebox-ce8a4.appspot.com',
    measurementId: 'G-90PN78TDWR',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyA2FMrQr2iCbG8CKTpnEKabcity43QDzH0',
    appId: '1:1047871199588:android:9fff1d451bb11056e2c271',
    messagingSenderId: '1047871199588',
    projectId: 'messagebox-ce8a4',
    storageBucket: 'messagebox-ce8a4.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBwYcFQlhK_QW-tbzjVbaPu9PdMYnjj5bw',
    appId: '1:1047871199588:ios:02265030caf2485ce2c271',
    messagingSenderId: '1047871199588',
    projectId: 'messagebox-ce8a4',
    storageBucket: 'messagebox-ce8a4.appspot.com',
    iosBundleId: 'com.surena.schoolsmessanger.teacher',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBwYcFQlhK_QW-tbzjVbaPu9PdMYnjj5bw',
    appId: '1:1047871199588:ios:5fef5e56a8be654fe2c271',
    messagingSenderId: '1047871199588',
    projectId: 'messagebox-ce8a4',
    storageBucket: 'messagebox-ce8a4.appspot.com',
    iosBundleId: 'com.example.chatApp',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyB-he5BeXormgX0rz8As_LCDNB0Q8Pm2MY',
    appId: '1:1047871199588:web:f48babf3466b688ae2c271',
    messagingSenderId: '1047871199588',
    projectId: 'messagebox-ce8a4',
    authDomain: 'messagebox-ce8a4.firebaseapp.com',
    storageBucket: 'messagebox-ce8a4.appspot.com',
    measurementId: 'G-KYKPPQS3Q5',
  );

}