import 'dart:ui';

import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/screens/auth/splash_screen.dart';

import 'package:chat_app/utils/app_colors.dart';

import 'package:chat_app/utils/local_storage.dart';
import 'package:chat_app/utils/navigator_service.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:toastification/toastification.dart';

import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  FlutterError.onError = (errorDetails) {
    FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  };
  // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };
  bool isDark = await LocalStorage().isDarkTheme();
  AppColors.isDark = isDark;

  runApp(
    EasyLocalization(
      supportedLocales: [
        Locale('en', 'US'),
        Locale('hi', 'IN'), // Hindi
        Locale('ta', 'IN'), // Tamil
        Locale('kn', 'IN'), // Kannada
        Locale('te', 'IN'), // Telugu
      ],
      path: 'assets/translations',
      fallbackLocale: Locale('en', 'US'),
      child: MyApp(
        isDark: isDark,
      ),
    ),
  );
}

class MyApp extends StatelessWidget {
  final bool isDark;
  const MyApp({super.key, required this.isDark});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return ToastificationWrapper(
      child: MultiBlocProvider(
        providers: [
          BlocProvider(create: (_) => AuthBloc()),
        ],
        child: OverlaySupport.global(
          child: MaterialApp(
            navigatorKey: NavigationService.instance.navigationKey,
            themeMode: isDark ? ThemeMode.dark : ThemeMode.light,
            localizationsDelegates: context.localizationDelegates,
            supportedLocales: context.supportedLocales,
            locale: context.locale,
            debugShowCheckedModeBanner: false,
            theme: ThemeData(
              primaryColor: AppColors.primary,
              iconTheme: IconThemeData(color: AppColors.primary),
              textSelectionTheme:
                  TextSelectionThemeData(selectionColor: AppColors.primary),
              // selectedRowColor: AppColors.primary,
              checkboxTheme: CheckboxThemeData(
                overlayColor: WidgetStatePropertyAll(AppColors.primary),
                fillColor: WidgetStatePropertyAll(AppColors.primary),
                checkColor: WidgetStatePropertyAll(Colors.white),
                side: BorderSide(color: AppColors.primary),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5),
                    side: BorderSide(color: AppColors.primary)),
              ),
              floatingActionButtonTheme: const FloatingActionButtonThemeData(
                  backgroundColor: Colors.green),
              brightness: isDark ? Brightness.dark : Brightness.light,
              primarySwatch: Colors.green,
            ),
            home: const SplashScreen(),
          ),
        ),
      ),
    );
  }
}
