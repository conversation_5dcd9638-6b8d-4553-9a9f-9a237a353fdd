import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:io';

class AppVersionService {
  static const String _versionCollection = 'app_versions';

  /// Get current platform document name
  static String get _currentPlatformDoc {
    if (kIsWeb) return 'web';
    if (Platform.isAndroid) return 'android';
    return 'ios';
  }

  /// Get current app version
  static Future<String> getCurrentVersion() async {
    if (kIsWeb) {
      // For web, return a default version or get from environment
      return '1.0.20';
    }

    final packageInfo = await PackageInfo.fromPlatform();
    return packageInfo.version;
  }

  /// Get current build number
  static Future<String> getCurrentBuildNumber() async {
    if (kIsWeb) {
      return '170';
    }

    final packageInfo = await PackageInfo.fromPlatform();
    return packageInfo.buildNumber;
  }

  /// Check if app update is available
  static Future<AppUpdateInfo?> checkForUpdate() async {
    try {
      final currentVersion = await getCurrentVersion();
      final currentBuildNumber = await getCurrentBuildNumber();

      final doc = await FirebaseFirestore.instance
          .collection(_versionCollection)
          .doc(_currentPlatformDoc)
          .get();

      if (!doc.exists) {
        return null;
      }

      final data = doc.data()!;
      final latestVersion = data['version'] as String;
      final latestBuildNumber = data['build_number'] as String;
      final isForceUpdate = data['force_update'] as bool? ?? false;
      final updateMessage =
          data['message'] as String? ?? 'A new version is available';
      final downloadUrl = data['download_url'] as String?;

      // Compare versions
      if (_isNewerVersion(latestVersion, currentVersion) ||
          _isNewerBuildNumber(latestBuildNumber, currentBuildNumber)) {
        return AppUpdateInfo(
          currentVersion: currentVersion,
          latestVersion: latestVersion,
          currentBuildNumber: currentBuildNumber,
          latestBuildNumber: latestBuildNumber,
          isForceUpdate: isForceUpdate,
          message: updateMessage,
          downloadUrl: downloadUrl,
        );
      }

      return null;
    } catch (e) {
      debugPrint('Error checking for update: $e');
      return null;
    }
  }

  /// Compare version strings (e.g., "1.0.20" vs "1.0.21")
  static bool _isNewerVersion(String latestVersion, String currentVersion) {
    final latest = latestVersion.split('.').map(int.parse).toList();
    final current = currentVersion.split('.').map(int.parse).toList();

    for (int i = 0; i < latest.length && i < current.length; i++) {
      if (latest[i] > current[i]) return true;
      if (latest[i] < current[i]) return false;
    }

    return latest.length > current.length;
  }

  /// Compare build numbers
  static bool _isNewerBuildNumber(String latestBuild, String currentBuild) {
    try {
      return int.parse(latestBuild) > int.parse(currentBuild);
    } catch (e) {
      return false;
    }
  }

  /// Show update dialog
  static Future<void> showUpdateDialog(
    BuildContext context,
    AppUpdateInfo updateInfo,
  ) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: !updateInfo.isForceUpdate,
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop: () async => !updateInfo.isForceUpdate,
          child: AlertDialog(
            title: Row(
              children: [
                Icon(
                  Icons.system_update,
                  color: Colors.blue,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  updateInfo.isForceUpdate
                      ? 'Required Update'
                      : 'Update Available',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(updateInfo.message),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('Current Version:'),
                          Text(
                            '${updateInfo.currentVersion} (${updateInfo.currentBuildNumber})',
                            style: TextStyle(fontWeight: FontWeight.w500),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('Latest Version:'),
                          Text(
                            '${updateInfo.latestVersion} (${updateInfo.latestBuildNumber})',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                if (updateInfo.isForceUpdate) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: Colors.orange.withOpacity(0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.warning, color: Colors.orange, size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'This update is required to continue using the app.',
                            style: TextStyle(
                              color: Colors.orange.shade700,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
            actions: [
              if (!updateInfo.isForceUpdate)
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text('Later'),
                ),
              ElevatedButton(
                onPressed: () => _handleUpdate(context, updateInfo),
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      updateInfo.isForceUpdate ? Colors.red : Colors.blue,
                ),
                child: Text(
                  updateInfo.isForceUpdate ? 'Update Now' : 'Update',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Handle update action
  static Future<void> _handleUpdate(
    BuildContext context,
    AppUpdateInfo updateInfo,
  ) async {
    if (updateInfo.downloadUrl != null) {
      try {
        final uri = Uri.parse(updateInfo.downloadUrl!);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
          if (!updateInfo.isForceUpdate) {
            Navigator.of(context).pop();
          }
        } else {
          _showErrorDialog(context, 'Cannot open update link');
        }
      } catch (e) {
        _showErrorDialog(context, 'Error opening update link: $e');
      }
    } else {
      // Default store links with correct URLs
      String storeUrl;
      if (Platform.isAndroid) {
        storeUrl =
            'https://play.google.com/store/apps/details?id=com.surena.schoolsmessanger.teacher';
      } else if (Platform.isIOS) {
        storeUrl =
            'https://apps.apple.com/us/app/schoolmessenger-teacher/id6744751476';
      } else {
        _showErrorDialog(context, 'Update not available for this platform');
        return;
      }

      try {
        final uri = Uri.parse(storeUrl);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
          if (!updateInfo.isForceUpdate) {
            Navigator.of(context).pop();
          }
        } else {
          _showErrorDialog(context, 'Cannot open app store');
        }
      } catch (e) {
        debugPrint('Error opening app store: $e');
        _showErrorDialog(
            context, 'Error opening app store. Please try again later.');
      }
    }
  }

  /// Show error dialog
  static void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }
}

/// App update information model
class AppUpdateInfo {
  final String currentVersion;
  final String latestVersion;
  final String currentBuildNumber;
  final String latestBuildNumber;
  final bool isForceUpdate;
  final String message;
  final String? downloadUrl;

  AppUpdateInfo({
    required this.currentVersion,
    required this.latestVersion,
    required this.currentBuildNumber,
    required this.latestBuildNumber,
    required this.isForceUpdate,
    required this.message,
    this.downloadUrl,
  });

  @override
  String toString() {
    return 'AppUpdateInfo(current: $currentVersion+$currentBuildNumber, '
        'latest: $latestVersion+$latestBuildNumber, force: $isForceUpdate)';
  }
}
