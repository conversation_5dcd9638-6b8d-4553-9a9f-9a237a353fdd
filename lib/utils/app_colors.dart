import 'package:flutter/material.dart';

class AppColors {
  static bool isDark = true;
  static const Color primary = Color(0xff7CB342);
  static const Color secondaryColor = Colors.cyan;
  static Color get black => isDark ? const Color(0xff263238) : Colors.white;
  static Color get white => isDark ? Colors.white : const Color(0xff263238);
  static const Color grey = Colors.grey;
  static Color get backgroundScaffold => isDark
      ? const Color.fromARGB(255, 36, 31, 29)
      : const Color.fromARGB(255, 229, 228, 228);
  static Color presentColor = Colors.green;
  static Color halfDayColor = const Color.fromARGB(255, 11, 79, 135);
  static Color absentColor = Colors.red;
  static Color holidayColor = Colors.blue;
  static Color partialLeave = const Color.fromARGB(255, 11, 79, 135);
}
