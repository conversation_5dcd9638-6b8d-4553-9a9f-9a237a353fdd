import 'dart:html' as html;
import 'dart:typed_data';
import 'package:flutter/foundation.dart';

class WebDownloadHelper {
  /// Download file on web platform
  static void downloadFile({
    required Uint8List bytes,
    required String fileName,
    String? mimeType,
  }) {
    if (!kIsWeb) return;

    // Create blob
    final blob = html.Blob([bytes], mimeType ?? 'application/octet-stream');
    
    // Create download URL
    final url = html.Url.createObjectUrlFromBlob(blob);
    
    // Create anchor element and trigger download
    final anchor = html.AnchorElement(href: url)
      ..setAttribute('download', fileName)
      ..click();
    
    // Clean up
    html.Url.revokeObjectUrl(url);
  }

  /// Download file from URL on web platform
  static void downloadFromUrl({
    required String url,
    required String fileName,
  }) {
    if (!kIsWeb) return;

    // Create anchor element and trigger download
    final anchor = html.AnchorElement(href: url)
      ..setAttribute('download', fileName)
      ..setAttribute('target', '_blank')
      ..click();
  }

  /// Open URL in new tab
  static void openUrl(String url) {
    if (!kIsWeb) return;
    
    html.window.open(url, '_blank');
  }
}
