import 'dart:async';
import 'dart:html' as html;
import 'package:flutter/foundation.dart';

class CorsDebug {
  /// Test CORS for a given URL
  static Future<bool> testCors(String url) async {
    if (!kIsWeb) return true; // CORS only applies to web

    try {
      final request = html.HttpRequest();
      request.open('HEAD', url);

      // Add CORS headers
      request.setRequestHeader('Access-Control-Request-Method', 'GET');
      request.setRequestHeader(
          'Access-Control-Request-Headers', 'Content-Type');

      final completer = Completer<bool>();

      request.onLoad.listen((event) {
        print('CORS test successful for: $url');
        print('Status: ${request.status}');
        print('Response headers: ${request.getAllResponseHeaders()}');
        completer.complete(true);
      });

      request.onError.listen((event) {
        print('CORS test failed for: $url');
        print('Error: $event');
        completer.complete(false);
      });

      request.send();
      return await completer.future;
    } catch (e) {
      print('CORS test exception for $url: $e');
      return false;
    }
  }

  /// Test multiple Firebase Storage URLs
  static Future<void> testFirebaseStorageCors(List<String> urls) async {
    if (!kIsWeb) {
      print('CORS testing only applies to web platform');
      return;
    }

    print('Testing CORS for ${urls.length} URLs...');

    for (int i = 0; i < urls.length; i++) {
      final url = urls[i];
      print('\n--- Testing URL ${i + 1}/${urls.length} ---');
      print('URL: $url');

      final success = await testCors(url);
      print('Result: ${success ? "✅ PASS" : "❌ FAIL"}');

      // Add delay between requests
      await Future.delayed(Duration(milliseconds: 500));
    }

    print('\n--- CORS Testing Complete ---');
  }

  /// Get CORS info for current domain
  static void printCorsInfo() {
    if (!kIsWeb) {
      print('CORS info only available on web platform');
      return;
    }

    print('=== CORS Debug Info ===');
    print('Current origin: ${html.window.location.origin}');
    print('Current hostname: ${html.window.location.hostname}');
    print('Current port: ${html.window.location.port}');
    print('Current protocol: ${html.window.location.protocol}');
    print('User agent: ${html.window.navigator.userAgent}');
  }
}
