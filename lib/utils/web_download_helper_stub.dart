import 'dart:typed_data';

class WebDownloadHelper {
  /// Download file on web platform (stub for non-web platforms)
  static void downloadFile({
    required Uint8List bytes,
    required String fileName,
    String? mimeType,
  }) {
    // No-op for non-web platforms
  }

  /// Download file from URL on web platform (stub for non-web platforms)
  static void downloadFromUrl({
    required String url,
    required String fileName,
  }) {
    // No-op for non-web platforms
  }

  /// Open URL in new tab (stub for non-web platforms)
  static void openUrl(String url) {
    // No-op for non-web platforms
  }
}
