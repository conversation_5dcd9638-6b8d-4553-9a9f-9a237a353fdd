import 'dart:convert';

import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/app_constants.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocalStorage {
  final storage = const FlutterSecureStorage();
  // Obtain shared preferences.

  Future<SharedPreferences> getInstance() async {
    return SharedPreferences.getInstance();
  }

  Future<String?> getStringValue(String key) async {
    var instance = await getInstance();
    return instance.getString(key);
  }

  Future<bool> setStringValue(String key, String value) async {
    var instance = await getInstance();
    return instance.setString(key, value);
  }

  Future<void> setUserDocId(String docId) async {
    await storage.write(key: AppConstants.userDocID, value: docId);
  }

  Future<String?> getUserId() async {
    return storage.read(key: AppConstants.userDocID);
  }

  Future<bool> isDarkTheme() async {
    var prefs = await getInstance();
    return prefs.getBool(AppConstants.theme) ?? true;
  }

  Future<void> setTheme(bool isDark) async {
    AppColors.isDark = isDark;
    var prefs = await getInstance();
    await prefs.setBool(AppConstants.theme, isDark);
  }

  Future<void> changeLanguage(String locale) async {
    var prefs = await getInstance();
    await prefs.setString(AppConstants.language, locale);
  }

  Future<String> getLanguage() async {
    var prefs = await getInstance();
    var result = prefs.getString(AppConstants.language);
    if (result == null) {
      return "en_US";
    }
    return result;
  }

  Future<void> saveSchoolCode(String schoolCode) async {
    var prefs = await getInstance();
    await prefs.setString(AppConstants.schoolCode, schoolCode.toUpperCase());
  }

  Future<String> getSchoolCode() async {
    var prefs = await getInstance();
    var result = prefs.getString(AppConstants.schoolCode);
    if (result == null) {
      return defaultFirebaseAppName;
    }
    return result;
  }

  Future<List<UserModel>> getUsersLocal() async {
    var prefs = await getInstance();
    var data = prefs.getStringList(AppConstants.userData);
    if (data != null && data.isNotEmpty) {
      var result = data.map((e) {
        var json = jsonDecode(e);
        return UserModel.fromJson(json as Map<String, dynamic>);
      }).toList();
      return result;
    }
    return [];
  }

  Future<void> saveUsersToLocal(List<UserModel> users) async {
    var prefs = await getInstance();
    var data = users.map((e) => jsonEncode(e.toMap())).toList();
    await prefs.setStringList(AppConstants.userData, data);
  }

  Future<void> saveTempUsers(List<UserModel> users, String date) async {
    var prefs = await getInstance();
    var data = users.map((e) => jsonEncode(e.toMap())).toList();
    await prefs.setStringList(AppConstants.tempUsers + date, data);
  }

  Future<List<UserModel>> getTempUsers(String date) async {
    var prefs = await getInstance();
    var data = prefs.getStringList(AppConstants.tempUsers + date);
    if (data != null && data.isNotEmpty) {
      var result = data.map((e) {
        var json = jsonDecode(e);
        return UserModel.fromJson(json as Map<String, dynamic>);
      }).toList();
      return result;
    }
    return [];
  }
}
