import 'package:chat_app/utils/firestore_service.dart';
import 'package:flutter/foundation.dart';

/// Admin utility for managing app versions
/// This is for development/admin use only
class AppVersionAdmin {
  /// Set Android app version
  static Future<void> setAndroidVersion({
    required String version,
    required String buildNumber,
    String? message,
    bool forceUpdate = false,
    String? playStoreUrl,
  }) async {
    try {
      await FirestoreService.setAppVersion(
        platform: 'android',
        version: version,
        buildNumber: buildNumber,
        message: message ??
            'A new version of the app is available with bug fixes and improvements.',
        forceUpdate: forceUpdate,
        downloadUrl: playStoreUrl ??
            'https://play.google.com/store/apps/details?id=com.surena.schoolsmessanger.teacher',
      );
    } catch (e) {
      debugPrint('Error setting Android version: $e');
      // Don't rethrow to prevent system break
    }
  }

  /// Set iOS app version
  static Future<void> setIosVersion({
    required String version,
    required String buildNumber,
    String? message,
    bool forceUpdate = false,
    String? appStoreUrl,
  }) async {
    try {
      await FirestoreService.setAppVersion(
        platform: 'ios',
        version: version,
        buildNumber: buildNumber,
        message: message ??
            'A new version of the app is available with bug fixes and improvements.',
        forceUpdate: forceUpdate,
        downloadUrl: appStoreUrl ??
            'https://apps.apple.com/us/app/schoolmessenger-teacher/id6744751476',
      );
    } catch (e) {
      debugPrint('Error setting iOS version: $e');
      // Don't rethrow to prevent system break
    }
  }

  /// Set web app version
  static Future<void> setWebVersion({
    required String version,
    required String buildNumber,
    String? message,
    bool forceUpdate = false,
  }) async {
    try {
      await FirestoreService.setAppVersion(
        platform: 'web',
        version: version,
        buildNumber: buildNumber,
        message: message ??
            'A new version of the web app is available. Please refresh your browser.',
        forceUpdate: forceUpdate,
      );
    } catch (e) {
      debugPrint('Error setting web version: $e');
      // Don't rethrow to prevent system break
    }
  }

  /// Quick setup for current version (for testing)
  /// Call this once to set up version info in Firestore
  static Future<void> setupCurrentVersions() async {
    try {
      if (kDebugMode) {
        print('Setting up app versions in Firestore...');
      }

      // Set Android version (current version from pubspec.yaml)
      await setAndroidVersion(
        version: '1.0.20',
        buildNumber: '170',
        message: 'New features and bug fixes available!',
        forceUpdate: false,
        playStoreUrl:
            'https://play.google.com/store/apps/details?id=com.surena.schoolsmessanger.teacher',
      );

      // Set iOS version
      await setIosVersion(
        version: '1.0.20',
        buildNumber: '170',
        message: 'New features and bug fixes available!',
        forceUpdate: false,
        appStoreUrl:
            'https://apps.apple.com/us/app/schoolmessenger-teacher/id6744751476',
      );

      // Set Web version
      await setWebVersion(
        version: '1.0.20',
        buildNumber: '170',
        message: 'New features available! Please refresh your browser.',
        forceUpdate: false,
      );

      if (kDebugMode) {
        print('App versions setup complete!');
      }
    } catch (e) {
      debugPrint('Error setting up app versions: $e');
      // Don't rethrow to prevent system break
    }
  }

  /// Trigger a force update for all platforms
  static Future<void> triggerForceUpdate({
    required String version,
    required String buildNumber,
    String? message,
  }) async {
    try {
      final forceMessage =
          message ?? 'Critical update required. Please update immediately.';

      await Future.wait([
        setAndroidVersion(
          version: version,
          buildNumber: buildNumber,
          message: forceMessage,
          forceUpdate: true,
        ),
        setIosVersion(
          version: version,
          buildNumber: buildNumber,
          message: forceMessage,
          forceUpdate: true,
        ),
        setWebVersion(
          version: version,
          buildNumber: buildNumber,
          message: forceMessage,
          forceUpdate: true,
        ),
      ]);

      if (kDebugMode) {
        print(
            'Force update triggered for all platforms: $version+$buildNumber');
      }
    } catch (e) {
      debugPrint('Error triggering force update: $e');
      // Don't rethrow to prevent system break
    }
  }

  /// Example usage for testing
  static Future<void> exampleUsage() async {
    try {
      // Example 1: Set a new version with optional update
      await setAndroidVersion(
        version: '1.0.21',
        buildNumber: '171',
        message: 'New chat features and performance improvements!',
        forceUpdate: false,
        playStoreUrl:
            'https://play.google.com/store/apps/details?id=com.surena.schoolsmessanger.teacher',
      );

      // Example 2: Trigger force update for critical bug fix
      await triggerForceUpdate(
        version: '1.0.22',
        buildNumber: '172',
        message: 'Critical security update. Please update immediately.',
      );
    } catch (e) {
      debugPrint('Error in example usage: $e');
      // Don't rethrow to prevent system break
    }
  }
}

/// Extension to make it easy to call from anywhere
extension AppVersionAdminExtension on FirestoreService {
  /// Quick method to setup app versions
  static Future<void> setupAppVersions() async {
    await AppVersionAdmin.setupCurrentVersions();
  }
}
