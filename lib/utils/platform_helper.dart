import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:open_filex/open_filex.dart';
import 'web_download_helper.dart'
    if (dart.library.io) 'web_download_helper_stub.dart';

class PlatformHelper {
  /// Get application documents directory with web support
  static Future<Directory?> getApplicationDocumentsDirectoryCompat() async {
    if (kIsWeb) {
      // On web, we can't access file system
      return null;
    } else {
      return await getApplicationDocumentsDirectory();
    }
  }

  /// Get downloads directory with web support
  static Future<Directory?> getDownloadsDirectoryCompat() async {
    if (kIsWeb) {
      // On web, we can't access file system
      return null;
    } else {
      return await getDownloadsDirectory();
    }
  }

  /// Get temporary directory with web support
  static Future<Directory?> getTemporaryDirectoryCompat() async {
    if (kIsWeb) {
      // On web, we can't access file system
      return null;
    } else {
      return await getTemporaryDirectory();
    }
  }

  /// Check if file operations are supported on current platform
  static bool get isFileOperationSupported => !kIsWeb;

  /// Get platform-specific file path
  static String? getPlatformFilePath(Directory? directory, String fileName) {
    if (directory == null || kIsWeb) {
      return null;
    }
    return '${directory.path}/$fileName';
  }

  /// Handle file download for different platforms
  static Future<String?> handleFileDownload({
    required String url,
    required String fileName,
    bool useTemporary = false,
  }) async {
    if (kIsWeb) {
      // On web, we can trigger browser download or return URL
      return url; // Return the URL itself for web
    } else {
      // On mobile/desktop, save to file system
      final directory = useTemporary
          ? await getTemporaryDirectoryCompat()
          : await getApplicationDocumentsDirectoryCompat();

      if (directory != null) {
        return getPlatformFilePath(directory, fileName);
      }
      return null;
    }
  }

  /// Open file with platform-specific method
  static Future<void> openFile(String? filePath) async {
    if (filePath == null) return;

    if (kIsWeb) {
      // On web, try to open URL in new tab
      if (isUrl(filePath)) {
        WebDownloadHelper.openUrl(filePath);
      }
    } else {
      // On mobile/desktop, use open_filex
      await OpenFilex.open(filePath);
    }
  }

  /// Check if string is a valid URL
  static bool isUrl(String string) {
    final urlPattern = RegExp(
        r'^(https?|ftp)://'
        r'(?:(?:[A-Z0-9][A-Z0-9_-]*)(?::(?:[A-Z0-9][A-Z0-9_-]*))?@)?'
        r'(?:'
        r'(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+(?:[A-Z]{2,6}\.?|[A-Z0-9-]{2,}\.?)|'
        r'localhost|'
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}'
        r')(?::\d+)?'
        r'(?:/?|[/?]\S+)$',
        caseSensitive: false);

    return urlPattern.hasMatch(string);
  }

  /// Download file from URL with platform-specific method
  static Future<void> downloadFromUrl({
    required String url,
    required String fileName,
  }) async {
    if (kIsWeb) {
      // On web, trigger browser download
      WebDownloadHelper.downloadFromUrl(url: url, fileName: fileName);
    } else {
      // On mobile/desktop, this would require additional implementation
      // For now, just open the file
      await openFile(url);
    }
  }
}
