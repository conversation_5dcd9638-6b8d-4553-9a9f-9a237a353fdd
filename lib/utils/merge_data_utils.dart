import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/models/education/student_class_model.dart';
import 'package:chat_app/utils/firestore_service.dart';

class MergeDataUtils {
  static Future<void> mergeDataStudentClass({Function(String)? onLog}) async {
    List<UserModel> users = [];
    List<StudentClassModel> studentClasses = [];
    users = await FirestoreService.searchUser(
      type: "student",
      fetchAll: true,
    );
    int total = users.length;
    int count = 0;
    for (var user in users) {
      studentClasses.add(StudentClassModel(
        docId: "",
        sid: user.sid ?? "",
        prCourseName: user.prCourseName ?? "",
        prSectionName: user.prSectionName ?? "",
        prAcYear: user.prAcYear ?? "",
        prCGroupName: user.prCGroupName ?? "",
        createdAt: DateTime.now().toIso8601String(),
        studentName: user.name ?? "",
        updatedAt: DateTime.now().toIso8601String(),
      ));
    }
    for (var studentClass in studentClasses) {
      count++;
      await FirestoreService.createOrUpdateStudentClass(studentClass);
      onLog?.call(
          "Done $count/$total\nsid: ${studentClass.sid}, course: ${studentClass.prCourseName}, section: ${studentClass.prSectionName}, year: ${studentClass.prAcYear}, group: ${studentClass.prCGroupName}");
    }
  }
}
