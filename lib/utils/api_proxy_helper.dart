import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class ApiProxyHelper {
  static final Dio _dio = Dio();
  
  // Firebase Functions URL for API proxy
  static const String _proxyBaseUrl = 'https://us-central1-messagebox-ce8a4.cloudfunctions.net/apiProxy';
  
  /// Make API call through proxy for web platform
  static Future<Response> makeApiCall({
    required String url,
    required String method,
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) async {
    if (kIsWeb) {
      // On web, use proxy to avoid CORS issues
      return await _makeProxyCall(
        url: url,
        method: method,
        data: data,
        queryParameters: queryParameters,
        headers: headers,
      );
    } else {
      // On mobile, make direct call
      return await _makeDirectCall(
        url: url,
        method: method,
        data: data,
        queryParameters: queryParameters,
        headers: headers,
      );
    }
  }
  
  /// Make direct API call (for mobile)
  static Future<Response> _makeDirectCall({
    required String url,
    required String method,
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) async {
    final options = Options(
      method: method.toUpperCase(),
      headers: headers,
    );
    
    switch (method.toUpperCase()) {
      case 'GET':
        return await _dio.get(url, queryParameters: queryParameters, options: options);
      case 'POST':
        return await _dio.post(url, data: data, queryParameters: queryParameters, options: options);
      case 'PUT':
        return await _dio.put(url, data: data, queryParameters: queryParameters, options: options);
      case 'DELETE':
        return await _dio.delete(url, data: data, queryParameters: queryParameters, options: options);
      default:
        throw Exception('Unsupported HTTP method: $method');
    }
  }
  
  /// Make API call through Firebase Functions proxy (for web)
  static Future<Response> _makeProxyCall({
    required String url,
    required String method,
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) async {
    // Build target URL with query parameters
    String targetUrl = url;
    if (queryParameters != null && queryParameters.isNotEmpty) {
      final uri = Uri.parse(url);
      final newUri = uri.replace(queryParameters: {
        ...uri.queryParameters,
        ...queryParameters.map((key, value) => MapEntry(key, value.toString())),
      });
      targetUrl = newUri.toString();
    }
    
    // Prepare proxy request
    final proxyData = {
      'url': targetUrl,
      'method': method.toUpperCase(),
      if (data != null) 'body': data,
      if (headers != null) 'headers': headers,
    };
    
    try {
      final response = await _dio.post(
        _proxyBaseUrl,
        data: proxyData,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );
      
      return response;
    } catch (e) {
      print('Proxy API call failed: $e');
      rethrow;
    }
  }
  
  /// Helper method for common POST requests
  static Future<Response> post({
    required String url,
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) async {
    return await makeApiCall(
      url: url,
      method: 'POST',
      data: data,
      queryParameters: queryParameters,
      headers: headers,
    );
  }
  
  /// Helper method for common GET requests
  static Future<Response> get({
    required String url,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) async {
    return await makeApiCall(
      url: url,
      method: 'GET',
      queryParameters: queryParameters,
      headers: headers,
    );
  }
  
  /// Check if URL needs proxy (for external domains)
  static bool needsProxy(String url) {
    if (!kIsWeb) return false;
    
    final uri = Uri.parse(url);
    final externalDomains = [
      'payitseasy.com',
      'feepayindia.in',
      'fcm.googleapis.com',
    ];
    
    return externalDomains.any((domain) => 
      uri.host == domain || uri.host.endsWith('.$domain')
    );
  }
}
