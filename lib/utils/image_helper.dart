import 'package:flutter/foundation.dart';

class ImageHelper {
  /// Get web-compatible image URL
  static String getWebCompatibleImageUrl(String originalUrl) {
    if (!kIsWeb) {
      return originalUrl;
    }
    
    // For web, we can use a CORS proxy or modify the URL
    // Option 1: Use a CORS proxy (for development)
    // return 'https://cors-anywhere.herokuapp.com/$originalUrl';
    
    // Option 2: Add CORS parameters to Firebase Storage URL
    if (originalUrl.contains('firebasestorage.googleapis.com')) {
      // Add CORS parameters to Firebase Storage URL
      final uri = Uri.parse(originalUrl);
      final newUri = uri.replace(
        queryParameters: {
          ...uri.queryParameters,
          'cors': 'true',
        },
      );
      return newUri.toString();
    }
    
    return originalUrl;
  }
  
  /// Check if URL is a Firebase Storage URL
  static bool isFirebaseStorageUrl(String url) {
    return url.contains('firebasestorage.googleapis.com');
  }
  
  /// Get Firebase Storage download URL with token
  static String getFirebaseStorageUrlWithToken(String url) {
    if (!isFirebaseStorageUrl(url)) {
      return url;
    }
    
    // Ensure the URL has proper format for web access
    final uri = Uri.parse(url);
    if (!uri.queryParameters.containsKey('alt')) {
      final newUri = uri.replace(
        queryParameters: {
          ...uri.queryParameters,
          'alt': 'media',
        },
      );
      return newUri.toString();
    }
    
    return url;
  }
}
