import 'dart:async';
import 'dart:convert';
import 'dart:html' as html;
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class CorsWorkaround {
  /// Make API call with CORS workaround for web
  static Future<Response> makeApiCall({
    required String url,
    required String method,
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) async {
    if (kIsWeb) {
      // Try different approaches for web
      return await _makeWebApiCall(
        url: url,
        method: method,
        data: data,
        queryParameters: queryParameters,
        headers: headers,
      );
    } else {
      // Direct call for mobile
      final dio = Dio();
      final options = Options(
        method: method.toUpperCase(),
        headers: headers,
      );

      switch (method.toUpperCase()) {
        case 'GET':
          return await dio.get(url,
              queryParameters: queryParameters, options: options);
        case 'POST':
          return await dio.post(url,
              data: data, queryParameters: queryParameters, options: options);
        default:
          throw Exception('Unsupported method: $method');
      }
    }
  }

  /// Web-specific API call with CORS handling
  static Future<Response> _makeWebApiCall({
    required String url,
    required String method,
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) async {
    try {
      // First, try with Dio (might work if server supports CORS)
      final dio = Dio();

      // Add CORS headers
      final corsHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers':
            'Content-Type, Authorization, X-Requested-With',
        ...?headers,
      };

      final options = Options(
        method: method.toUpperCase(),
        headers: corsHeaders,
      );

      // Build URL with query parameters
      String finalUrl = url;
      if (queryParameters != null && queryParameters.isNotEmpty) {
        final uri = Uri.parse(url);
        final newUri = uri.replace(queryParameters: {
          ...uri.queryParameters,
          ...queryParameters
              .map((key, value) => MapEntry(key, value.toString())),
        });
        finalUrl = newUri.toString();
      }

      switch (method.toUpperCase()) {
        case 'GET':
          return await dio.get(finalUrl, options: options);
        case 'POST':
          return await dio.post(finalUrl, data: data, options: options);
        default:
          throw Exception('Unsupported method: $method');
      }
    } catch (e) {
      print('Direct API call failed: $e');

      // Fallback: Try with HTML HttpRequest
      return await _makeHtmlHttpRequest(
        url: url,
        method: method,
        data: data,
        queryParameters: queryParameters,
        headers: headers,
      );
    }
  }

  /// Fallback using HTML HttpRequest
  static Future<Response> _makeHtmlHttpRequest({
    required String url,
    required String method,
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) async {
    // Build URL with query parameters
    String finalUrl = url;
    if (queryParameters != null && queryParameters.isNotEmpty) {
      final uri = Uri.parse(url);
      final newUri = uri.replace(queryParameters: {
        ...uri.queryParameters,
        ...queryParameters.map((key, value) => MapEntry(key, value.toString())),
      });
      finalUrl = newUri.toString();
    }

    final request = html.HttpRequest();

    // Set up the request
    request.open(method.toUpperCase(), finalUrl);

    // Add headers
    if (headers != null) {
      headers.forEach((key, value) {
        request.setRequestHeader(key, value.toString());
      });
    }

    // Set content type for POST requests
    if (method.toUpperCase() == 'POST' && data != null) {
      request.setRequestHeader('Content-Type', 'application/json');
    }

    // Create a completer to handle the async response
    final completer = Completer<Response>();

    request.onLoad.listen((event) {
      try {
        final responseData = request.responseText;
        final response = Response(
          data: responseData,
          statusCode: request.status,
          statusMessage: request.statusText,
          requestOptions: RequestOptions(path: finalUrl),
        );
        completer.complete(response);
      } catch (e) {
        completer.completeError(e);
      }
    });

    request.onError.listen((event) {
      completer.completeError(
        DioException(
          requestOptions: RequestOptions(path: finalUrl),
          message: 'Network error occurred',
        ),
      );
    });

    // Send the request
    if (method.toUpperCase() == 'POST' && data != null) {
      request.send(jsonEncode(data));
    } else {
      request.send();
    }

    return await completer.future;
  }

  /// Helper for POST requests
  static Future<Response> post({
    required String url,
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) async {
    return await makeApiCall(
      url: url,
      method: 'POST',
      data: data,
      queryParameters: queryParameters,
      headers: headers,
    );
  }

  /// Helper for GET requests
  static Future<Response> get({
    required String url,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) async {
    return await makeApiCall(
      url: url,
      method: 'GET',
      queryParameters: queryParameters,
      headers: headers,
    );
  }
}
