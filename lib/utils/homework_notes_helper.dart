import 'package:chat_app/screens/chat/homework_and_notes/homework_and_notes.dart';

/// Helper class để quản lý HomeworkAndNotes global key và refresh
class HomeworkNotesHelper {
  /// Refresh dữ liệu HomeworkAndNotes nếu widget đang active
  static Future<void> refreshHomeworkAndNotes() async {
    if (homeworkAndNotesGlobalKey.currentState != null) {
      await homeworkAndNotesGlobalKey.currentState!.refreshHomeworkAndNotes();
    }
  }

  /// Kiểm tra xem HomeworkAndNotes widget có đang active không
  static bool isHomeworkAndNotesActive() {
    return homeworkAndNotesGlobalKey.currentState != null;
  }

  /// Gọi refresh với error handling
  static Future<void> safeRefreshHomeworkAndNotes() async {
    try {
      await refreshHomeworkAndNotes();
    } catch (e) {
      print('Error refreshing homework and notes: $e');
    }
  }
}
