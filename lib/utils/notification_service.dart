import 'dart:async';
import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/models/chats/conversatoin_model.dart';
import 'package:chat_app/screens/chat/activity/activity.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_ringtone_player/flutter_ringtone_player.dart';

import 'package:overlay_support/overlay_support.dart';

import '../screens/chat/chat_details/chat_details.dart';
import 'app_colors.dart';
import 'firestore_service.dart';

class NotificationService {
  FirebaseMessaging messaging = FirebaseMessaging.instance;
  final FlutterRingtonePlayer flutterRingtonePlayer = FlutterRingtonePlayer();
  static StreamSubscription? fcmListener;

  static Future<void> showNotification(
      String title, String body, RemoteMessage remoteMessage) async {
    // await FlutterRingtonePlayer.playNotification(
    //   volume: 0.1,
    //   looping: false,
    // );
    showOverlayNotification(
      (context) {
        return Material(
          color: Colors.transparent,
          child: GestureDetector(
            onTap: () {
              _handleMessage(remoteMessage);
            },
            child: SafeArea(
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(8),
                ),
                padding:
                    const EdgeInsets.symmetric(horizontal: 21, vertical: 12),
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: AppStyles.textSize15(
                                fontWeight: FontWeight.w500,
                                color: Colors.white),
                          ),
                          Text(
                            body,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                            style: AppStyles.textSize13(
                              color: Colors.white,
                            ),
                          )
                        ],
                      ),
                    ),
                    if (remoteMessage.notification?.android?.imageUrl != null)
                      CachedNetworkImage(
                        imageUrl:
                            "${remoteMessage.notification?.android?.imageUrl}",
                        width: 40,
                        height: 40,
                        fit: BoxFit.cover,
                      )
                  ],
                ),
              ),
            ),
          ),
        );
      },
      duration: const Duration(milliseconds: 3000),
    );
  }

  Future<void> settingNotifcation(BuildContext context) async {
    NotificationSettings settings = await messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      String? token = await messaging.getToken();
      print("token: $token");
      var userId = await LocalStorage().getUserId();

      await FirestoreService.updateFcmToken(userId: userId!, fcmToken: token!);

      fcmListener =
          FirebaseMessaging.onMessage.asBroadcastStream().listen((message) {
        if (message.notification != null) {
          var type = message.data['type'];
          if (type == 'chat') {
            var currentConversationId = FirestoreService.currentConversationId;

            var roomId = message.data['id'];
            if (roomId != currentConversationId) {
              showNotification(message.notification!.title!,
                  message.notification?.body ?? "", message);
            }
          } else {
            showNotification(message.notification!.title!,
                message.notification?.body ?? "", message);
          }
        }
      });
      await setupInteractedMessage();
    }
  }

  static void selectNotification(String payload) {
    // NavigationService.instance.push(const NotificationPage());
  }

  Future<void> setupInteractedMessage() async {
    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null) {
      _handleMessage(initialMessage);
    }
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessage);
  }

  static void _handleMessage(RemoteMessage message) {
    print("onTap message, $message");
    var type = message.data['type'];
    if (type == 'chat') {
      var map = message.data;
      map['userIds'] = jsonDecode(map['userIds']);
      map['users'] = jsonDecode(map['users']);
      map['mutedByUsers'] = jsonDecode(map['mutedByUsers']);
      var room = ConversationModel.fromJson(message.data, message.data['id']);
      push(
          NavigationService.instance.navigationKey!.currentContext!,
          ChatDetails(
            conversationModel: room,
            converationId: room.id,
          ));
    } else if (type == 'activity') {
      push(NavigationService.instance.navigationKey!.currentContext!,
          Activity());
    }
  }

  dispose() async {
    await fcmListener?.cancel();
  }
}
