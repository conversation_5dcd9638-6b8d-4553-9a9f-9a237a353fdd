import 'dart:convert';
import 'dart:io';

import 'package:chat_app/api/api.dart';
import 'package:chat_app/api/api_url.dart';
import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/models/chats/conversatoin_model.dart';
import 'package:chat_app/models/education/student_class_model.dart';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:collection/collection.dart';
import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:googleapis_auth/googleapis_auth.dart';
import 'package:string_similarity/string_similarity.dart';
import 'package:googleapis_auth/googleapis_auth.dart' as auth;
import 'package:http/http.dart' as http;
import 'package:chat_app/services/app_version_service.dart';

class FirestoreService {
  static String projectName = defaultFirebaseAppName;
  static var instance =
      FirebaseFirestore.instanceFor(app: Firebase.app(projectName));
  static FirebaseAuth auth =
      FirebaseAuth.instanceFor(app: Firebase.app(projectName));
  static const String userCollection = 'users';
  static const String conversationCollection = 'conversations';
  static const String chatCollection = 'chats';
  static const String settingsCollections = 'settings';
  static const String activityCollection = 'activities';
  static const String historyCollection = 'history';
  static const String studentClassCollection = 'student_classes';
  static final Api _api = Api();
  static LocalStorage _localStorage = LocalStorage();
  static String? accessToken;
  // Create a storage reference from our app
  static var storageRef =
      FirebaseStorage.instanceFor(app: Firebase.app(projectName)).ref();
  static String? currentConversationId = null;

  static Future<void> getFirebaseSettings() async {
    var instance = FirebaseFirestore.instanceFor(
        app: Firebase.app(defaultFirebaseAppName));
    var settings = await instance.collection(settingsCollections).get();
    for (var setting in settings.docs) {
      var data = setting.data();
      var apiKey = "apiKey";
      var appId = 'appId';
      // String? logo = data['logo'] ?? "";
      // String? fullName = data["full_name"] ?? "";
      if (!kIsWeb) {
        if (Platform.isIOS) {
          apiKey = "apiKey_ios";
          appId = "appId_ios";
        }
      }

      if (kIsWeb) {
        var jsonWebConfig = data['web_config'];
        if (jsonWebConfig is String) {
          try {
            // Lần đầu decode
            var decoded = jsonDecode(jsonWebConfig);

            // Nếu vẫn là string -> decode tiếp
            if (decoded is String) {
              decoded = jsonDecode(decoded);
            }

            final configMap = decoded;

            await Firebase.initializeApp(
              options: FirebaseOptions(
                apiKey: configMap['apiKey'],
                appId: configMap['appId'],
                messagingSenderId: configMap['messagingSenderId'],
                projectId: configMap['projectId'],
                storageBucket: configMap['storageBucket'],
              ),
              name: data['name'],
            );
          } catch (e) {
            print('Error decoding web config: $e, school ${data['name']}');
          }
        }
      } else {
        await Firebase.initializeApp(
          options: FirebaseOptions(
              apiKey: data[apiKey],
              appId: data[appId],
              messagingSenderId: data['messagingSenderId'],
              projectId: data['projectId'],
              storageBucket: data['storageBucket']),
          name: data['name'],
        );
      }

      var storage = await _localStorage.getInstance();
      await storage.setString(data['name'], data['schoolName'] ?? '');
    }
  }

  static Future<void> updateFirebaseProjectName(String name) async {
    projectName = name.toUpperCase();
    instance = FirebaseFirestore.instanceFor(app: Firebase.app(projectName));
    auth = FirebaseAuth.instanceFor(app: Firebase.app(projectName));
    storageRef =
        FirebaseStorage.instanceFor(app: Firebase.app(projectName)).ref();
    await _localStorage.saveSchoolCode(name);
  }

  static bool checkFirebaseProjectNameExist(String name) {
    return Firebase.apps
        .map((e) => e.name.toUpperCase())
        .contains(name.toUpperCase());
  }

  static Future<String> addUser(Map<String, dynamic> data) async {
    final result = await instance.collection(userCollection).add(data);
    var users = await _localStorage.getUsersLocal();
    data.addAll({"docId": result.id});
    users.add(UserModel.fromJson(data));
    await _localStorage.saveUsersToLocal(users);
    return result.id;
  }

  static Future<String?> checkPhoneNumber(
      {required String phoneNumber, String? type}) async {
    int phone = int.parse(phoneNumber);
    {
      var query = instance
          .collection(userCollection)
          .where("whatsappNumber", isEqualTo: phone);

      if (type != null) {
        query = query.where("type", isEqualTo: type);
      }

      var result = await query.get();

      if (result.docs.isNotEmpty) {
        return result.docs.first.id;
      }
    }
    {
      var query = instance
          .collection(userCollection)
          .where("contact_one", isEqualTo: phone);

      if (type != null) {
        query = query.where("type", isEqualTo: type);
      }

      var result = await query.get();

      if (result.docs.isNotEmpty) {
        return result.docs.first.id;
      }
    }
    {
      var query = instance
          .collection(userCollection)
          .where("contact_two", isEqualTo: phone);

      if (type != null) {
        query = query.where("type", isEqualTo: type);
      }

      var result = await query.get();

      if (result.docs.isNotEmpty) {
        return result.docs.first.id;
      }
    }
    return null;
  }

  static Future<List<String>> findStudentsWithPhoneNumber(
      {required String phoneNumber}) async {
    int phone = int.parse(phoneNumber);
    String type = AppConstants.studentType;
    {
      var result = await instance
          .collection(userCollection)
          .where("whatsappNumber", isEqualTo: phone)
          .where("type", isEqualTo: type)
          .get();

      if (result.docs.isNotEmpty) {
        return result.docs.map((e) => e.id).toList();
      }
    }
    {
      var result = await instance
          .collection(userCollection)
          .where("contact_one", isEqualTo: phone)
          .where("type", isEqualTo: type)
          .get();
      if (result.docs.isNotEmpty) {
        return result.docs.map((e) => e.id).toList();
      }
    }
    {
      var result = await instance
          .collection(userCollection)
          .where("contact_two", isEqualTo: phone)
          .where("type", isEqualTo: type)
          .get();
      if (result.docs.isNotEmpty) {
        return result.docs.map((e) => e.id).toList();
      }
    }
    return [];
  }

  static Future<void> updateParentIdToStudent(
      {required String parentId, required String studentId}) async {
    await instance
        .collection(userCollection)
        .doc(studentId)
        .update({"parentId": parentId});
  }

  static Future<void> addParentPhoneNumberToStudents(
      {required int parentPhoneNumber,
      required String parentId,
      required List<String> students}) async {
    for (var student in students) {
      await instance.collection(userCollection).doc(student).update({
        "parent_phone_number": parentPhoneNumber,
        "parent_id": parentId,
      });
    }
  }

  static Future<List<UserModel>> getStudentsByParentPhoneNumber(
      {required String phoneNumber}) async {
    var result = await instance
        .collection(userCollection)
        .where("parent_phone_number", isEqualTo: phoneNumber)
        .get();
    return result.docs
        .map((e) => UserModel.fromJson(e.data(), docId: e.id))
        .toList();
  }

  static Future<UserModel?> getUserDetails({required String docId}) async {
    var doc = await instance.collection(userCollection).doc(docId).get();
    if (doc.exists) {
      return UserModel.fromJson(doc.data()!).copyWith(docId: docId);
    }
    return null;
  }

  static Future<UserModel?> getUserDetailsBySid({required String sid}) async {
    var doc = await instance
        .collection(userCollection)
        .where("sid", isEqualTo: sid)
        .get();
    if (doc.docs.isNotEmpty) {
      return UserModel.fromJson(doc.docs.first.data(),
          docId: doc.docs.first.id);
    }
    return null;
  }

  // static Future<List<UserModel>> searchUser(
  //     {String? type,
  //     String? name,
  //     String? phoneNumber,
  //     bool fromLocal = true,
  //     String? schoolCode}) async {
  //   if (fromLocal) {
  //     var users = await _localStorage.getUsersLocal();
  //     if (users.isNotEmpty) {
  //       if (type != null) {
  //         return users
  //             .where((element) => element.type == type)
  //             .where((element) => element.activeStatus == 1)
  //             .toList();
  //       }
  //       return users;
  //     }
  //   }

  //   if (name != null) {
  //     var result = await instance
  //         .collection(userCollection)
  //         .where("type", isEqualTo: type)
  //         .where(
  //           "name",
  //           isGreaterThanOrEqualTo: name,
  //         )
  //         // .limit(200)
  //         .get();
  //     return result.docs
  //         .map((e) => UserModel.fromJson(e.data(), docId: e.id))
  //         .where((element) => element.activeStatus == 1)
  //         .toList();
  //   }
  //   var result = await instance
  //       .collection(userCollection)
  //       .where("type", isEqualTo: type)
  //       .where("contact_one", isEqualTo: int.tryParse(phoneNumber ?? ""))
  //       .limit(100)
  //       .where("contact_two",
  //           isLessThanOrEqualTo: int.tryParse(phoneNumber ?? ""))
  //       .where("whatsappNumber",
  //           isLessThanOrEqualTo: int.tryParse(phoneNumber ?? ""))
  //       .limit(200)
  //       .get();
  //   var list = result.docs
  //       .map((e) {
  //         var model = UserModel.fromJson(e.data(), docId: e.id);
  //         if (model.type == AppConstants.parentType) {
  //           return null;
  //         }
  //         return model;
  //       })
  //       .where((element) => element?.activeStatus == 1)
  //       .whereType<UserModel>()
  //       .toList();

  //   var userId = await _localStorage.getUserId();
  //   if (userId != null) {
  //     list.removeWhere((element) => element.docId == userId);
  //   }
  //   await _localStorage.saveUsersToLocal(list);
  //   return list;
  // }
  static Future<List<UserModel>> searchUser({
    String? type,
    String? name,
    String? phoneNumber,
    bool fromLocal = true,
    String? schoolCode,
    bool fetchAll = false,
    bool hideMe = true,
  }) async {
    // Fetch from local storage if applicable
    if (fromLocal) {
      var users = await _localStorage.getUsersLocal();
      if (users.isNotEmpty) {
        if (type != null) {
          // Filter by type and active status
          var list = users
              .where((element) => element.type == type)
              // .where((element) => element.activeStatus == 1)
              .toList();

          return list;
        }

        return users; // Return users directly if no filters are applied
      }
    }

    // Dynamic query building
    var query = instance.collection(userCollection).orderBy("name");

    if (type != null) {
      query = query.where("type", isEqualTo: type);
    } else {
      query = query.where("type",
          whereIn: [AppConstants.studentType, AppConstants.teacherType]);
    }

    if (name != null && name.isNotEmpty) {
      query = query.where(
        "name",
        isGreaterThanOrEqualTo: name.toUpperCase(),
      );
    }

    if (phoneNumber != null) {
      var phoneInt = int.tryParse(phoneNumber);
      if (phoneInt != null) {
        if (phoneInt.toString().length >= 9) {
          query = query.where("contact_one", isEqualTo: phoneInt);
        } else {
          query = query.where("sid", isEqualTo: phoneInt.toString());
        }
      }
    }
    // query = query.where("type", isNotEqualTo: AppConstants.parentType);
    // Execute query with limit
    if (fetchAll == false) {
      query = query.limit(200);
    }
    var result = await query.get();

    // Map results to UserModel and filter by active status
    var list = result.docs
        .map((e) => UserModel.fromJson(e.data(), docId: e.id))
        .where((element) {
      return element.activeStatus == 1;
    }).toList();

    // sort
    if (name?.isNotEmpty ?? false) {
      // Calculate similarity scores for each student name
      list = list.map((user) {
        return user.copyWith(
            similarity: StringSimilarity.compareTwoStrings(
                name!.toLowerCase(), user.name?.toLowerCase()));
      }).toList();
      list.removeWhere((e) => (e.similarity ?? 0) < 0.3);

      // // Sort matches based on similarity in descending order
      list.sort((a, b) => (b.similarity ?? 0.0).compareTo(a.similarity ?? 0));
    }
    // Exclude current user from the list
    if (type == null && name == null && phoneNumber == null && fetchAll) {
      var userId = await _localStorage.getUserId();
      if (userId != null && hideMe) {
        list.removeWhere((element) => element.docId == userId);
      }

      // Save to local storage and return the list
      await _localStorage.saveUsersToLocal(list);
      await _localStorage.setStringValue(AppConstants.updateDataUserDate,
          DateTime.now().toUtc().toIso8601String());
    }
    if (fromLocal == false) {
      try {
        var listStudentClass = await getAllStudentClass();
        var groupData = groupBy(listStudentClass, (p0) => p0.sid);
        for (var key in groupData.keys) {
          var data = groupData[key]?.toList() ?? [];
          data = data
              .where((e) => e.updatedAt != null && e.updatedAt != "")
              .toList();
          data.sort((a, b) => a.updatedAt!.compareTo(b.updatedAt!));
          var lastedClass = data.last;
          var index = list.indexWhere((element) => element.sid == key);
          if (index != -1) {
            list[index] = list[index].copyWith(
              prCGroupName: lastedClass.prCGroupName,
              prCourseName: lastedClass.prCourseName,
              prSectionName: lastedClass.prSectionName,
              prAcYear: lastedClass.prAcYear,
            );
          }
        }
        await _localStorage.saveUsersToLocal(list);
        await _localStorage.setStringValue(AppConstants.updateDataUserDate,
            DateTime.now().toUtc().toIso8601String());
      } catch (e) {
        showToast(e.toString());
      }
    }
    return list;
  }

  static Future<ConversationModel> createConversation({
    required String teacherId,
    required List<UserModel> users,
    required String title,
    required String lastedMessage,
    String? image,
  }) async {
    var dateTime = DateTime.now().toUtc().toIso8601String();
    var user = await getUserDetails(docId: teacherId);
    var result = await instance.collection(conversationCollection).add({
      'updated_at': dateTime,
      'created_at': dateTime,
      "teacher_id": teacherId,
      "users": (users + [user!]).map((e) => e.toMap()).toList(),
      "lasted_message": lastedMessage,
      "title": title,
      "userIds": users.map((e) => e.docId).toList() + [teacherId],
      "teacher_name": user.name ?? "",
      "image": image,
    });
    var model = await result.get();
    return ConversationModel.fromJson(model.data()!, model.id);
  }

  static Future<void> sendMessage({
    required ConversationModel conversationModel,
    required String message,
    required String senderId,
    Map<String, dynamic>? linkButton,
    ChatModel? chatModel,
    required String senderName,
    ChatModel? replyChat,
  }) async {
    var dateTime = DateTime.now().toUtc().toIso8601String();
    if (chatModel != null) {
      await instance
          .collection(conversationCollection)
          .doc(conversationModel.id)
          .collection(chatCollection)
          .add(chatModel.toMap());
    } else {
      await instance
          .collection(conversationCollection)
          .doc(conversationModel.id)
          .collection(chatCollection)
          .add({
        "created_at": dateTime,
        "updated_at": dateTime,
        "message": message,
        "sender_id": senderId,
        "type": "text",
        "linkButton": linkButton,
        "sender_name": senderName,
        "reply": replyChat?.toMap(),
      });
    }

    await instance
        .collection(conversationCollection)
        .doc(conversationModel.id)
        .update({"lasted_message": message, "updated_at": dateTime});

    var userIds = conversationModel.userIds ?? [];
    for (var user in userIds) {
      if (user != senderId) {
        String? userId;
        var userDetails = await getUserDetails(docId: user);
        if (userDetails != null) {
          if (userDetails.type == AppConstants.teacherType) {
            userId = userDetails.docId;
          } else {
            userId = userDetails.parentId;
          }

          if (userId != null &&
              (conversationModel.mutedByUsers?.contains(userId) ?? false) ==
                  false) {
            var model = await getUserDetails(docId: userId);
            if (model?.fcmToken != null) {
              String schoolName = BlocProvider.of<AuthBloc>(getContext())
                      .state
                      .schoolFullName ??
                  "School";

              String notificationTitle = "$schoolName: Message Received";

              await sendNotification(
                  title: notificationTitle,
                  body: message,
                  fcmToken: model!.fcmToken!,
                  conversationModel: conversationModel);
            }
          }
        }
      }
    }
  }

  static Future<void> sendMediaMessage(
      {required ConversationModel conversationModel,
      required String path,
      required String senderId,
      required String type,
      required String? fileThumbnail,
      required String? fileName,
      required String? content,
      ChatModel? replyChat,
      required int? fileSize}) async {
    var dateTime = DateTime.now().toUtc().toIso8601String();
    var profile = getProfile(getContext());
    await instance
        .collection(conversationCollection)
        .doc(conversationModel.id)
        .collection(chatCollection)
        .add({
      "created_at": dateTime,
      "updated_at": dateTime,
      "message": path,
      "sender_id": senderId,
      "type": type,
      "file_thumbnail": fileThumbnail,
      "file_name": fileName,
      "file_size": fileSize,
      "file_content": content,
      "sender_name": profile.name,
      "reply": replyChat?.toMap(),
    });
    await instance
        .collection(conversationCollection)
        .doc(conversationModel.id)
        .update({"lasted_message": path, "updated_at": dateTime});
    var userIds = conversationModel.userIds ?? [];
    for (var user in userIds) {
      if (user != senderId) {
        String? userId;
        var userDetails = await getUserDetails(docId: user);
        if (userDetails != null) {
          if (userDetails.type == AppConstants.teacherType) {
            userId = userDetails.docId;
          } else {
            userId = userDetails.parentId;
          }

          if (userId != null &&
              (conversationModel.mutedByUsers?.contains(userId) ?? false) ==
                  false) {
            var model = await getUserDetails(docId: userId);
            if (model?.fcmToken != null) {
              String bodyMessage = "";
              if (content?.isNotEmpty ?? false) {
                bodyMessage = content ?? "";
              } else {
                bodyMessage = "Message received";
              }
              String schoolName = BlocProvider.of<AuthBloc>(getContext())
                      .state
                      .schoolFullName ??
                  "School";

              String notificationTitle;
              notificationTitle = "$schoolName: Message Received";
              await sendNotification(
                  imageNotification: type == 'image' ? path : null,
                  title: notificationTitle,
                  body: bodyMessage,
                  fcmToken: model!.fcmToken!,
                  conversationModel: conversationModel);
            }
          }
        }
      }
    }
  }

  static Stream<List<ChatModel>> getChatStream(
      {required String conversationId, required String directoryPath}) {
    return instance
        .collection(conversationCollection)
        .doc(conversationId)
        .collection(chatCollection)
        .orderBy("created_at", descending: false)
        .snapshots()
        .map((event) => event.docs.map((e) {
              var chat = ChatModel.fromJson(e.data(), id: e.id);
              var localPath = Helper.chechFileExist(chat, directoryPath);
              return chat.copyWith(
                localPath: localPath,
              );
            }).toList());
  }

  static Stream<List<ConversationModel>> getConversationStream(
      {required String userId, required bool isTeacher}) {
    if (isTeacher && false) {
      return instance
          .collection(conversationCollection)
          .where("teacher_id", isEqualTo: userId)
          .where("userIds", arrayContainsAny: [userId])
          .orderBy("updated_at", descending: true)
          .snapshots()
          .map((event) => event.docs
              .map((e) => ConversationModel.fromJson(e.data(), e.id))
              .toList());
    } else {
      return instance
          .collection(conversationCollection)
          .where("userIds", arrayContainsAny: [userId])
          .orderBy("updated_at", descending: true)
          .snapshots()
          .map((event) => event.docs
              .map((e) => ConversationModel.fromJson(e.data(), e.id))
              .toList());
    }
  }

  static Stream<ConversationModel> getConversationDetailsStream(
      {required String id}) {
    return instance
        .collection(conversationCollection)
        .doc(id)
        .snapshots()
        .map((event) => ConversationModel.fromJson(event.data()!, event.id));
  }

  static Future<String?> checkConversationExist(
      {required List<UserModel> users}) async {
    var userId = await _localStorage.getUserId();
    var result = await instance
        .collection(conversationCollection)
        .where(
          "userIds",
          isEqualTo: users.map((e) => e.docId!).toList() + [userId!],
        )
        .get();
    if (result.docs.isNotEmpty) {
      var model = ConversationModel.fromJson(
          result.docs.first.data(), result.docs.first.id);
      if (model.teacherId == null) {
        return null;
      }
      return model.id;
    }
    return null;
  }

  static Future<UploadTask> uploadFileToFireStorage(File file) async {
    var prefix = file.path.split('.').last;
    String path = "${DateTime.now().millisecondsSinceEpoch}.$prefix";

    Reference storageReference = storageRef.child('medias').child(path);
    UploadTask uploadTask = storageReference.putFile(file);

    return uploadTask;
  }

  static Future<void> deleteMessage(
      {required ChatModel chatModel,
      required bool isLasted,
      required String conversationId}) async {
    await instance
        .collection(conversationCollection)
        .doc(conversationId)
        .collection(chatCollection)
        .doc(chatModel.id)
        .update({
      "deleted_at": DateTime.now().toUtc().toIso8601String(),
    });
    if (isLasted) {
      await instance
          .collection(conversationCollection)
          .doc(conversationId)
          .update({
        "lasted_message": "",
      });
    }
  }

  static Future<void> setMessageAsHomework(
      {required ChatModel chatModel,
      required String conversationId,
      required bool isHomework}) async {
    Map<String, dynamic> updateData = {
      "is_homework": isHomework,
      "updated_at": DateTime.now().toUtc().toIso8601String(),
    };

    if (isHomework) {
      updateData["homework_set_at"] = DateTime.now().toUtc().toIso8601String();
    } else {
      updateData["homework_set_at"] = FieldValue.delete();
    }

    await instance
        .collection(conversationCollection)
        .doc(conversationId)
        .collection(chatCollection)
        .doc(chatModel.id)
        .update(updateData);
    if (isHomework) {
      await sendNotificationForHomeworkAndNotes(
          chatModel: chatModel,
          conversationId: conversationId,
          isHomework: true,
          isNotes: false);
    }
  }

  static Future<void> setMessageAsNotes(
      {required ChatModel chatModel,
      required String conversationId,
      required bool isNotes}) async {
    Map<String, dynamic> updateData = {
      "is_notes": isNotes,
      "updated_at": DateTime.now().toUtc().toIso8601String(),
    };

    if (isNotes) {
      updateData["notes_set_at"] = DateTime.now().toUtc().toIso8601String();
    } else {
      updateData["notes_set_at"] = FieldValue.delete();
    }

    await instance
        .collection(conversationCollection)
        .doc(conversationId)
        .collection(chatCollection)
        .doc(chatModel.id)
        .update(updateData);
    if (isNotes) {
      await sendNotificationForHomeworkAndNotes(
          chatModel: chatModel,
          conversationId: conversationId,
          isHomework: false,
          isNotes: true);
    }
  }

  static Future<void> sendNotificationForHomeworkAndNotes({
    required ChatModel chatModel,
    required String conversationId,
    required bool isHomework,
    required bool isNotes,
  }) async {
    var senderId = chatModel.senderId;

    var conversationModel = await getConversationDetails(conversationId);
    var userIds = conversationModel.userIds ?? [];
    for (var user in userIds) {
      if (user != senderId) {
        String? userId;
        var userDetails = await getUserDetails(docId: user);
        if (userDetails != null) {
          if (userDetails.type == AppConstants.teacherType) {
            userId = userDetails.docId;
          } else {
            userId = userDetails.parentId;
          }

          if (userId != null &&
              (conversationModel.mutedByUsers?.contains(userId) ?? false) ==
                  false) {
            var model = await getUserDetails(docId: userId);
            var content = chatModel.message;
            if (model?.fcmToken != null) {
              String bodyMessage = "";
              if (content?.isNotEmpty ?? false) {
                bodyMessage = content ?? "";
              } else {
                bodyMessage = "Message received";
              }
              String schoolName = BlocProvider.of<AuthBloc>(getContext())
                      .state
                      .schoolFullName ??
                  "School";

              String notificationTitle = "";
              if (isHomework) {
                notificationTitle = "$schoolName: Homework posted";
              } else if (isNotes) {
                notificationTitle = "$schoolName: Notes posted";
              }
              await sendNotification(
                  imageNotification: null,
                  title: notificationTitle,
                  body: bodyMessage,
                  fcmToken: model!.fcmToken!,
                  conversationModel: conversationModel);
            }
          }
        }
      }
    }
  }

  static Future<List<ConversationModel>> getAllUserConversations(
      String userId) async {
    try {
      var querySnapshot = await instance
          .collection(conversationCollection)
          .where('userIds', arrayContains: userId)
          .orderBy('updated_at', descending: true)
          .get();

      List<ConversationModel> conversations = [];
      for (var doc in querySnapshot.docs) {
        var conversation = ConversationModel.fromJson(doc.data(), doc.id);
        conversations.add(conversation);
      }
      return conversations;
    } catch (e) {
      debugPrint('Error getting user conversations: $e');
      return [];
    }
  }

  static Future<List<ChatModel>> getAllMessagesFromConversation(
      String conversationId) async {
    try {
      var querySnapshot = await instance
          .collection(conversationCollection)
          .doc(conversationId)
          .collection(chatCollection)
          .orderBy('created_at', descending: true)
          .get();

      List<ChatModel> messages = [];
      for (var doc in querySnapshot.docs) {
        var message = ChatModel.fromJson(doc.data(), id: doc.id);
        if (message.deletedAt == null) {
          messages.add(message);
        }
      }
      return messages;
    } catch (e) {
      debugPrint('Error getting messages from conversation: $e');
      return [];
    }
  }

  static Future changeRoomName(
      {required String roomId, required String name, String? image}) async {
    await instance
        .collection(conversationCollection)
        .doc(roomId)
        .update({"title": name, "image": image});
  }

  static Future<ConversationModel> getConversationDetails(
      String conversationId) async {
    var result = await instance
        .collection(conversationCollection)
        .doc(conversationId)
        .get();
    return ConversationModel.fromJson(result.data()!, result.id);
  }

  static Future<void> addMembersToConversation(
      {required List<UserModel> members,
      required String conversationId}) async {
    var model = await getConversationDetails(conversationId);
    var users = model.users?.toList() ?? [];
    users = users + members;
    List<String> uniqueList = users.map((e) => e.docId!).toSet().toList();
    List<UserModel> list = [];
    for (var e in uniqueList) {
      var index = users.indexWhere((element) => element.docId == e);
      if (index != -1) {
        list.add(users[index]);
      }
    }
    await instance
        .collection(conversationCollection)
        .doc(conversationId)
        .update({
      "users": list.map((e) => e.toMap()).toList(),
      "userIds": users.map((e) => e.docId).toList()
    });
  }

  static Future<ConversationModel> removePeopleInGroup(
      {required String conversationId, required String userId}) async {
    var model = await getConversationDetails(conversationId);
    var users = model.users?.toList() ?? [];
    var index = users.indexWhere((element) => element.docId == userId);
    if (index != -1) {
      users.removeAt(index);
    }
    await instance
        .collection(conversationCollection)
        .doc(conversationId)
        .update({
      "users": users.map((e) => e.toMap()).toList(),
      "userIds": users.map((e) => e.docId).toList()
    });
    return getConversationDetails(conversationId);
  }

  static Future<void> leaveGroup(
      {required String conversationId, required String userId}) async {
    var model = await getConversationDetails(conversationId);
    var userIds = model.userIds?.toList() ?? [];
    userIds.removeWhere((element) => element == userId);
    var users = model.users ?? [];
    users.removeWhere((element) => element.docId == userId);
    await instance
        .collection(conversationCollection)
        .doc(conversationId)
        .update({
      "userIds": userIds,
      "users": users.map((e) => e.toMap()).toList(),
      "teacher_id": null,
    });
  }

  static Future<void> updateFcmToken(
      {required String userId, required String fcmToken}) async {
    await instance
        .collection(userCollection)
        .doc(userId)
        .update({"fcmToken": fcmToken});
  }

  static Future<AccessCredentials> getAccessToken() async {
    final serviceAccount =
        await rootBundle.loadString('assets/data/service_account.json');
    final serviceJson = jsonDecode(serviceAccount);

    final credentials = ServiceAccountCredentials.fromJson(serviceJson);

    final client = http.Client();
    final accessCredentials = await obtainAccessCredentialsViaServiceAccount(
      credentials,
      ['https://www.googleapis.com/auth/firebase.messaging'],
      client,
    );

    client.close();
    return accessCredentials;
  }

  static Future<void> sendNotification(
      {required String title,
      required String body,
      required String fcmToken,
      required ConversationModel conversationModel,
      String? imageNotification}) async {
    final accessCredentials = await getAccessToken();
    accessToken ??= accessCredentials.accessToken.data;
    var projectId = "messagebox-ce8a4";
    try {
      var response = await _api.request(
        ApiUrl.sendNotification.replaceAll('{projectId}', projectId),
        Method.post,
        headerAddition: {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
        },
        body: {
          "message": {
            "token": fcmToken,
            "notification": {
              "title": title,
              "body": body,
            },
            // "data": conversationModel.toMap()..addAll({"type": "chat"}),
          },
        },
      );
      print(response.data);
    } catch (e) {
      print(e);
    }
  }

  static Future<void> sendNotificationCustomData(
      {required String title,
      required String body,
      required String fcmToken,
      required Map<String, dynamic>? data}) async {
    final accessCredentials = await getAccessToken();
    accessToken ??= accessCredentials.accessToken.data;
    var projectId = "messagebox-ce8a4";
    var response = await _api.request(
      ApiUrl.sendNotification.replaceAll('{projectId}', projectId),
      Method.post,
      body: jsonEncode(
        {
          "message": {
            "token": fcmToken,
            "notification": {
              "title": title,
              "body": body,
              "mutable_content": true,
              "sound": "Tri-tone",
            },
            "data": data,
          },
        },
      ),
      headerAddition: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $accessToken',
      },
    );
    print(response.data);
  }

  static Future<void> markReadAllMessage(
      {required List<ChatModel> messages,
      required String conversationId,
      required ConversationModel model}) async {
    try {
      for (var message in messages) {
        String? dateTime;

        var users = message.readBy ?? [];
        var userId = await _localStorage.getUserId();
        users.add(userId!);
        if (model.users!.length - 1 == users.length) {
          dateTime = DateTime.now().toUtc().toIso8601String();
        }

        await instance
            .collection(conversationCollection)
            .doc(conversationId)
            .collection(chatCollection)
            .doc(message.id)
            .update({"read_by": users, "read_at": dateTime});
      }
    } catch (e) {
      print(e.toString());
    }
  }

  static Future<void> muteNotificationChat(
      {required ConversationModel model, required bool isMute}) async {
    var muted = model.mutedByUsers ?? [];
    var userId = await _localStorage.getUserId();
    if (isMute) {
      if (muted.contains(userId) == false) {
        muted.add(userId!);
      }
    } else {
      muted.removeWhere((element) => element == userId);
    }

    await instance
        .collection(conversationCollection)
        .doc(model.id)
        .update({"mutedByUsers": muted});
  }

  static Future<void> updateProfile(
      {required Map<String, dynamic> data}) async {
    var userId = await _localStorage.getUserId();
    await instance.collection(userCollection).doc(userId).update(data);
  }

  static Future<String> sendOtp(String phoneNumber) async {
    var response = await _api.request(
        "https://payitseasy.com/billerapi.php?action=smsNonReguserNew",
        Method.post,
        body: FormData.fromMap({"mobileNumber": phoneNumber}));
    print(response.data);
    return response.data['otp'].toString();
  }

  static Stream<List<ActivityModel>> getActivityStream(
      {required String senderId}) {
    return instance
        .collection(activityCollection)
        .where("sender_id", isEqualTo: senderId)
        .orderBy("created_at", descending: true)
        .snapshots()
        .map((event) => event.docs
            .map((e) => ActivityModel.fromJson(e.data(), e.id))
            .toList());
  }

  static Stream<List<ActivityModel>> getActivityStreamStudent(
      {required String receiverId}) {
    return instance
        .collection(activityCollection)
        .where("receiver_id", isEqualTo: receiverId)
        .orderBy("created_at", descending: true)
        .snapshots()
        .map((event) => event.docs
            .map((e) => ActivityModel.fromJson(e.data(), e.id))
            .toList());
  }

  static Future<void> markReadAllActivity(String userId) async {
    var list = await instance
        .collection(activityCollection)
        .where("receiver_id", isEqualTo: userId)
        .where("read_at", isEqualTo: null)
        .get();

    for (var element in list.docs) {
      await instance
          .collection(activityCollection)
          .doc(element.id)
          .update({"read_at": DateTime.now().toUtc().toIso8601String()});
    }
  }

  static Future<int> getUnReadActivityCount({required String userId}) async {
    var list = await instance
        .collection(activityCollection)
        .where("receiver_id", isEqualTo: userId)
        .where("read_at", isEqualTo: null)
        .get();
    return list.docs
        .map((e) => ActivityModel.fromJson(e.data(), e.id))
        .where((element) => element.readAt == null)
        .toList()
        .length;
  }

  static Future<void> sendActivity(
      {required String sid,
      required String message,
      required String senderName,
      required String receiverName,
      required String senderId,
      required String receiverId,
      required String titleNotification,
      String? type,
      Map<String, dynamic>? metadata,
      bool sendNotification = true}) async {
    await instance.collection(activityCollection).add(ActivityModel(
          createdAt: DateTime.now().toUtc().toIso8601String(),
          senderId: senderId,
          senderName: senderName,
          receiverId: sid,
          receiverName: receiverName,
          message: message,
          type: type,
          metadata: metadata,
        ).toMap());
    if (sendNotification) {
      var userDetails = await getUserDetails(docId: receiverId);
      if (userDetails?.parentId != null) {
        var model = await getUserDetails(docId: userDetails!.parentId!);
        if (model?.fcmToken?.isNotEmpty ?? false) {
          await sendNotificationCustomData(
              title: titleNotification,
              body: message,
              fcmToken: model!.fcmToken!,
              data: {
                "type": "activity",
              });
        }
      }
    }
  }

  static Future<void> updateUser(
      {required String docId, required Map<String, dynamic> data}) async {
    await instance.collection(userCollection).doc(docId).update(data);
    var users = await _localStorage.getUsersLocal();
    var index = users.indexWhere((element) => element.docId == docId);
    if (index != -1) {
      var user = users[index];
      var map = user.toMap();
      map.addAll(data);
      user = UserModel.fromJson(map);
      users[index] = user;
      await _localStorage.saveUsersToLocal(users);
    }
  }

  static Future<void> deleteUser({required String docId}) async {
    await instance.collection(userCollection).doc(docId).delete();
    var users = await _localStorage.getUsersLocal();
    users.removeWhere((element) => element.docId == docId);
    await _localStorage.saveUsersToLocal(users);
  }

  static Future<void> sendBulkMessages({
    required UserModel profile,
    required List<UserModel> students,
    required String message,
    required DateTime dateTime,
  }) async {
    for (var user in students) {
      var date = Helper.formatDateTimeToString(getContext(), dateTime,
          newPattern: "yyyy-MM-dd");
      message = message
          .replaceAll('[student]', '${user.name}')
          .replaceAll('[date]', date);
      await FirestoreService.sendActivity(
          titleNotification: "key_notifications".tr(),
          receiverId: user.docId!,
          sid: user.sid!,
          message: message,
          senderName: profile.name ?? "",
          receiverName: user.name ?? "",
          senderId: "");
    }
    await FirestoreService.sendActivity(
        sendNotification: false,
        titleNotification: "key_notifications".tr(),
        receiverId: "",
        sid: "",
        message: "key_bulk_messages_sent_successfully".tr(),
        senderName: profile.name ?? "",
        receiverName: "key_notifications".tr(),
        senderId: profile.docId!);
  }

  static Future<void> deleteGroup({required String groupId}) async {
    await instance.collection(conversationCollection).doc(groupId).delete();
  }

  static Future<void> createOrUpdateStudentClass(
      StudentClassModel studentClass) async {
    var record = await instance
        .collection(studentClassCollection)
        .where("sid", isEqualTo: studentClass.sid)
        .get();
    if (record.docs.isNotEmpty) {
      var docId = record.docs.first.id;
      await instance
          .collection(studentClassCollection)
          .doc(docId)
          .update(studentClass.toJson());
    } else {
      await instance
          .collection(studentClassCollection)
          .add(studentClass.toJson());
    }
  }

  static Future<void> createStudentClass(StudentClassModel studentClass) async {
    await instance
        .collection(studentClassCollection)
        .add(studentClass.toJson());
  }

  static Future<List<StudentClassModel>> getAllStudentClass() async {
    var result = await instance.collection(studentClassCollection).get();
    return result.docs.map((e) {
      var map = e.data();
      map['docId'] = e.id;
      return StudentClassModel.fromJson(map);
    }).toList();
  }

  /// Check for app updates and show notification if available
  static Future<void> checkAndShowAppUpdate(BuildContext context) async {
    try {
      final updateInfo = await AppVersionService.checkForUpdate();
      if (updateInfo != null) {
        await AppVersionService.showUpdateDialog(context, updateInfo);
      }
    } catch (e) {
      debugPrint('Error checking for app update: $e');
    }
  }

  /// Set app version info in Firestore (for admin use)
  static Future<void> setAppVersion({
    required String platform, // 'android', 'ios', or 'web'
    required String version,
    required String buildNumber,
    required String message,
    bool forceUpdate = false,
    String? downloadUrl,
  }) async {
    try {
      await instance.collection('app_versions').doc(platform).set({
        'version': version,
        'build_number': buildNumber,
        'message': message,
        'force_update': forceUpdate,
        'download_url': downloadUrl,
        'updated_at': FieldValue.serverTimestamp(),
      });
      debugPrint('App version updated for $platform: $version+$buildNumber');
    } catch (e) {
      debugPrint('Error setting app version: $e');
    }
  }
}
