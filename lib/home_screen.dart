// import 'package:chat_app/widgets/overlay_loading.dart';
// import 'package:firebase_storage/firebase_storage.dart';
// import 'package:flutter/material.dart';
// import 'package:file_picker/file_picker.dart';
// import 'package:open_filex/open_filex.dart';
// import 'package:path_provider/path_provider.dart';
// import 'dart:io';

// class MyHomePage extends StatefulWidget {
//   const MyHomePage({super.key});

//   @override
//   _MyHomePageState createState() => _MyHomePageState();
// }

// class _MyHomePageState extends State<MyHomePage> {
//   final FirebaseStorage _storage = FirebaseStorage.instance;
//   File? _file;
//   bool _isDownloading = false;
//   bool _isLoading = false;

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('Firebase Storage Example'),
//       ),
//       body: OverlayLoading(
//         isLoading: _isLoading || _isDownloading,
//         child: Center(
//           child: Column(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               if (_file != null)
//                 Image.file(
//                   _file!,
//                   height: 150,
//                 ),
//               ElevatedButton(
//                 onPressed: _uploadFile,
//                 child: Text('Upload File'),
//               ),
//               SizedBox(height: 20),
//               ElevatedButton(
//                 onPressed: _isDownloading ? null : _downloadFile,
//                 child: Text('Download File'),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   String? path;
//   void _uploadFile() async {
//     try {
//       setState(() {
//         _isLoading = true;
//       });
//       FilePickerResult? result = await FilePicker.platform.pickFiles();
//       if (result != null) {
//         File file = File(result.files.single.path!);

//         path =
//             DateTime.now().toIso8601String() + "_" + file.path.split('/').last;
//         Reference storageReference =
//             _storage.ref().child('uploads').child(path!);

//         UploadTask uploadTask = storageReference.putFile(file);

//         await uploadTask.whenComplete(() {
//           ScaffoldMessenger.of(context).showSnackBar(
//             SnackBar(content: Text('File uploaded successfully')),
//           );
//         });
//       }
//       setState(() {
//         _isLoading = false;
//       });
//     } catch (e) {
//       setState(() {
//         _isLoading = false;
//       });
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(
//           content: Text(
//             e.toString(),
//           ),
//         ),
//       );
//     }
//   }

//   void _downloadFile() async {
//     if (path != null) {
//       try {
//         setState(() {
//           _isDownloading = true;
//         });

//         final directory = await getApplicationDocumentsDirectory();
//         String fileName = DateTime.now().millisecondsSinceEpoch.toString() +
//             "." +
//             path!.split('.').last;
//         File file = File('${directory.path}/$fileName');

//         Reference storageReference = _storage.ref().child('uploads/$path');
//         await storageReference.writeToFile(file);

//         setState(() {
//           _isDownloading = false;
//         });

//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(content: Text('File downloaded successfully')),
//         );
//         OpenFilex.open(file.path);
//       } catch (e) {
//         setState(() {
//           _isDownloading = false;
//         });
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(
//             content: Text(
//               e.toString(),
//             ),
//           ),
//         );
//       }
//     }
//   }
// }
