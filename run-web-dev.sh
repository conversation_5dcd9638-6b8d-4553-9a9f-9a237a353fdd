#!/bin/bash

echo "🚀 Starting Flutter Web with CORS disabled (Development only)"
echo "⚠️  This is for development only - do not use in production!"

# Kill any existing Chrome processes
pkill -f "Google Chrome"
sleep 2

# Start Flutter web server
flutter run -d web-server --web-port 8080 &
FLUTTER_PID=$!

# Wait for server to start
echo "⏳ Waiting for Flutter web server to start..."
sleep 5

# Open Chrome with disabled security (macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🌐 Opening Chrome with disabled CORS (macOS)"
    open -n -a "Google Chrome" --args --user-data-dir=/tmp/chrome_dev_session --disable-web-security --disable-features=VizDisplayCompositor --allow-running-insecure-content --disable-site-isolation-trials http://localhost:8080
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "🌐 Opening Chrome with disabled CORS (Linux)"
    google-chrome --user-data-dir=/tmp/chrome_dev_session --disable-web-security --disable-features=VizDisplayCompositor --allow-running-insecure-content --disable-site-isolation-trials http://localhost:8080 &
else
    echo "❌ Unsupported OS. Please manually open Chrome with --disable-web-security flag"
    echo "Chrome command: chrome --user-data-dir=/tmp/chrome_dev_session --disable-web-security --disable-features=VizDisplayCompositor http://localhost:8080"
fi

echo "✅ Development server started!"
echo "📝 URL: http://localhost:8080"
echo "⚠️  CORS is disabled for development - APIs should work now"
echo ""
echo "Press Ctrl+C to stop the server"

# Wait for user to stop
wait $FLUTTER_PID
