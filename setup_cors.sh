#!/bin/bash

# Script to setup CORS for Firebase Storage

echo "Setting up CORS for Firebase Storage..."

# Get project ID from Firebase config
PROJECT_ID=$(grep -o '"projectId": "[^"]*' web/index.html | grep -o '[^"]*$' || echo "")

if [ -z "$PROJECT_ID" ]; then
    echo "Could not detect project ID. Please enter your Firebase project ID:"
    read PROJECT_ID
fi

echo "Using project ID: $PROJECT_ID"

# Create CORS configuration
cat > cors.json << EOF
[
  {
    "origin": ["*"],
    "method": ["GET", "HEAD", "OPTIONS"],
    "responseHeader": [
      "Content-Type",
      "Access-Control-Allow-Origin",
      "Access-Control-Allow-Methods",
      "Access-Control-Allow-Headers",
      "Access-Control-Max-Age"
    ],
    "maxAgeSeconds": 3600
  }
]
EOF

echo "CORS configuration created in cors.json"

# Apply CORS configuration
BUCKET_NAME="${PROJECT_ID}.appspot.com"
echo "Applying CORS to bucket: gs://$BUCKET_NAME"

# Check if gsutil is available
if command -v gsutil &> /dev/null; then
    gsutil cors set cors.json gs://$BUCKET_NAME
    echo "CORS configuration applied successfully!"
    
    # Verify CORS configuration
    echo "Current CORS configuration:"
    gsutil cors get gs://$BUCKET_NAME
else
    echo "gsutil not found. Please install Google Cloud SDK:"
    echo "https://cloud.google.com/sdk/docs/install"
    echo ""
    echo "Then run:"
    echo "gcloud auth login"
    echo "gsutil cors set cors.json gs://$BUCKET_NAME"
fi

echo "Done!"
