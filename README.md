# SchoolMessenger Teacher App

A Flutter-based teacher communication application for school management.

## Overview

This is a cross-platform Flutter application that provides teachers with tools to:
- Send messages to students and parents
- Manage student attendance
- Handle exam management
- Promote students
- Manage users

The app supports both mobile (Android/iOS) and web platforms.

## Prerequisites

Before building and deploying, ensure you have:

- Flutter SDK (>=3.1.0 <4.0.0)
- Firebase CLI installed
- Node.js (for Firebase functions)
- A Firebase project set up

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd message_box
```

2. Install dependencies:
```bash
flutter pub get
```

3. Configure Firebase:
```bash
firebase login
flutterfire configure
```

## Building for Web

### Development Build

For development and testing:

```bash
flutter build web --debug
```

### Production Build

For production deployment:

```bash
flutter build web --release --web-renderer html
flutter build web && firebase deploy --only hosting
```

**Note**: We use `--web-renderer html` for better compatibility across different browsers and devices.

### Build with Custom Configuration

If you need to specify a different Firebase project:

```bash
flutter build web --release --web-renderer html --dart-define=FIREBASE_PROJECT=your-project-id
```

## Firebase Deployment

### Prerequisites

1. Install Firebase CLI:
```bash
npm install -g firebase-tools
```

2. Login to Firebase:
```bash
firebase login
```

### Deployment Steps

1. **Build the web app** (if not already done):
```bash
flutter build web --release --web-renderer html
```

2. **Deploy to Firebase Hosting**:

For default project (messagebox-ce8a4):
```bash
firebase deploy --only hosting
```

For production project (vpsudp-61c02):
```bash
firebase deploy --only hosting --project default
```

3. **Deploy with functions** (if needed):
```bash
firebase deploy
```

### Project Configuration

The app is configured with multiple Firebase projects:

- **Default**: `messagebox-ce8a4` (development/staging)
- **Production**: `vpsudp-61c02` (production)

### Current Deployment

The app is currently deployed to:
- **Site**: `surena-schoolsmessenger-teacher`
- **URL**: https://surena-schoolsmessenger-teacher.web.app

You can switch between projects using:
```bash
firebase use default  # for messagebox-ce8a4
firebase use prod     # for vpsudp-61c02
```

### Hosting Configuration

The web app is configured to:
- Serve from `build/web` directory
- Deploy to specific site: `surena-schoolsmessenger-teacher`
- Handle CORS headers for API requests
- Use single-page application routing
- Support PWA features

### Quick Deployment

To deploy the latest changes:

```bash
# Build the app
flutter build web

# Deploy to Firebase
firebase deploy --only hosting
```

The app will be automatically deployed to: https://surena-schoolsmessenger-teacher.web.app

## Environment Variables

The app uses the following configuration:

- **Firebase Project ID**: Set via `.firebaserc`
- **Web Title**: "SchoolMessenger Teacher"
- **Storage Bucket**: `gs://vpsudp-61c02.appspot.com`

## Platform-Specific Features

### Web Platform
- Opens external links in new tabs
- Disabled voice recording functionality
- Uses browser-native file handling

### Mobile Platform
- In-app WebView for external links
- Voice recording enabled
- Native file picker integration

## Deployment URLs

After successful deployment, your app will be available at:

- **Current Live Site**: https://surena-schoolsmessenger-teacher.web.app
- **Development**: `https://messagebox-ce8a4.web.app`
- **Production**: `https://vpsudp-61c02.web.app`

## Troubleshooting

### Common Build Issues

1. **Web build fails**: Ensure all dependencies support web platform
2. **Firebase deployment fails**: Check Firebase CLI authentication
3. **CORS issues**: Verify hosting headers in `firebase.json`

### Performance Optimization

For better web performance:
- Use `--web-renderer html` for compatibility
- Enable web-specific optimizations in `flutter build web`
- Minimize asset sizes

## Development Resources

- [Flutter Web Documentation](https://docs.flutter.dev/platform-integration/web)
- [Firebase Hosting Guide](https://firebase.google.com/docs/hosting)
- [Flutter Firebase Integration](https://firebase.flutter.dev/)
