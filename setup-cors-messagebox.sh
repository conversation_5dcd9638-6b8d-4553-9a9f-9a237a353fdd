#!/bin/bash

echo "🚀 Setting up CORS for Firebase Storage project: messagebox-ce8a4"

# Set project
echo "📋 Setting Google Cloud project..."
gcloud config set project messagebox-ce8a4

# Check if bucket exists
echo "🔍 Checking bucket..."
gsutil ls gs://messagebox-ce8a4.appspot.com

# Apply CORS
echo "🌐 Applying CORS configuration..."
gsutil cors set cors-messagebox.json gs://messagebox-ce8a4.appspot.com

# Add public read access
echo "🔓 Setting public read access..."
gsutil iam ch allUsers:objectViewer gs://messagebox-ce8a4.appspot.com

# Verify CORS
echo "✅ Verifying CORS configuration:"
gsutil cors get gs://messagebox-ce8a4.appspot.com

# Verify IAM
echo "✅ Verifying IAM permissions:"
gsutil iam get gs://messagebox-ce8a4.appspot.com

echo "🎉 Done! Please restart your web app and test image loading."
echo "📝 Test URL example:"
echo "https://firebasestorage.googleapis.com/v0/b/messagebox-ce8a4.appspot.com/o/medias%2Fexample.jpg?alt=media&token=..."
