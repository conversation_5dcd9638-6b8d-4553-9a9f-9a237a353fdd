# PHP CORS Configuration Guide

## 🎯 **Quick Fix - Add to Top of PHP Files**

Add this code to the **top** of your PHP API files (before any output):

```php
<?php
// CORS Headers for SchoolMessenger Web App
header("Access-Control-Allow-Origin: https://messagebox-ce8a4.web.app");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header("Access-Control-Max-Age: 86400"); // 24 hours

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Your existing PHP code continues here...
?>
```

---

## 📁 **Files to Update:**

### 1. **PayItEasy APIs:**
```
/path/to/billerapi.php
/path/to/feepay/dataapi.php
```

### 2. **School APIs:**
```
/path/to/opay/schoolMessanger.php
/path/to/opay/process.php
/path/to/Exams/manage_exam.php
```

---

## 🔧 **Method 1: Individual File Headers**

Add to each API file:

```php
<?php
// CORS Configuration
header("Access-Control-Allow-Origin: https://messagebox-ce8a4.web.app");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// Rest of your code...
?>
```

---

## 🌐 **Method 2: .htaccess Configuration**

Create or update `.htaccess` file in your web root:

```apache
# CORS Headers for SchoolMessenger Web App
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "https://messagebox-ce8a4.web.app"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
    Header always set Access-Control-Max-Age "86400"
</IfModule>

# Handle preflight OPTIONS requests
RewriteEngine On
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]
```

---

## 🔄 **Method 3: Common Include File**

Create `cors.php`:

```php
<?php
// cors.php - Include this at the top of all API files

function setCorsHeaders() {
    // Allow specific origin
    header("Access-Control-Allow-Origin: https://messagebox-ce8a4.web.app");
    
    // Allow specific methods
    header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    
    // Allow specific headers
    header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
    
    // Cache preflight response for 24 hours
    header("Access-Control-Max-Age: 86400");
    
    // Handle preflight OPTIONS request
    if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
        http_response_code(200);
        exit();
    }
}

// Call the function
setCorsHeaders();
?>
```

Then include in each API file:
```php
<?php
require_once 'cors.php';
// Your existing code...
?>
```

---

## 🛡️ **Method 4: Multiple Domains (if needed)**

If you need to allow multiple domains:

```php
<?php
$allowed_origins = [
    'https://messagebox-ce8a4.web.app',
    'http://localhost:8080', // for development
    'https://your-other-domain.com'
];

$origin = $_SERVER['HTTP_ORIGIN'] ?? '';

if (in_array($origin, $allowed_origins)) {
    header("Access-Control-Allow-Origin: $origin");
}

header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}
?>
```

---

## ✅ **Testing the Configuration**

After implementing, test with:

```bash
curl -H "Origin: https://messagebox-ce8a4.web.app" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     https://your-api-endpoint.com/api.php
```

Should return:
```
Access-Control-Allow-Origin: https://messagebox-ce8a4.web.app
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
```

---

## 🚨 **Important Notes:**

1. **Add headers BEFORE any output** (echo, print, HTML, etc.)
2. **Handle OPTIONS requests** - browsers send these for preflight
3. **Test after implementation** - we can verify from web app
4. **Backup files** before making changes

---

## 📞 **Need Help?**

If you encounter any issues:
1. Check server error logs
2. Verify headers are being sent (use browser dev tools)
3. Contact us for verification testing

The web app will work immediately after these headers are added! 🚀
