#!/bin/bash

echo "🚀 Setting up CORS for Firebase Storage project: vpsudp-61c02"

# Set project
echo "📋 Setting Google Cloud project..."
gcloud config set project vpsudp-61c02

# Check if bucket exists
echo "🔍 Checking bucket..."
gsutil ls gs://vpsudp-61c02.appspot.com

# Apply CORS
echo "🌐 Applying CORS configuration..."
gsutil cors set cors-vpsudp.json gs://vpsudp-61c02.appspot.com

# Add public read access
echo "🔓 Setting public read access..."
gsutil iam ch allUsers:objectViewer gs://vpsudp-61c02.appspot.com

# Verify CORS
echo "✅ Verifying CORS configuration:"
gsutil cors get gs://vpsudp-61c02.appspot.com

# Verify IAM
echo "✅ Verifying IAM permissions:"
gsutil iam get gs://vpsudp-61c02.appspot.com

echo "🎉 Done! Please restart your web app and test image loading."
echo "📝 Test URL example:"
echo "https://firebasestorage.googleapis.com/v0/b/vpsudp-61c02.appspot.com/o/medias%2F1748328429140.jpg?alt=media&token=..."
