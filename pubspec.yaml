name: chat_app
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev
platforms:
  android:
  ios:
  web:
# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more   iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.25+184

environment:
  sdk: ">=3.1.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.

dependencies:
  flutter:
    sdk: flutter

  flutter_secure_storage: ^9.2.4
  fluttertoast: ^8.2.2
  cloud_firestore: any
  cupertino_icons: ^1.0.2
  easy_localization: ^3.0.2
  file_picker: ^10.1.2
  path_provider: any
  firebase_core: ^3.13.0
  firebase_storage: ^12.4.5
  open_filex: ^4.3.4
  firebase_ui_auth: ^1.6.2
  flutter_svg: ^2.0.5
  cached_network_image: ^3.2.3
  url_launcher: ^6.1.11
  pin_code_fields: ^8.0.1
  flutter_bloc: ^9.1.0
  equatable: ^2.0.5
  rxdart: ^0.28.0
  firebase_auth: 5.3.4
  mime_type: ^1.0.0
  video_thumbnail: ^0.5.3
  shared_preferences: ^2.2.1
  # appinio_video_player: ^1.2.2
  video_player: ^2.8.1
  chewie: ^1.8.5
  image_picker: ^1.0.4
  dio: ^5.3.3
  firebase_messaging: ^15.2.5
  overlay_support: ^2.1.0
  flutter_ringtone_player: ^4.0.0+2
  extended_image: ^9.1.0
  highlight_text: ^1.7.0
  scrollable_positioned_list: ^0.3.8
  icons_launcher: ^3.0.1
  share_plus: ^11.0.0
  firebase_crashlytics: ^4.3.5
  # quill_html_editor:
  #   git:
  #     url: https://github.com/mostafijur566/quill_html_editor
  #     ref: main
  flutter_widget_from_html: any
  flutter_cache_manager: ^3.3.1
  # pdf_thumbnail: any
  flutter_link_previewer: ^3.2.2
  any_link_preview: ^3.0.1
  insta_image_viewer: ^1.0.2
  collection: ^1.17.2
  calendar_timeline: ^1.1.3
  flutter_typeahead: ^5.0.1
  pdf: ^3.10.7
  pdfx:
    git:
      url: https://github.com/SamuelGadiel/packages.flutter.git
      path: packages/pdfx
  flutter_image_compress: ^2.1.0
  # easy_upi_payment: ^0.1.6
  upi_pay: ^1.1.0
  flutter_launcher_icons: ^0.14.3
  toastification: ^3.0.2
  flutter_cached_pdfview: ^0.4.2
  table_calendar: ^3.0.9
  webview_flutter: ^4.11.0
  string_similarity: ^2.1.1
  package_info_plus: ^8.1.2
  waveform_recorder: ^1.6.3
  audio_waveforms: ^1.3.0
  googleapis_auth: ^2.0.0
  excel:
    path: custom_packages/excel-main
  upgrader: ^11.4.0

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/app_logo_teacher.png"
  min_sdk_android: 21 # android min sdk min:16, default 21

dev_dependencies:
  flutter_test:
    sdk: flutter
  change_app_package_name: ^1.5.0
dependency_overrides:
  archive: ^4.0.7
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  assets:
    - assets/translations/
    - assets/images/icons/
    - assets/images/
    - assets/data/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

icons_launcher:
  image_path: "assets/images/app_logo.png"
  platforms:
    android:
      enable: true
    ios:
      enable: true

flutter_assets:
  assets_path: assets/
  output_path: lib/utils/
  filename: assets.dart
