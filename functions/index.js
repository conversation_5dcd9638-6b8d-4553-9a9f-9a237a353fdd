const functions = require('firebase-functions');
const cors = require('cors')({ origin: true });
const fetch = require('node-fetch');

exports.imageProxy = functions.https.onRequest((req, res) => {
  return cors(req, res, async () => {
    try {
      const imageUrl = req.query.url;

      if (!imageUrl) {
        return res.status(400).json({ error: 'URL parameter is required' });
      }

      // Fetch the image
      const response = await fetch(imageUrl);

      if (!response.ok) {
        return res.status(response.status).json({ error: 'Failed to fetch image' });
      }

      // Set CORS headers
      res.set('Access-Control-Allow-Origin', '*');
      res.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
      res.set('Access-Control-Allow-Headers', 'Content-Type');

      // Set content type
      const contentType = response.headers.get('content-type');
      if (contentType) {
        res.set('Content-Type', contentType);
      }

      // Pipe the image
      response.body.pipe(res);

    } catch (error) {
      console.error('Image proxy error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  });
});

// API Proxy for external APIs (payitseasy.com, feepayindia.in, etc.)
exports.apiProxy = functions.https.onRequest((req, res) => {
  return cors(req, res, async () => {
    try {
      const targetUrl = req.query.url || req.body.url;

      if (!targetUrl) {
        return res.status(400).json({ error: 'URL parameter is required' });
      }

      // Validate allowed domains for security
      const allowedDomains = [
        'payitseasy.com',
        'feepayindia.in',
        'fcm.googleapis.com'
      ];

      const urlObj = new URL(targetUrl);
      const isAllowed = allowedDomains.some(domain =>
        urlObj.hostname === domain || urlObj.hostname.endsWith('.' + domain)
      );

      if (!isAllowed) {
        return res.status(403).json({ error: 'Domain not allowed' });
      }

      // Prepare request options
      const options = {
        method: req.method,
        headers: {
          'Content-Type': req.headers['content-type'] || 'application/json',
          'User-Agent': 'SchoolMessenger-Proxy/1.0'
        }
      };

      // Add body for POST/PUT requests
      if (req.method === 'POST' || req.method === 'PUT') {
        if (req.body) {
          if (typeof req.body === 'string') {
            options.body = req.body;
          } else {
            options.body = JSON.stringify(req.body);
          }
        }
      }

      // Make the request
      const response = await fetch(targetUrl, options);
      const data = await response.text();

      // Set CORS headers
      res.set('Access-Control-Allow-Origin', '*');
      res.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');

      // Set content type from original response
      const contentType = response.headers.get('content-type');
      if (contentType) {
        res.set('Content-Type', contentType);
      }

      // Return response
      res.status(response.status).send(data);

    } catch (error) {
      console.error('API proxy error:', error);
      res.status(500).json({ error: 'Internal server error', details: error.message });
    }
  });
});
