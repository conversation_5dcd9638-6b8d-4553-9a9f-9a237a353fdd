# .github/workflows/publish.yml
name: Publish package to pub.dev

on:
  push:
    branches: [ publish ]

jobs:
  build:
    runs-on: ubuntu-latest
    container:
      image: google/dart:latest
    steps:
      - uses: actions/checkout@v1
      - name: Setup credentials
        run: |
          mkdir -p ~/.pub-cache 
          cat <<EOF > ~/.pub-cache/credentials.json
          {
            "accessToken":"${{ secrets.OAUTH_ACCESS_TOKEN }}",
            "refreshToken":"${{ secrets.OAUTH_REFRESH_TOKEN }}",
            "tokenEndpoint":"https://accounts.google.com/o/oauth2/token",
            "scopes": [ "openid", "https://www.googleapis.com/auth/userinfo.email" ],
            "expiration": *************
          }
          EOF
      - name: Publish package
        run: pub publish -f
