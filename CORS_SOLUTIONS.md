# 🛠️ CORS Solutions for Image Loading Issues

## 📋 **Available CORS Solutions in Project**

<PERSON><PERSON><PERSON> bạn gặp CORS image issues, project đã có sẵn nhiều giải pháp:

---

## 🚀 **1. Firebase Storage CORS Setup**

### **Automatic Scripts Available:**

#### **For messagebox-ce8a4 project:**
```bash
# Run this script to setup CORS for current project
./setup-cors-messagebox.sh
```

#### **For vpsudp-61c02 project:**
```bash
# Run this script for production project
./setup-cors-vpsudp.sh
```

#### **Generic setup:**
```bash
# Universal CORS setup script
./setup_cors.sh
```

### **Manual CORS Configuration:**
```bash
# Set project
gcloud config set project messagebox-ce8a4

# Apply CORS configuration
gsutil cors set cors-messagebox.json gs://messagebox-ce8a4.appspot.com

# Verify CORS
gsutil cors get gs://messagebox-ce8a4.appspot.com
```

---

## 🔧 **2. Web-Compatible Image Components**

### **WebCompatibleImage Widget:**
```dart
// Use this instead of regular Image widgets
WebCompatibleImage(
  imageUrl: "your-image-url",
  width: 200,
  height: 200,
  fit: BoxFit.cover,
)
```

### **WebCompatibleCircleAvatar:**
```dart
// For profile pictures
WebCompatibleCircleAvatar(
  imageUrl: "avatar-url",
  radius: 30,
)
```

**Location**: `lib/widgets/web_compatible_image.dart`

---

## 🌐 **3. Image Helper Utilities**

### **ImageHelper Class:**
```dart
// Get web-compatible URL
String webUrl = ImageHelper.getWebCompatibleImageUrl(originalUrl);

// Check if Firebase Storage URL
bool isFirebase = ImageHelper.isFirebaseStorageUrl(url);

// Get proper download URL
String downloadUrl = ImageHelper.getFirebaseStorageUrlWithToken(url);
```

**Location**: `lib/utils/image_helper.dart`

---

## 🔄 **4. Firebase Functions Image Proxy**

### **Image Proxy Function:**
```javascript
// Available at: functions/index.js
exports.imageProxy = functions.https.onRequest((req, res) => {
  // Handles CORS for external images
});
```

### **Usage:**
```dart
// Use proxy for external images
String proxyUrl = "https://your-project.cloudfunctions.net/imageProxy?url=$imageUrl";
```

---

## 📝 **5. CORS Configuration Files**

### **Available Config Files:**
- `cors.json` - Generic CORS config
- `cors-messagebox.json` - For messagebox-ce8a4 project
- `cors-vpsudp.json` - For vpsudp-61c02 project

### **Firebase.json CORS Headers:**
```json
{
  "hosting": {
    "headers": [
      {
        "source": "**",
        "headers": [
          {
            "key": "Access-Control-Allow-Origin",
            "value": "*"
          },
          {
            "key": "Access-Control-Allow-Methods", 
            "value": "GET, POST, PUT, DELETE, OPTIONS"
          },
          {
            "key": "Access-Control-Allow-Headers",
            "value": "Content-Type, Authorization, X-Requested-With"
          }
        ]
      }
    ]
  }
}
```

---

## 🔧 **6. API CORS Workaround**

### **CorsWorkaround Class:**
```dart
// For API calls with CORS issues
Response response = await CorsWorkaround.makeApiCall(
  url: "api-endpoint",
  method: "GET",
  headers: headers,
);
```

**Location**: `lib/utils/cors_workaround.dart`

---

## 🐘 **7. PHP Server CORS Configuration**

### **Quick PHP Fix:**
```php
<?php
// Add to top of PHP files
header("Access-Control-Allow-Origin: https://surena-schoolsmessenger-teacher.web.app");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");

// Handle preflight
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}
?>
```

**Full Guide**: `PHP_CORS_GUIDE.md`

---

## 🚨 **Quick Troubleshooting**

### **If Images Not Loading:**

#### **1. Check Firebase Storage CORS:**
```bash
# Verify current CORS settings
gsutil cors get gs://messagebox-ce8a4.appspot.com
```

#### **2. Apply CORS Configuration:**
```bash
# Quick fix
./setup-cors-messagebox.sh
```

#### **3. Use Web-Compatible Components:**
```dart
// Replace regular Image.network with:
WebCompatibleImage(imageUrl: url)
```

#### **4. Check Console Errors:**
- Open browser DevTools
- Look for CORS errors in Console
- Check Network tab for failed requests

---

## 📊 **Current Domain Configuration**

### **New Domain**: `https://surena-schoolsmessenger-teacher.web.app`

### **Update CORS for New Domain:**

#### **Firebase Storage:**
```bash
# Update CORS config for new domain
gsutil cors set cors-messagebox.json gs://messagebox-ce8a4.appspot.com
```

#### **PHP APIs:**
```php
// Update PHP headers
header("Access-Control-Allow-Origin: https://surena-schoolsmessenger-teacher.web.app");
```

#### **External APIs:**
Contact API providers to whitelist new domain.

---

## 🎯 **Recommended Solution Order**

### **For Firebase Storage Images:**
1. ✅ **Run CORS setup script**: `./setup-cors-messagebox.sh`
2. ✅ **Use WebCompatibleImage**: Replace Image.network
3. ✅ **Verify in browser**: Check DevTools for errors

### **For External Images:**
1. ✅ **Use Image Proxy**: Firebase Functions proxy
2. ✅ **Contact API providers**: Request CORS whitelist
3. ✅ **Use CorsWorkaround**: For API calls

### **For PHP APIs:**
1. ✅ **Add CORS headers**: To PHP files
2. ✅ **Handle OPTIONS**: Preflight requests
3. ✅ **Test endpoints**: Verify CORS working

---

## 📞 **Support Resources**

### **Documentation:**
- `PHP_CORS_GUIDE.md` - PHP server configuration
- `CORS_REQUEST_EMAIL.md` - Email template for API providers
- `lib/widgets/web_compatible_image.dart` - Web image components

### **Scripts:**
- `setup-cors-messagebox.sh` - Firebase Storage CORS
- `setup_cors.sh` - Generic CORS setup

### **Utilities:**
- `lib/utils/image_helper.dart` - Image URL helpers
- `lib/utils/cors_workaround.dart` - API CORS solutions

---

## 🎉 **Quick Fix Commands**

```bash
# 1. Setup Firebase Storage CORS
./setup-cors-messagebox.sh

# 2. Verify CORS applied
gsutil cors get gs://messagebox-ce8a4.appspot.com

# 3. Test image loading
flutter run -d chrome

# 4. Check browser console for errors
# Open DevTools → Console tab
```

**Most image CORS issues should be resolved with the available solutions!** 🚀
