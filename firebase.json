{"flutter": {"platforms": {"android": {"default": {"projectId": "messagebox-ce8a4", "appId": "1:1047871199588:android:9fff1d451bb11056e2c271", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "messagebox-ce8a4", "appId": "1:1047871199588:ios:02265030caf2485ce2c271", "uploadDebugSymbols": true, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "messagebox-ce8a4", "configurations": {"android": "1:1047871199588:android:9fff1d451bb11056e2c271", "ios": "1:1047871199588:ios:02265030caf2485ce2c271", "web": "1:1047871199588:web:4e2bab7e0d25e964e2c271", "windows": "1:1047871199588:web:f48babf3466b688ae2c271"}}}}}, "hosting": {"site": "<PERSON><PERSON>-schoolsmessenger-teacher", "public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "headers": [{"source": "**", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Requested-With"}]}], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "functions": [{"source": "functions", "codebase": "default"}, {"source": "api-proxy", "codebase": "api-proxy", "ignore": ["venv", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"]}]}